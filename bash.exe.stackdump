Stack trace:
Frame         Function      Args
0007FFFFABF0  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF9AF0) msys-2.0.dll+0x2118E
0007FFFFABF0  0002100469BA (000000000000, 000000000000, 000000000000, 0007FFFFAEC8) msys-2.0.dll+0x69BA
0007FFFFABF0  0002100469F2 (00021028DF99, 0007FFFFAAA8, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFFABF0  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFABF0  00021006A545 (0007FFFFAC00, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0007FFFFAED0  00021006B9A5 (0007FFFFAC00, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFC3CBC0000 ntdll.dll
7FFC3BAD0000 KERNEL32.DLL
7FFC3A570000 KERNELBASE.dll
7FFC3BC80000 USER32.dll
7FFC3A310000 win32u.dll
7FFC3AC50000 GDI32.dll
7FFC3A1D0000 gdi32full.dll
7FFC3A340000 msvcp_win.dll
7FFC39FE0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFC3C600000 advapi32.dll
7FFC3BBD0000 msvcrt.dll
7FFC3CAD0000 sechost.dll
7FFC3C7E0000 RPCRT4.dll
7FFC39430000 CRYPTBASE.DLL
7FFC3A130000 bcryptPrimitives.dll
7FFC3AC80000 IMM32.DLL
