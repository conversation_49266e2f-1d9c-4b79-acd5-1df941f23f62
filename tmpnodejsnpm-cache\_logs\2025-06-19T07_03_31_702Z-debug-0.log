0 verbose cli D:\Program Files\nodejs\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\npm\bin\npm-cli.js
1 info using npm@10.9.0
2 info using node@v20.11.1
3 silly config load:file:C:\Users\<USER>\AppData\Roaming\npm\node_modules\npm\npmrc
4 silly config load:file:C:\laragon\www\PolyFlix\.npmrc
5 silly config load:file:C:\Users\<USER>\.npmrc
6 silly config load:file:C:\Users\<USER>\AppData\Roaming\npm\etc\npmrc
7 verbose title npm install jquery
8 verbose argv "install" "jquery"
9 verbose logfile logs-max:10 dir:C:\laragon\www\PolyFlix\tmpnodejsnpm-cache\_logs\2025-06-19T07_03_31_702Z-
10 verbose logfile C:\laragon\www\PolyFlix\tmpnodejsnpm-cache\_logs\2025-06-19T07_03_31_702Z-debug-0.log
11 silly logfile done cleaning log files
12 silly packumentCache heap:2197815296 maxSize:549453824 maxEntrySize:274726912
13 silly idealTree buildDeps
14 silly fetch manifest jquery@^3.7.1
15 silly packumentCache full:https://registry.npmjs.org/jquery cache-miss
16 verbose shrinkwrap failed to load node_modules/.package-lock.json out of date, updated: node_modules
17 http fetch GET 200 https://registry.npmjs.org/jquery 289ms (cache miss)
18 silly packumentCache full:https://registry.npmjs.org/jquery set size:undefined disposed:false
19 silly placeDep ROOT jquery@3.7.1 REPLACE for:  want: ^3.7.1
20 silly reify moves {}
21 verbose reify failed optional dependency C:\laragon\www\PolyFlix\node_modules\@tailwindcss\oxide-wasm32-wasi
22 silly reify mark deleted [
22 silly reify   'C:\\laragon\\www\\PolyFlix\\node_modules\\@tailwindcss\\oxide-wasm32-wasi'
22 silly reify ]
23 silly audit bulk request {
23 silly audit   '@babel/polyfill': [ '7.12.1' ],
23 silly audit   '@ericblade/quagga2': [ '1.8.4' ],
23 silly audit   '@esbuild/aix-ppc64': [ '0.25.2' ],
23 silly audit   '@esbuild/android-arm': [ '0.25.2' ],
23 silly audit   '@esbuild/android-arm64': [ '0.25.2' ],
23 silly audit   '@esbuild/android-x64': [ '0.25.2' ],
23 silly audit   '@esbuild/darwin-arm64': [ '0.25.2' ],
23 silly audit   '@esbuild/darwin-x64': [ '0.25.2' ],
23 silly audit   '@esbuild/freebsd-arm64': [ '0.25.2' ],
23 silly audit   '@esbuild/freebsd-x64': [ '0.25.2' ],
23 silly audit   '@esbuild/linux-arm': [ '0.25.2' ],
23 silly audit   '@esbuild/linux-arm64': [ '0.25.2' ],
23 silly audit   '@esbuild/linux-ia32': [ '0.25.2' ],
23 silly audit   '@esbuild/linux-loong64': [ '0.25.2' ],
23 silly audit   '@esbuild/linux-mips64el': [ '0.25.2' ],
23 silly audit   '@esbuild/linux-ppc64': [ '0.25.2' ],
23 silly audit   '@esbuild/linux-riscv64': [ '0.25.2' ],
23 silly audit   '@esbuild/linux-s390x': [ '0.25.2' ],
23 silly audit   '@esbuild/linux-x64': [ '0.25.2' ],
23 silly audit   '@esbuild/netbsd-arm64': [ '0.25.2' ],
23 silly audit   '@esbuild/netbsd-x64': [ '0.25.2' ],
23 silly audit   '@esbuild/openbsd-arm64': [ '0.25.2' ],
23 silly audit   '@esbuild/openbsd-x64': [ '0.25.2' ],
23 silly audit   '@esbuild/sunos-x64': [ '0.25.2' ],
23 silly audit   '@esbuild/win32-arm64': [ '0.25.2' ],
23 silly audit   '@esbuild/win32-ia32': [ '0.25.2' ],
23 silly audit   '@esbuild/win32-x64': [ '0.25.2' ],
23 silly audit   '@popperjs/core': [ '2.11.8' ],
23 silly audit   '@rollup/rollup-android-arm-eabi': [ '4.40.0' ],
23 silly audit   '@rollup/rollup-android-arm64': [ '4.40.0' ],
23 silly audit   '@rollup/rollup-darwin-arm64': [ '4.40.0' ],
23 silly audit   '@rollup/rollup-darwin-x64': [ '4.40.0' ],
23 silly audit   '@rollup/rollup-freebsd-arm64': [ '4.40.0' ],
23 silly audit   '@rollup/rollup-freebsd-x64': [ '4.40.0' ],
23 silly audit   '@rollup/rollup-linux-arm-gnueabihf': [ '4.40.0' ],
23 silly audit   '@rollup/rollup-linux-arm-musleabihf': [ '4.40.0' ],
23 silly audit   '@rollup/rollup-linux-arm64-gnu': [ '4.40.0' ],
23 silly audit   '@rollup/rollup-linux-arm64-musl': [ '4.40.0' ],
23 silly audit   '@rollup/rollup-linux-loongarch64-gnu': [ '4.40.0' ],
23 silly audit   '@rollup/rollup-linux-powerpc64le-gnu': [ '4.40.0' ],
23 silly audit   '@rollup/rollup-linux-riscv64-gnu': [ '4.40.0' ],
23 silly audit   '@rollup/rollup-linux-riscv64-musl': [ '4.40.0' ],
23 silly audit   '@rollup/rollup-linux-s390x-gnu': [ '4.40.0' ],
23 silly audit   '@rollup/rollup-linux-x64-gnu': [ '4.40.0' ],
23 silly audit   '@rollup/rollup-linux-x64-musl': [ '4.40.0' ],
23 silly audit   '@rollup/rollup-win32-arm64-msvc': [ '4.40.0' ],
23 silly audit   '@rollup/rollup-win32-ia32-msvc': [ '4.40.0' ],
23 silly audit   '@rollup/rollup-win32-x64-msvc': [ '4.40.0' ],
23 silly audit   '@tailwindcss/node': [ '4.1.4' ],
23 silly audit   '@tailwindcss/oxide': [ '4.1.4' ],
23 silly audit   '@tailwindcss/oxide-android-arm64': [ '4.1.4' ],
23 silly audit   '@tailwindcss/oxide-darwin-arm64': [ '4.1.4' ],
23 silly audit   '@tailwindcss/oxide-darwin-x64': [ '4.1.4' ],
23 silly audit   '@tailwindcss/oxide-freebsd-x64': [ '4.1.4' ],
23 silly audit   '@tailwindcss/oxide-linux-arm-gnueabihf': [ '4.1.4' ],
23 silly audit   '@tailwindcss/oxide-linux-arm64-gnu': [ '4.1.4' ],
23 silly audit   '@tailwindcss/oxide-linux-arm64-musl': [ '4.1.4' ],
23 silly audit   '@tailwindcss/oxide-linux-x64-gnu': [ '4.1.4' ],
23 silly audit   '@tailwindcss/oxide-linux-x64-musl': [ '4.1.4' ],
23 silly audit   '@tailwindcss/oxide-win32-arm64-msvc': [ '4.1.4' ],
23 silly audit   '@tailwindcss/oxide-win32-x64-msvc': [ '4.1.4' ],
23 silly audit   '@tailwindcss/vite': [ '4.1.4' ],
23 silly audit   '@types/estree': [ '1.0.7' ],
23 silly audit   ajv: [ '6.12.6' ],
23 silly audit   'ansi-regex': [ '5.0.1' ],
23 silly audit   'ansi-styles': [ '4.3.0' ],
23 silly audit   asn1: [ '0.2.6' ],
23 silly audit   'assert-plus': [ '1.0.0' ],
23 silly audit   asynckit: [ '0.4.0' ],
23 silly audit   'aws-sign2': [ '0.7.0' ],
23 silly audit   aws4: [ '1.13.2' ],
23 silly audit   axios: [ '1.8.4' ],
23 silly audit   'bcrypt-pbkdf': [ '1.0.2' ],
23 silly audit   bootstrap: [ '5.3.7' ],
23 silly audit   'call-bind-apply-helpers': [ '1.0.2' ],
23 silly audit   caseless: [ '0.12.0' ],
23 silly audit   chalk: [ '4.1.2' ],
23 silly audit   'supports-color': [ '7.2.0', '8.1.1' ],
23 silly audit   cliui: [ '8.0.1' ],
23 silly audit   'color-convert': [ '2.0.1' ],
23 silly audit   'color-name': [ '1.1.4' ],
23 silly audit   'combined-stream': [ '1.0.8' ],
23 silly audit   concurrently: [ '9.1.2' ],
23 silly audit   'core-js': [ '2.6.12' ],
23 silly audit   'core-util-is': [ '1.0.2' ],
23 silly audit   'cwise-compiler': [ '1.1.3' ],
23 silly audit   dashdash: [ '1.14.1' ],
23 silly audit   'data-uri-to-buffer': [ '0.0.3' ],
23 silly audit   'delayed-stream': [ '1.0.0' ],
23 silly audit   'detect-libc': [ '2.0.3' ],
23 silly audit   'dunder-proto': [ '1.0.1' ],
23 silly audit   'ecc-jsbn': [ '0.1.2' ],
23 silly audit   'emoji-regex': [ '8.0.0' ],
23 silly audit   'enhanced-resolve': [ '5.18.1' ],
23 silly audit   'es-define-property': [ '1.0.1' ],
23 silly audit   'es-errors': [ '1.3.0' ],
23 silly audit   'es-object-atoms': [ '1.1.1' ],
23 silly audit   'es-set-tostringtag': [ '2.1.0' ],
23 silly audit   esbuild: [ '0.25.2' ],
23 silly audit   escalade: [ '3.2.0' ],
23 silly audit   extend: [ '3.0.2' ],
23 silly audit   extsprintf: [ '1.3.0' ],
23 silly audit   'fast-deep-equal': [ '3.1.3' ],
23 silly audit   'fast-json-stable-stringify': [ '2.1.0' ],
23 silly audit   fdir: [ '6.4.3' ],
23 silly audit   'follow-redirects': [ '1.15.9' ],
23 silly audit   'forever-agent': [ '0.6.1' ],
23 silly audit   'form-data': [ '4.0.2', '2.3.3' ],
23 silly audit   fsevents: [ '2.3.3' ],
23 silly audit   'function-bind': [ '1.1.2' ],
23 silly audit   'get-caller-file': [ '2.0.5' ],
23 silly audit   'get-intrinsic': [ '1.3.0' ],
23 silly audit   'get-pixels': [ '3.3.3' ],
23 silly audit   'get-proto': [ '1.0.1' ],
23 silly audit   getpass: [ '0.1.7' ],
23 silly audit   'gl-matrix': [ '3.4.3' ],
23 silly audit   gopd: [ '1.2.0' ],
23 silly audit   'graceful-fs': [ '4.2.11' ],
23 silly audit   'har-schema': [ '2.0.0' ],
23 silly audit   'har-validator': [ '5.1.5' ],
23 silly audit   'has-flag': [ '4.0.0' ],
23 silly audit   'has-symbols': [ '1.1.0' ],
23 silly audit   'has-tostringtag': [ '1.0.2' ],
23 silly audit   hasown: [ '2.0.2' ],
23 silly audit   'http-signature': [ '1.2.0' ],
23 silly audit   'iota-array': [ '1.0.0' ],
23 silly audit   'is-buffer': [ '1.1.6' ],
23 silly audit   'is-fullwidth-code-point': [ '3.0.0' ],
23 silly audit   'is-typedarray': [ '1.0.0' ],
23 silly audit   isstream: [ '0.1.2' ],
23 silly audit   jiti: [ '2.4.2' ],
23 silly audit   'jpeg-js': [ '0.4.4' ],
23 silly audit   jsbn: [ '0.1.1' ],
23 silly audit   'json-schema': [ '0.4.0' ],
23 silly audit   'json-schema-traverse': [ '0.4.1' ],
23 silly audit   'json-stringify-safe': [ '5.0.1' ],
23 silly audit   jsprim: [ '1.4.2' ],
23 silly audit   'laravel-vite-plugin': [ '1.2.0' ],
23 silly audit   lightningcss: [ '1.29.2' ],
23 silly audit   'lightningcss-darwin-arm64': [ '1.29.2' ],
23 silly audit   'lightningcss-darwin-x64': [ '1.29.2' ],
23 silly audit   'lightningcss-freebsd-x64': [ '1.29.2' ],
23 silly audit   'lightningcss-linux-arm-gnueabihf': [ '1.29.2' ],
23 silly audit   'lightningcss-linux-arm64-gnu': [ '1.29.2' ],
23 silly audit   'lightningcss-linux-arm64-musl': [ '1.29.2' ],
23 silly audit   'lightningcss-linux-x64-gnu': [ '1.29.2' ],
23 silly audit   'lightningcss-linux-x64-musl': [ '1.29.2' ],
23 silly audit   'lightningcss-win32-arm64-msvc': [ '1.29.2' ],
23 silly audit   'lightningcss-win32-x64-msvc': [ '1.29.2' ],
23 silly audit   lodash: [ '4.17.21' ],
23 silly audit   'math-intrinsics': [ '1.1.0' ],
23 silly audit   'mime-db': [ '1.52.0' ],
23 silly audit   'mime-types': [ '2.1.35' ],
23 silly audit   nanoid: [ '3.3.11' ],
23 silly audit   ndarray: [ '1.0.19' ],
23 silly audit   'ndarray-linear-interpolate': [ '1.0.0' ],
23 silly audit   'ndarray-pack': [ '1.2.1' ],
23 silly audit   'node-bitmap': [ '0.0.1' ],
23 silly audit   'oauth-sign': [ '0.9.0' ],
23 silly audit   omggif: [ '1.0.10' ],
23 silly audit   'parse-data-uri': [ '0.2.0' ],
23 silly audit   'performance-now': [ '2.1.0' ],
23 silly audit   picocolors: [ '1.1.1' ],
23 silly audit   picomatch: [ '4.0.2', '2.3.1' ],
23 silly audit   pngjs: [ '3.4.0' ],
23 silly audit   postcss: [ '8.5.3' ],
23 silly audit   'proxy-from-env': [ '1.1.0' ],
23 silly audit   psl: [ '1.15.0' ],
23 silly audit   punycode: [ '2.3.1' ],
23 silly audit   qs: [ '6.5.3' ],
23 silly audit   'regenerator-runtime': [ '0.13.11' ],
23 silly audit   request: [ '2.88.2' ],
23 silly audit   'require-directory': [ '2.1.1' ],
23 silly audit   rollup: [ '4.40.0' ],
23 silly audit   rxjs: [ '7.8.2' ],
23 silly audit   'safe-buffer': [ '5.2.1' ],
23 silly audit   'safer-buffer': [ '2.1.2' ],
23 silly audit   select2: [ '4.1.0-rc.0' ],
23 silly audit   'shell-quote': [ '1.8.2' ],
23 silly audit   'source-map-js': [ '1.2.1' ],
23 silly audit   sshpk: [ '1.18.0' ],
23 silly audit   'string-width': [ '4.2.3' ],
23 silly audit   'strip-ansi': [ '6.0.1' ],
23 silly audit   tailwindcss: [ '4.1.4' ],
23 silly audit   tapable: [ '2.2.1' ],
23 silly audit   through: [ '2.3.8' ],
23 silly audit   tinyglobby: [ '0.2.12' ],
23 silly audit   'tough-cookie': [ '2.5.0' ],
23 silly audit   'tree-kill': [ '1.2.2' ],
23 silly audit   tslib: [ '2.8.1' ],
23 silly audit   'tunnel-agent': [ '0.6.0' ],
23 silly audit   tweetnacl: [ '0.14.5' ],
23 silly audit   uniq: [ '1.0.1' ],
23 silly audit   'uri-js': [ '4.4.1' ],
23 silly audit   uuid: [ '3.4.0' ],
23 silly audit   verror: [ '1.10.0' ],
23 silly audit   vite: [ '6.3.2' ],
23 silly audit   'vite-plugin-full-reload': [ '1.2.0' ],
23 silly audit   'wrap-ansi': [ '7.0.0' ],
23 silly audit   y18n: [ '5.0.8' ],
23 silly audit   yargs: [ '17.7.2' ],
23 silly audit   'yargs-parser': [ '21.1.1' ],
23 silly audit   jquery: [ '3.7.1' ],
23 silly audit   '@tailwindcss/oxide-wasm32-wasi': [ '4.1.4' ]
23 silly audit }
24 verbose reify failed optional dependency C:\laragon\www\PolyFlix\node_modules\lightningcss-win32-arm64-msvc
25 silly reify mark deleted [
25 silly reify   'C:\\laragon\\www\\PolyFlix\\node_modules\\lightningcss-win32-arm64-msvc'
25 silly reify ]
26 verbose reify failed optional dependency C:\laragon\www\PolyFlix\node_modules\lightningcss-linux-x64-musl
27 silly reify mark deleted [
27 silly reify   'C:\\laragon\\www\\PolyFlix\\node_modules\\lightningcss-linux-x64-musl'
27 silly reify ]
28 verbose reify failed optional dependency C:\laragon\www\PolyFlix\node_modules\lightningcss-linux-x64-gnu
29 silly reify mark deleted [
29 silly reify   'C:\\laragon\\www\\PolyFlix\\node_modules\\lightningcss-linux-x64-gnu'
29 silly reify ]
30 verbose reify failed optional dependency C:\laragon\www\PolyFlix\node_modules\lightningcss-linux-arm64-musl
31 silly reify mark deleted [
31 silly reify   'C:\\laragon\\www\\PolyFlix\\node_modules\\lightningcss-linux-arm64-musl'
31 silly reify ]
32 verbose reify failed optional dependency C:\laragon\www\PolyFlix\node_modules\lightningcss-linux-arm64-gnu
33 silly reify mark deleted [
33 silly reify   'C:\\laragon\\www\\PolyFlix\\node_modules\\lightningcss-linux-arm64-gnu'
33 silly reify ]
34 verbose reify failed optional dependency C:\laragon\www\PolyFlix\node_modules\lightningcss-linux-arm-gnueabihf
35 silly reify mark deleted [
35 silly reify   'C:\\laragon\\www\\PolyFlix\\node_modules\\lightningcss-linux-arm-gnueabihf'
35 silly reify ]
36 verbose reify failed optional dependency C:\laragon\www\PolyFlix\node_modules\lightningcss-freebsd-x64
37 silly reify mark deleted [
37 silly reify   'C:\\laragon\\www\\PolyFlix\\node_modules\\lightningcss-freebsd-x64'
37 silly reify ]
38 verbose reify failed optional dependency C:\laragon\www\PolyFlix\node_modules\lightningcss-darwin-x64
39 silly reify mark deleted [ 'C:\\laragon\\www\\PolyFlix\\node_modules\\lightningcss-darwin-x64' ]
40 verbose reify failed optional dependency C:\laragon\www\PolyFlix\node_modules\lightningcss-darwin-arm64
41 silly reify mark deleted [
41 silly reify   'C:\\laragon\\www\\PolyFlix\\node_modules\\lightningcss-darwin-arm64'
41 silly reify ]
42 verbose reify failed optional dependency C:\laragon\www\PolyFlix\node_modules\fsevents
43 silly reify mark deleted [ 'C:\\laragon\\www\\PolyFlix\\node_modules\\fsevents' ]
44 verbose reify failed optional dependency C:\laragon\www\PolyFlix\node_modules\@tailwindcss\oxide-win32-arm64-msvc
45 silly reify mark deleted [
45 silly reify   'C:\\laragon\\www\\PolyFlix\\node_modules\\@tailwindcss\\oxide-win32-arm64-msvc'
45 silly reify ]
46 verbose reify failed optional dependency C:\laragon\www\PolyFlix\node_modules\@tailwindcss\oxide-linux-x64-musl
47 silly reify mark deleted [
47 silly reify   'C:\\laragon\\www\\PolyFlix\\node_modules\\@tailwindcss\\oxide-linux-x64-musl'
47 silly reify ]
48 verbose reify failed optional dependency C:\laragon\www\PolyFlix\node_modules\@tailwindcss\oxide-linux-x64-gnu
49 silly reify mark deleted [
49 silly reify   'C:\\laragon\\www\\PolyFlix\\node_modules\\@tailwindcss\\oxide-linux-x64-gnu'
49 silly reify ]
50 verbose reify failed optional dependency C:\laragon\www\PolyFlix\node_modules\@tailwindcss\oxide-linux-arm64-musl
51 silly reify mark deleted [
51 silly reify   'C:\\laragon\\www\\PolyFlix\\node_modules\\@tailwindcss\\oxide-linux-arm64-musl'
51 silly reify ]
52 verbose reify failed optional dependency C:\laragon\www\PolyFlix\node_modules\@tailwindcss\oxide-linux-arm64-gnu
53 silly reify mark deleted [
53 silly reify   'C:\\laragon\\www\\PolyFlix\\node_modules\\@tailwindcss\\oxide-linux-arm64-gnu'
53 silly reify ]
54 verbose reify failed optional dependency C:\laragon\www\PolyFlix\node_modules\@tailwindcss\oxide-linux-arm-gnueabihf
55 silly reify mark deleted [
55 silly reify   'C:\\laragon\\www\\PolyFlix\\node_modules\\@tailwindcss\\oxide-linux-arm-gnueabihf'
55 silly reify ]
56 verbose reify failed optional dependency C:\laragon\www\PolyFlix\node_modules\@tailwindcss\oxide-freebsd-x64
57 silly reify mark deleted [
57 silly reify   'C:\\laragon\\www\\PolyFlix\\node_modules\\@tailwindcss\\oxide-freebsd-x64'
57 silly reify ]
58 verbose reify failed optional dependency C:\laragon\www\PolyFlix\node_modules\@tailwindcss\oxide-darwin-x64
59 silly reify mark deleted [
59 silly reify   'C:\\laragon\\www\\PolyFlix\\node_modules\\@tailwindcss\\oxide-darwin-x64'
59 silly reify ]
60 verbose reify failed optional dependency C:\laragon\www\PolyFlix\node_modules\@tailwindcss\oxide-darwin-arm64
61 silly reify mark deleted [
61 silly reify   'C:\\laragon\\www\\PolyFlix\\node_modules\\@tailwindcss\\oxide-darwin-arm64'
61 silly reify ]
62 verbose reify failed optional dependency C:\laragon\www\PolyFlix\node_modules\@tailwindcss\oxide-android-arm64
63 silly reify mark deleted [
63 silly reify   'C:\\laragon\\www\\PolyFlix\\node_modules\\@tailwindcss\\oxide-android-arm64'
63 silly reify ]
64 verbose reify failed optional dependency C:\laragon\www\PolyFlix\node_modules\@rollup\rollup-win32-ia32-msvc
65 silly reify mark deleted [
65 silly reify   'C:\\laragon\\www\\PolyFlix\\node_modules\\@rollup\\rollup-win32-ia32-msvc'
65 silly reify ]
66 verbose reify failed optional dependency C:\laragon\www\PolyFlix\node_modules\@rollup\rollup-win32-arm64-msvc
67 silly reify mark deleted [
67 silly reify   'C:\\laragon\\www\\PolyFlix\\node_modules\\@rollup\\rollup-win32-arm64-msvc'
67 silly reify ]
68 verbose reify failed optional dependency C:\laragon\www\PolyFlix\node_modules\@rollup\rollup-linux-x64-musl
69 silly reify mark deleted [
69 silly reify   'C:\\laragon\\www\\PolyFlix\\node_modules\\@rollup\\rollup-linux-x64-musl'
69 silly reify ]
70 verbose reify failed optional dependency C:\laragon\www\PolyFlix\node_modules\@rollup\rollup-linux-x64-gnu
71 silly reify mark deleted [
71 silly reify   'C:\\laragon\\www\\PolyFlix\\node_modules\\@rollup\\rollup-linux-x64-gnu'
71 silly reify ]
72 verbose reify failed optional dependency C:\laragon\www\PolyFlix\node_modules\@rollup\rollup-linux-s390x-gnu
73 silly reify mark deleted [
73 silly reify   'C:\\laragon\\www\\PolyFlix\\node_modules\\@rollup\\rollup-linux-s390x-gnu'
73 silly reify ]
74 verbose reify failed optional dependency C:\laragon\www\PolyFlix\node_modules\@rollup\rollup-linux-riscv64-musl
75 silly reify mark deleted [
75 silly reify   'C:\\laragon\\www\\PolyFlix\\node_modules\\@rollup\\rollup-linux-riscv64-musl'
75 silly reify ]
76 verbose reify failed optional dependency C:\laragon\www\PolyFlix\node_modules\@rollup\rollup-linux-riscv64-gnu
77 silly reify mark deleted [
77 silly reify   'C:\\laragon\\www\\PolyFlix\\node_modules\\@rollup\\rollup-linux-riscv64-gnu'
77 silly reify ]
78 verbose reify failed optional dependency C:\laragon\www\PolyFlix\node_modules\@rollup\rollup-linux-powerpc64le-gnu
79 silly reify mark deleted [
79 silly reify   'C:\\laragon\\www\\PolyFlix\\node_modules\\@rollup\\rollup-linux-powerpc64le-gnu'
79 silly reify ]
80 verbose reify failed optional dependency C:\laragon\www\PolyFlix\node_modules\@rollup\rollup-linux-loongarch64-gnu
81 silly reify mark deleted [
81 silly reify   'C:\\laragon\\www\\PolyFlix\\node_modules\\@rollup\\rollup-linux-loongarch64-gnu'
81 silly reify ]
82 verbose reify failed optional dependency C:\laragon\www\PolyFlix\node_modules\@rollup\rollup-linux-arm64-musl
83 silly reify mark deleted [
83 silly reify   'C:\\laragon\\www\\PolyFlix\\node_modules\\@rollup\\rollup-linux-arm64-musl'
83 silly reify ]
84 verbose reify failed optional dependency C:\laragon\www\PolyFlix\node_modules\@rollup\rollup-linux-arm64-gnu
85 silly reify mark deleted [
85 silly reify   'C:\\laragon\\www\\PolyFlix\\node_modules\\@rollup\\rollup-linux-arm64-gnu'
85 silly reify ]
86 verbose reify failed optional dependency C:\laragon\www\PolyFlix\node_modules\@rollup\rollup-linux-arm-musleabihf
87 silly reify mark deleted [
87 silly reify   'C:\\laragon\\www\\PolyFlix\\node_modules\\@rollup\\rollup-linux-arm-musleabihf'
87 silly reify ]
88 verbose reify failed optional dependency C:\laragon\www\PolyFlix\node_modules\@rollup\rollup-linux-arm-gnueabihf
89 silly reify mark deleted [
89 silly reify   'C:\\laragon\\www\\PolyFlix\\node_modules\\@rollup\\rollup-linux-arm-gnueabihf'
89 silly reify ]
90 verbose reify failed optional dependency C:\laragon\www\PolyFlix\node_modules\@rollup\rollup-freebsd-x64
91 silly reify mark deleted [
91 silly reify   'C:\\laragon\\www\\PolyFlix\\node_modules\\@rollup\\rollup-freebsd-x64'
91 silly reify ]
92 verbose reify failed optional dependency C:\laragon\www\PolyFlix\node_modules\@rollup\rollup-freebsd-arm64
93 silly reify mark deleted [
93 silly reify   'C:\\laragon\\www\\PolyFlix\\node_modules\\@rollup\\rollup-freebsd-arm64'
93 silly reify ]
94 verbose reify failed optional dependency C:\laragon\www\PolyFlix\node_modules\@rollup\rollup-darwin-x64
95 silly reify mark deleted [
95 silly reify   'C:\\laragon\\www\\PolyFlix\\node_modules\\@rollup\\rollup-darwin-x64'
95 silly reify ]
96 verbose reify failed optional dependency C:\laragon\www\PolyFlix\node_modules\@rollup\rollup-darwin-arm64
97 silly reify mark deleted [
97 silly reify   'C:\\laragon\\www\\PolyFlix\\node_modules\\@rollup\\rollup-darwin-arm64'
97 silly reify ]
98 verbose reify failed optional dependency C:\laragon\www\PolyFlix\node_modules\@rollup\rollup-android-arm64
99 silly reify mark deleted [
99 silly reify   'C:\\laragon\\www\\PolyFlix\\node_modules\\@rollup\\rollup-android-arm64'
99 silly reify ]
100 verbose reify failed optional dependency C:\laragon\www\PolyFlix\node_modules\@rollup\rollup-android-arm-eabi
101 silly reify mark deleted [
101 silly reify   'C:\\laragon\\www\\PolyFlix\\node_modules\\@rollup\\rollup-android-arm-eabi'
101 silly reify ]
102 verbose reify failed optional dependency C:\laragon\www\PolyFlix\node_modules\@esbuild\win32-ia32
103 silly reify mark deleted [ 'C:\\laragon\\www\\PolyFlix\\node_modules\\@esbuild\\win32-ia32' ]
104 verbose reify failed optional dependency C:\laragon\www\PolyFlix\node_modules\@esbuild\win32-arm64
105 silly reify mark deleted [ 'C:\\laragon\\www\\PolyFlix\\node_modules\\@esbuild\\win32-arm64' ]
106 verbose reify failed optional dependency C:\laragon\www\PolyFlix\node_modules\@esbuild\sunos-x64
107 silly reify mark deleted [ 'C:\\laragon\\www\\PolyFlix\\node_modules\\@esbuild\\sunos-x64' ]
108 verbose reify failed optional dependency C:\laragon\www\PolyFlix\node_modules\@esbuild\openbsd-x64
109 silly reify mark deleted [ 'C:\\laragon\\www\\PolyFlix\\node_modules\\@esbuild\\openbsd-x64' ]
110 verbose reify failed optional dependency C:\laragon\www\PolyFlix\node_modules\@esbuild\openbsd-arm64
111 silly reify mark deleted [ 'C:\\laragon\\www\\PolyFlix\\node_modules\\@esbuild\\openbsd-arm64' ]
112 verbose reify failed optional dependency C:\laragon\www\PolyFlix\node_modules\@esbuild\netbsd-x64
113 silly reify mark deleted [ 'C:\\laragon\\www\\PolyFlix\\node_modules\\@esbuild\\netbsd-x64' ]
114 verbose reify failed optional dependency C:\laragon\www\PolyFlix\node_modules\@esbuild\netbsd-arm64
115 silly reify mark deleted [ 'C:\\laragon\\www\\PolyFlix\\node_modules\\@esbuild\\netbsd-arm64' ]
116 verbose reify failed optional dependency C:\laragon\www\PolyFlix\node_modules\@esbuild\linux-x64
117 silly reify mark deleted [ 'C:\\laragon\\www\\PolyFlix\\node_modules\\@esbuild\\linux-x64' ]
118 verbose reify failed optional dependency C:\laragon\www\PolyFlix\node_modules\@esbuild\linux-s390x
119 silly reify mark deleted [ 'C:\\laragon\\www\\PolyFlix\\node_modules\\@esbuild\\linux-s390x' ]
120 verbose reify failed optional dependency C:\laragon\www\PolyFlix\node_modules\@esbuild\linux-riscv64
121 silly reify mark deleted [ 'C:\\laragon\\www\\PolyFlix\\node_modules\\@esbuild\\linux-riscv64' ]
122 verbose reify failed optional dependency C:\laragon\www\PolyFlix\node_modules\@esbuild\linux-ppc64
123 silly reify mark deleted [ 'C:\\laragon\\www\\PolyFlix\\node_modules\\@esbuild\\linux-ppc64' ]
124 verbose reify failed optional dependency C:\laragon\www\PolyFlix\node_modules\@esbuild\linux-mips64el
125 silly reify mark deleted [
125 silly reify   'C:\\laragon\\www\\PolyFlix\\node_modules\\@esbuild\\linux-mips64el'
125 silly reify ]
126 verbose reify failed optional dependency C:\laragon\www\PolyFlix\node_modules\@esbuild\linux-loong64
127 silly reify mark deleted [ 'C:\\laragon\\www\\PolyFlix\\node_modules\\@esbuild\\linux-loong64' ]
128 verbose reify failed optional dependency C:\laragon\www\PolyFlix\node_modules\@esbuild\linux-ia32
129 silly reify mark deleted [ 'C:\\laragon\\www\\PolyFlix\\node_modules\\@esbuild\\linux-ia32' ]
130 verbose reify failed optional dependency C:\laragon\www\PolyFlix\node_modules\@esbuild\linux-arm64
131 silly reify mark deleted [ 'C:\\laragon\\www\\PolyFlix\\node_modules\\@esbuild\\linux-arm64' ]
132 verbose reify failed optional dependency C:\laragon\www\PolyFlix\node_modules\@esbuild\linux-arm
133 silly reify mark deleted [ 'C:\\laragon\\www\\PolyFlix\\node_modules\\@esbuild\\linux-arm' ]
134 verbose reify failed optional dependency C:\laragon\www\PolyFlix\node_modules\@esbuild\freebsd-x64
135 silly reify mark deleted [ 'C:\\laragon\\www\\PolyFlix\\node_modules\\@esbuild\\freebsd-x64' ]
136 verbose reify failed optional dependency C:\laragon\www\PolyFlix\node_modules\@esbuild\freebsd-arm64
137 silly reify mark deleted [ 'C:\\laragon\\www\\PolyFlix\\node_modules\\@esbuild\\freebsd-arm64' ]
138 verbose reify failed optional dependency C:\laragon\www\PolyFlix\node_modules\@esbuild\darwin-x64
139 silly reify mark deleted [ 'C:\\laragon\\www\\PolyFlix\\node_modules\\@esbuild\\darwin-x64' ]
140 verbose reify failed optional dependency C:\laragon\www\PolyFlix\node_modules\@esbuild\darwin-arm64
141 silly reify mark deleted [ 'C:\\laragon\\www\\PolyFlix\\node_modules\\@esbuild\\darwin-arm64' ]
142 verbose reify failed optional dependency C:\laragon\www\PolyFlix\node_modules\@esbuild\android-x64
143 silly reify mark deleted [ 'C:\\laragon\\www\\PolyFlix\\node_modules\\@esbuild\\android-x64' ]
144 verbose reify failed optional dependency C:\laragon\www\PolyFlix\node_modules\@esbuild\android-arm64
145 silly reify mark deleted [ 'C:\\laragon\\www\\PolyFlix\\node_modules\\@esbuild\\android-arm64' ]
146 verbose reify failed optional dependency C:\laragon\www\PolyFlix\node_modules\@esbuild\android-arm
147 silly reify mark deleted [ 'C:\\laragon\\www\\PolyFlix\\node_modules\\@esbuild\\android-arm' ]
148 verbose reify failed optional dependency C:\laragon\www\PolyFlix\node_modules\@esbuild\aix-ppc64
149 silly reify mark deleted [ 'C:\\laragon\\www\\PolyFlix\\node_modules\\@esbuild\\aix-ppc64' ]
150 silly tarball no local data for jquery@https://registry.npmjs.org/jquery/-/jquery-3.7.1.tgz. Extracting by manifest.
151 silly tarball no local data for uuid@https://registry.npmjs.org/uuid/-/uuid-3.4.0.tgz. Extracting by manifest.
152 silly tarball no local data for verror@https://registry.npmjs.org/verror/-/verror-1.10.0.tgz. Extracting by manifest.
153 silly tarball no local data for uniq@https://registry.npmjs.org/uniq/-/uniq-1.0.1.tgz. Extracting by manifest.
154 silly tarball no local data for tough-cookie@https://registry.npmjs.org/tough-cookie/-/tough-cookie-2.5.0.tgz. Extracting by manifest.
155 silly tarball no local data for tweetnacl@https://registry.npmjs.org/tweetnacl/-/tweetnacl-0.14.5.tgz. Extracting by manifest.
156 silly tarball no local data for uri-js@https://registry.npmjs.org/uri-js/-/uri-js-4.4.1.tgz. Extracting by manifest.
157 silly tarball no local data for tunnel-agent@https://registry.npmjs.org/tunnel-agent/-/tunnel-agent-0.6.0.tgz. Extracting by manifest.
158 silly tarball no local data for sshpk@https://registry.npmjs.org/sshpk/-/sshpk-1.18.0.tgz. Extracting by manifest.
159 silly tarball no local data for through@https://registry.npmjs.org/through/-/through-2.3.8.tgz. Extracting by manifest.
160 silly tarball no local data for safer-buffer@https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz. Extracting by manifest.
161 silly tarball no local data for select2@https://registry.npmjs.org/select2/-/select2-4.1.0-rc.0.tgz. Extracting by manifest.
162 silly tarball no local data for safe-buffer@https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz. Extracting by manifest.
163 silly tarball no local data for request@https://registry.npmjs.org/request/-/request-2.88.2.tgz. Extracting by manifest.
164 silly tarball no local data for regenerator-runtime@https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.13.11.tgz. Extracting by manifest.
165 silly tarball no local data for qs@https://registry.npmjs.org/qs/-/qs-6.5.3.tgz. Extracting by manifest.
166 silly tarball no local data for punycode@https://registry.npmjs.org/punycode/-/punycode-2.3.1.tgz. Extracting by manifest.
167 silly tarball no local data for psl@https://registry.npmjs.org/psl/-/psl-1.15.0.tgz. Extracting by manifest.
168 silly tarball no local data for pngjs@https://registry.npmjs.org/pngjs/-/pngjs-3.4.0.tgz. Extracting by manifest.
169 silly tarball no local data for parse-data-uri@https://registry.npmjs.org/parse-data-uri/-/parse-data-uri-0.2.0.tgz. Extracting by manifest.
170 silly tarball no local data for omggif@https://registry.npmjs.org/omggif/-/omggif-1.0.10.tgz. Extracting by manifest.
171 silly tarball no local data for performance-now@https://registry.npmjs.org/performance-now/-/performance-now-2.1.0.tgz. Extracting by manifest.
172 silly tarball no local data for oauth-sign@https://registry.npmjs.org/oauth-sign/-/oauth-sign-0.9.0.tgz. Extracting by manifest.
173 silly tarball no local data for node-bitmap@https://registry.npmjs.org/node-bitmap/-/node-bitmap-0.0.1.tgz. Extracting by manifest.
174 silly tarball no local data for ndarray-pack@https://registry.npmjs.org/ndarray-pack/-/ndarray-pack-1.2.1.tgz. Extracting by manifest.
175 silly tarball no local data for ndarray-linear-interpolate@https://registry.npmjs.org/ndarray-linear-interpolate/-/ndarray-linear-interpolate-1.0.0.tgz. Extracting by manifest.
176 silly tarball no local data for jsprim@https://registry.npmjs.org/jsprim/-/jsprim-1.4.2.tgz. Extracting by manifest.
177 silly tarball no local data for ndarray@https://registry.npmjs.org/ndarray/-/ndarray-1.0.19.tgz. Extracting by manifest.
178 silly tarball no local data for json-schema-traverse@https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz. Extracting by manifest.
179 silly tarball no local data for json-stringify-safe@https://registry.npmjs.org/json-stringify-safe/-/json-stringify-safe-5.0.1.tgz. Extracting by manifest.
180 silly tarball no local data for jsbn@https://registry.npmjs.org/jsbn/-/jsbn-0.1.1.tgz. Extracting by manifest.
181 silly tarball no local data for json-schema@https://registry.npmjs.org/json-schema/-/json-schema-0.4.0.tgz. Extracting by manifest.
182 silly tarball no local data for jpeg-js@https://registry.npmjs.org/jpeg-js/-/jpeg-js-0.4.4.tgz. Extracting by manifest.
183 silly tarball no local data for isstream@https://registry.npmjs.org/isstream/-/isstream-0.1.2.tgz. Extracting by manifest.
184 silly tarball no local data for is-buffer@https://registry.npmjs.org/is-buffer/-/is-buffer-1.1.6.tgz. Extracting by manifest.
185 silly tarball no local data for is-typedarray@https://registry.npmjs.org/is-typedarray/-/is-typedarray-1.0.0.tgz. Extracting by manifest.
186 silly tarball no local data for iota-array@https://registry.npmjs.org/iota-array/-/iota-array-1.0.0.tgz. Extracting by manifest.
187 silly tarball no local data for http-signature@https://registry.npmjs.org/http-signature/-/http-signature-1.2.0.tgz. Extracting by manifest.
188 silly tarball no local data for har-validator@https://registry.npmjs.org/har-validator/-/har-validator-5.1.5.tgz. Extracting by manifest.
189 silly tarball no local data for har-schema@https://registry.npmjs.org/har-schema/-/har-schema-2.0.0.tgz. Extracting by manifest.
190 silly tarball no local data for gl-matrix@https://registry.npmjs.org/gl-matrix/-/gl-matrix-3.4.3.tgz. Extracting by manifest.
191 silly tarball no local data for getpass@https://registry.npmjs.org/getpass/-/getpass-0.1.7.tgz. Extracting by manifest.
192 silly tarball no local data for get-pixels@https://registry.npmjs.org/get-pixels/-/get-pixels-3.3.3.tgz. Extracting by manifest.
193 silly tarball no local data for forever-agent@https://registry.npmjs.org/forever-agent/-/forever-agent-0.6.1.tgz. Extracting by manifest.
194 silly tarball no local data for fast-deep-equal@https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz. Extracting by manifest.
195 silly tarball no local data for fast-json-stable-stringify@https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz. Extracting by manifest.
196 silly tarball no local data for extsprintf@https://registry.npmjs.org/extsprintf/-/extsprintf-1.3.0.tgz. Extracting by manifest.
197 silly tarball no local data for extend@https://registry.npmjs.org/extend/-/extend-3.0.2.tgz. Extracting by manifest.
198 silly tarball no local data for dashdash@https://registry.npmjs.org/dashdash/-/dashdash-1.14.1.tgz. Extracting by manifest.
199 silly tarball no local data for ecc-jsbn@https://registry.npmjs.org/ecc-jsbn/-/ecc-jsbn-0.1.2.tgz. Extracting by manifest.
200 silly tarball no local data for data-uri-to-buffer@https://registry.npmjs.org/data-uri-to-buffer/-/data-uri-to-buffer-0.0.3.tgz. Extracting by manifest.
201 silly tarball no local data for cwise-compiler@https://registry.npmjs.org/cwise-compiler/-/cwise-compiler-1.1.3.tgz. Extracting by manifest.
202 silly tarball no local data for core-util-is@https://registry.npmjs.org/core-util-is/-/core-util-is-1.0.2.tgz. Extracting by manifest.
203 silly tarball no local data for core-js@https://registry.npmjs.org/core-js/-/core-js-2.6.12.tgz. Extracting by manifest.
204 silly tarball no local data for aws4@https://registry.npmjs.org/aws4/-/aws4-1.13.2.tgz. Extracting by manifest.
205 silly tarball no local data for bcrypt-pbkdf@https://registry.npmjs.org/bcrypt-pbkdf/-/bcrypt-pbkdf-1.0.2.tgz. Extracting by manifest.
206 silly tarball no local data for caseless@https://registry.npmjs.org/caseless/-/caseless-0.12.0.tgz. Extracting by manifest.
207 silly tarball no local data for aws-sign2@https://registry.npmjs.org/aws-sign2/-/aws-sign2-0.7.0.tgz. Extracting by manifest.
208 silly tarball no local data for assert-plus@https://registry.npmjs.org/assert-plus/-/assert-plus-1.0.0.tgz. Extracting by manifest.
209 silly tarball no local data for bootstrap@https://registry.npmjs.org/bootstrap/-/bootstrap-5.3.7.tgz. Extracting by manifest.
210 silly tarball no local data for asn1@https://registry.npmjs.org/asn1/-/asn1-0.2.6.tgz. Extracting by manifest.
211 silly tarball no local data for ajv@https://registry.npmjs.org/ajv/-/ajv-6.12.6.tgz. Extracting by manifest.
212 silly tarball no local data for @ericblade/quagga2@https://registry.npmjs.org/@ericblade/quagga2/-/quagga2-1.8.4.tgz. Extracting by manifest.
213 silly tarball no local data for @popperjs/core@https://registry.npmjs.org/@popperjs/core/-/core-2.11.8.tgz. Extracting by manifest.
214 silly tarball no local data for @babel/polyfill@https://registry.npmjs.org/@babel/polyfill/-/polyfill-7.12.1.tgz. Extracting by manifest.
215 silly tarball no local data for form-data@https://registry.npmjs.org/form-data/-/form-data-2.3.3.tgz. Extracting by manifest.
216 http fetch POST 200 https://registry.npmjs.org/-/npm/v1/security/advisories/bulk 294ms
217 silly audit report {
217 silly audit report   request: [
217 silly audit report     {
217 silly audit report       id: 1096727,
217 silly audit report       url: 'https://github.com/advisories/GHSA-p8p7-x288-28g6',
217 silly audit report       title: 'Server-Side Request Forgery in Request',
217 silly audit report       severity: 'moderate',
217 silly audit report       vulnerable_versions: '<=2.88.2',
217 silly audit report       cwe: [Array],
217 silly audit report       cvss: [Object]
217 silly audit report     }
217 silly audit report   ],
217 silly audit report   'tough-cookie': [
217 silly audit report     {
217 silly audit report       id: 1097682,
217 silly audit report       url: 'https://github.com/advisories/GHSA-72xf-g2v4-qvf3',
217 silly audit report       title: 'tough-cookie Prototype Pollution vulnerability',
217 silly audit report       severity: 'moderate',
217 silly audit report       vulnerable_versions: '<4.1.3',
217 silly audit report       cwe: [Array],
217 silly audit report       cvss: [Object]
217 silly audit report     }
217 silly audit report   ],
217 silly audit report   vite: [
217 silly audit report     {
217 silly audit report       id: 1104176,
217 silly audit report       url: 'https://github.com/advisories/GHSA-859w-5945-r5v3',
217 silly audit report       title: "Vite's server.fs.deny bypassed with /. for files under project root",
217 silly audit report       severity: 'moderate',
217 silly audit report       vulnerable_versions: '>=6.3.0 <=6.3.3',
217 silly audit report       cwe: [Array],
217 silly audit report       cvss: [Object]
217 silly audit report     }
217 silly audit report   ]
217 silly audit report }
218 silly packumentCache corgi:https://registry.npmjs.org/request cache-miss
219 silly packumentCache corgi:https://registry.npmjs.org/tough-cookie cache-miss
220 silly packumentCache corgi:https://registry.npmjs.org/vite cache-miss
221 http fetch GET 200 https://registry.npmjs.org/uniq/-/uniq-1.0.1.tgz 356ms (cache miss)
222 http fetch GET 200 https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.13.11.tgz 1858ms (cache miss)
223 http fetch GET 200 https://registry.npmjs.org/punycode/-/punycode-2.3.1.tgz 1874ms (cache miss)
224 http fetch GET 200 https://registry.npmjs.org/ndarray-pack/-/ndarray-pack-1.2.1.tgz 1996ms (cache miss)
225 http fetch GET 200 https://registry.npmjs.org/node-bitmap/-/node-bitmap-0.0.1.tgz 2043ms (cache miss)
226 http fetch GET 200 https://registry.npmjs.org/json-stringify-safe/-/json-stringify-safe-5.0.1.tgz 2046ms (cache miss)
227 http fetch GET 200 https://registry.npmjs.org/cwise-compiler/-/cwise-compiler-1.1.3.tgz 2040ms (cache miss)
228 http fetch GET 200 https://registry.npmjs.org/ndarray-linear-interpolate/-/ndarray-linear-interpolate-1.0.0.tgz 2114ms (cache miss)
229 http fetch GET 200 https://registry.npmjs.org/iota-array/-/iota-array-1.0.0.tgz 2118ms (cache miss)
230 http fetch GET 200 https://registry.npmjs.org/har-validator/-/har-validator-5.1.5.tgz 2118ms (cache miss)
231 http fetch GET 200 https://registry.npmjs.org/is-typedarray/-/is-typedarray-1.0.0.tgz 2181ms (cache miss)
232 http fetch GET 200 https://registry.npmjs.org/getpass/-/getpass-0.1.7.tgz 2186ms (cache miss)
233 http fetch GET 200 https://registry.npmjs.org/is-buffer/-/is-buffer-1.1.6.tgz 2302ms (cache miss)
234 http fetch GET 200 https://registry.npmjs.org/data-uri-to-buffer/-/data-uri-to-buffer-0.0.3.tgz 2295ms (cache miss)
235 http fetch GET 200 https://registry.npmjs.org/bcrypt-pbkdf/-/bcrypt-pbkdf-1.0.2.tgz 2303ms (cache miss)
236 http fetch GET 200 https://registry.npmjs.org/caseless/-/caseless-0.12.0.tgz 2330ms (cache miss)
237 http fetch GET 200 https://registry.npmjs.org/tunnel-agent/-/tunnel-agent-0.6.0.tgz 2380ms (cache miss)
238 http fetch GET 200 https://registry.npmjs.org/get-pixels/-/get-pixels-3.3.3.tgz 2434ms (cache miss)
239 http fetch GET 200 https://registry.npmjs.org/assert-plus/-/assert-plus-1.0.0.tgz 2449ms (cache miss)
240 http fetch GET 200 https://registry.npmjs.org/parse-data-uri/-/parse-data-uri-0.2.0.tgz 2498ms (cache miss)
241 http fetch GET 200 https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz 2504ms (cache miss)
242 http fetch GET 200 https://registry.npmjs.org/oauth-sign/-/oauth-sign-0.9.0.tgz 2538ms (cache miss)
243 warn deprecated har-validator@5.1.5: this library is no longer supported
244 http fetch GET 200 https://registry.npmjs.org/core-util-is/-/core-util-is-1.0.2.tgz 2523ms (cache miss)
245 http fetch GET 200 https://registry.npmjs.org/forever-agent/-/forever-agent-0.6.1.tgz 2604ms (cache miss)
246 http fetch GET 200 https://registry.npmjs.org/aws4/-/aws4-1.13.2.tgz 2592ms (cache miss)
247 http fetch GET 200 https://registry.npmjs.org/json-schema/-/json-schema-0.4.0.tgz 2625ms (cache miss)
248 http fetch GET 200 https://registry.npmjs.org/isstream/-/isstream-0.1.2.tgz 2638ms (cache miss)
249 http fetch GET 200 https://registry.npmjs.org/aws-sign2/-/aws-sign2-0.7.0.tgz 2625ms (cache miss)
250 http fetch GET 200 https://registry.npmjs.org/omggif/-/omggif-1.0.10.tgz 2738ms (cache miss)
251 http fetch GET 200 https://registry.npmjs.org/ndarray/-/ndarray-1.0.19.tgz 2736ms (cache miss)
252 http fetch GET 200 https://registry.npmjs.org/through/-/through-2.3.8.tgz 2771ms (cache miss)
253 http fetch GET 200 https://registry.npmjs.org/request 2543ms (cache miss)
254 silly packumentCache corgi:https://registry.npmjs.org/request set size:undefined disposed:false
255 http fetch GET 200 https://registry.npmjs.org/tough-cookie 2611ms (cache miss)
256 silly packumentCache corgi:https://registry.npmjs.org/tough-cookie set size:undefined disposed:false
257 http fetch GET 200 https://registry.npmjs.org/extend/-/extend-3.0.2.tgz 2994ms (cache miss)
258 http fetch GET 200 https://registry.npmjs.org/tough-cookie/-/tough-cookie-2.5.0.tgz 3062ms (cache miss)
259 http fetch GET 200 https://registry.npmjs.org/form-data/-/form-data-2.3.3.tgz 3020ms (cache miss)
260 http fetch GET 200 https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz 3080ms (cache miss)
261 http fetch GET 200 https://registry.npmjs.org/jpeg-js/-/jpeg-js-0.4.4.tgz 3096ms (cache miss)
262 http fetch GET 200 https://registry.npmjs.org/verror/-/verror-1.10.0.tgz 3120ms (cache miss)
263 http fetch GET 200 https://registry.npmjs.org/extsprintf/-/extsprintf-1.3.0.tgz 3110ms (cache miss)
264 http fetch GET 200 https://registry.npmjs.org/jsprim/-/jsprim-1.4.2.tgz 3131ms (cache miss)
265 http fetch GET 200 https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz 3129ms (cache miss)
266 http fetch GET 200 https://registry.npmjs.org/ecc-jsbn/-/ecc-jsbn-0.1.2.tgz 3130ms (cache miss)
267 http fetch GET 200 https://registry.npmjs.org/jsbn/-/jsbn-0.1.1.tgz 3158ms (cache miss)
268 http fetch GET 200 https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz 3176ms (cache miss)
269 http fetch GET 200 https://registry.npmjs.org/asn1/-/asn1-0.2.6.tgz 3182ms (cache miss)
270 http fetch GET 200 https://registry.npmjs.org/http-signature/-/http-signature-1.2.0.tgz 3233ms (cache miss)
271 http fetch GET 200 https://registry.npmjs.org/dashdash/-/dashdash-1.14.1.tgz 3300ms (cache miss)
272 http fetch GET 200 https://registry.npmjs.org/tweetnacl/-/tweetnacl-0.14.5.tgz 3463ms (cache miss)
273 http fetch GET 200 https://registry.npmjs.org/performance-now/-/performance-now-2.1.0.tgz 3472ms (cache miss)
274 http fetch GET 200 https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz 3515ms (cache miss)
275 http fetch GET 200 https://registry.npmjs.org/har-schema/-/har-schema-2.0.0.tgz 3717ms (cache miss)
276 http fetch GET 200 https://registry.npmjs.org/uuid/-/uuid-3.4.0.tgz 3752ms (cache miss)
277 warn deprecated uuid@3.4.0: Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.
278 http fetch GET 200 https://registry.npmjs.org/@babel/polyfill/-/polyfill-7.12.1.tgz 3819ms (cache miss)
279 http fetch GET 200 https://registry.npmjs.org/request/-/request-2.88.2.tgz 3897ms (cache miss)
280 warn deprecated request@2.88.2: request has been deprecated, see https://github.com/request/request/issues/3142
281 warn deprecated @babel/polyfill@7.12.1: 🚨 This package has been deprecated in favor of separate inclusion of a polyfill and regenerator-runtime (when needed). See the @babel/polyfill docs (https://babeljs.io/docs/en/babel-polyfill) for more information.
282 http fetch GET 200 https://registry.npmjs.org/qs/-/qs-6.5.3.tgz 3979ms (cache miss)
283 http fetch GET 200 https://registry.npmjs.org/psl/-/psl-1.15.0.tgz 4154ms (cache miss)
284 http fetch GET 200 https://registry.npmjs.org/pngjs/-/pngjs-3.4.0.tgz 4393ms (cache miss)
285 http fetch GET 200 https://registry.npmjs.org/vite 4143ms (cache miss)
286 silly packumentCache corgi:https://registry.npmjs.org/vite set size:undefined disposed:false
287 silly packumentCache corgi:https://registry.npmjs.org/get-pixels cache-miss
288 http fetch GET 200 https://registry.npmjs.org/sshpk/-/sshpk-1.18.0.tgz 4620ms (cache miss)
289 http fetch GET 200 https://registry.npmjs.org/gl-matrix/-/gl-matrix-3.4.3.tgz 4661ms (cache miss)
290 http fetch GET 200 https://registry.npmjs.org/@ericblade/quagga2/-/quagga2-1.8.4.tgz 4658ms (cache miss)
291 http fetch GET 200 https://registry.npmjs.org/uri-js/-/uri-js-4.4.1.tgz 4755ms (cache miss)
292 http fetch GET 200 https://registry.npmjs.org/ajv/-/ajv-6.12.6.tgz 4955ms (cache miss)
293 http fetch GET 200 https://registry.npmjs.org/get-pixels 863ms (cache miss)
294 silly packumentCache corgi:https://registry.npmjs.org/get-pixels set size:undefined disposed:false
295 silly packumentCache corgi:https://registry.npmjs.org/@tailwindcss%2fvite cache-miss
296 silly packumentCache corgi:https://registry.npmjs.org/laravel-vite-plugin cache-miss
297 http fetch GET 200 https://registry.npmjs.org/jquery/-/jquery-3.7.1.tgz 5787ms (cache miss)
298 http fetch GET 200 https://registry.npmjs.org/laravel-vite-plugin 119ms (cache miss)
299 silly packumentCache corgi:https://registry.npmjs.org/laravel-vite-plugin set size:undefined disposed:false
300 http fetch GET 200 https://registry.npmjs.org/@tailwindcss%2fvite 187ms (cache miss)
301 silly packumentCache corgi:https://registry.npmjs.org/@tailwindcss%2fvite set size:undefined disposed:false
302 silly packumentCache corgi:https://registry.npmjs.org/@ericblade%2fquagga2 cache-miss
303 http fetch GET 200 https://registry.npmjs.org/select2/-/select2-4.1.0-rc.0.tgz 6168ms (cache miss)
304 http fetch GET 200 https://registry.npmjs.org/@popperjs/core/-/core-2.11.8.tgz 6359ms (cache miss)
305 http fetch GET 200 https://registry.npmjs.org/bootstrap/-/bootstrap-5.3.7.tgz 6567ms (cache miss)
306 http fetch GET 200 https://registry.npmjs.org/@ericblade%2fquagga2 1235ms (cache miss)
307 silly packumentCache corgi:https://registry.npmjs.org/@ericblade%2fquagga2 set size:undefined disposed:false
308 http fetch GET 200 https://registry.npmjs.org/core-js/-/core-js-2.6.12.tgz 8141ms (cache miss)
309 warn deprecated core-js@2.6.12: core-js@<3.23.3 is no longer maintained and not recommended for usage due to the number of issues. Because of the V8 engine whims, feature detection in old core-js versions could cause a slowdown up to 100x even if nothing is polyfilled. Some versions have web compatibility issues. Please, upgrade your dependencies to the actual version of core-js.
310 info run core-js@2.6.12 postinstall node_modules/core-js node -e "try{require('./postinstall')}catch(e){}"
311 info run core-js@2.6.12 postinstall { code: 0, signal: null }
312 silly ADD node_modules/jquery
313 silly ADD node_modules/verror
314 silly ADD node_modules/uuid
315 silly ADD node_modules/uri-js
316 silly ADD node_modules/uniq
317 silly ADD node_modules/tweetnacl
318 silly ADD node_modules/tunnel-agent
319 silly ADD node_modules/tough-cookie
320 silly ADD node_modules/through
321 silly ADD node_modules/sshpk
322 silly ADD node_modules/select2
323 silly ADD node_modules/safer-buffer
324 silly ADD node_modules/safe-buffer
325 silly ADD node_modules/request
326 silly ADD node_modules/request/node_modules/form-data
327 silly ADD node_modules/regenerator-runtime
328 silly ADD node_modules/qs
329 silly ADD node_modules/punycode
330 silly ADD node_modules/psl
331 silly ADD node_modules/pngjs
332 silly ADD node_modules/performance-now
333 silly ADD node_modules/parse-data-uri
334 silly ADD node_modules/omggif
335 silly ADD node_modules/oauth-sign
336 silly ADD node_modules/node-bitmap
337 silly ADD node_modules/ndarray-pack
338 silly ADD node_modules/ndarray-linear-interpolate
339 silly ADD node_modules/ndarray
340 silly ADD
341 silly ADD
342 silly ADD
343 silly ADD
344 silly ADD
345 silly ADD
346 silly ADD
347 silly ADD
348 silly ADD
349 silly ADD node_modules/jsprim
350 silly ADD node_modules/json-stringify-safe
351 silly ADD node_modules/json-schema-traverse
352 silly ADD node_modules/json-schema
353 silly ADD node_modules/jsbn
354 silly ADD node_modules/jpeg-js
355 silly ADD node_modules/isstream
356 silly ADD node_modules/is-typedarray
357 silly ADD node_modules/is-buffer
358 silly ADD node_modules/iota-array
359 silly ADD node_modules/http-signature
360 silly ADD node_modules/har-validator
361 silly ADD node_modules/har-schema
362 silly ADD node_modules/gl-matrix
363 silly ADD node_modules/getpass
364 silly ADD node_modules/get-pixels
365 silly ADD
366 silly ADD node_modules/forever-agent
367 silly ADD node_modules/fast-json-stable-stringify
368 silly ADD node_modules/fast-deep-equal
369 silly ADD node_modules/extsprintf
370 silly ADD node_modules/extend
371 silly ADD node_modules/ecc-jsbn
372 silly ADD node_modules/data-uri-to-buffer
373 silly ADD node_modules/dashdash
374 silly ADD node_modules/cwise-compiler
375 silly ADD node_modules/core-util-is
376 silly ADD node_modules/core-js
377 silly ADD node_modules/caseless
378 silly ADD node_modules/bootstrap
379 silly ADD node_modules/bcrypt-pbkdf
380 silly ADD node_modules/aws4
381 silly ADD node_modules/aws-sign2
382 silly ADD node_modules/assert-plus
383 silly ADD node_modules/asn1
384 silly ADD node_modules/ajv
385 silly ADD
386 silly ADD
387 silly ADD
388 silly ADD
389 silly ADD
390 silly ADD
391 silly ADD
392 silly ADD
393 silly ADD
394 silly ADD
395 silly ADD
396 silly ADD
397 silly ADD
398 silly ADD
399 silly ADD
400 silly ADD
401 silly ADD
402 silly ADD
403 silly ADD
404 silly ADD
405 silly ADD
406 silly ADD
407 silly ADD
408 silly ADD
409 silly ADD
410 silly ADD
411 silly ADD
412 silly ADD
413 silly ADD
414 silly ADD
415 silly ADD node_modules/@popperjs/core
416 silly ADD
417 silly ADD
418 silly ADD
419 silly ADD
420 silly ADD
421 silly ADD
422 silly ADD
423 silly ADD
424 silly ADD
425 silly ADD
426 silly ADD
427 silly ADD
428 silly ADD
429 silly ADD
430 silly ADD
431 silly ADD
432 silly ADD
433 silly ADD
434 silly ADD
435 silly ADD
436 silly ADD
437 silly ADD
438 silly ADD
439 silly ADD
440 silly ADD node_modules/@ericblade/quagga2
441 silly ADD node_modules/@babel/polyfill
442 verbose cwd C:\laragon\www\PolyFlix
443 verbose os Windows_NT 10.0.26100
444 verbose node v20.11.1
445 verbose npm  v10.9.0
446 verbose exit 0
447 info ok
