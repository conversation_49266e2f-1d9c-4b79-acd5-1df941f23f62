{"name": "@ericblade/quagga2", "dist-tags": {"latest": "1.8.4"}, "versions": {"0.0.1": {"name": "@ericblade/quagga2", "version": "0.0.1", "dependencies": {"get-pixels": "^3.2.3", "gl-mat2": "^1.0.0", "gl-vec2": "^1.0.0", "gl-vec3": "^1.0.3", "lodash": "^4.17.4", "ndarray": "^1.0.18", "ndarray-linear-interpolate": "^1.0.0"}, "devDependencies": {"async": "^1.4.2", "babel-cli": "^6.5.1", "babel-core": "^6.21.0", "babel-eslint": "^7.1.1", "babel-istanbul": "^0.12.2", "babel-istanbul-loader": "^0.1.0", "babel-loader": "^6.2.10", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-check-es2015-constants": "^6.3.13", "babel-plugin-lodash": "^3.2.11", "babel-plugin-transform-es2015-arrow-functions": "^6.3.13", "babel-plugin-transform-es2015-block-scoped-functions": "^6.3.13", "babel-plugin-transform-es2015-block-scoping": "^6.21.0", "babel-plugin-transform-es2015-classes": "^6.3.13", "babel-plugin-transform-es2015-computed-properties": "^6.3.13", "babel-plugin-transform-es2015-destructuring": "^6.3.13", "babel-plugin-transform-es2015-for-of": "^6.3.13", "babel-plugin-transform-es2015-function-name": "^6.3.13", "babel-plugin-transform-es2015-literals": "^6.3.13", "babel-plugin-transform-es2015-modules-commonjs": "^6.3.13", "babel-plugin-transform-es2015-object-super": "^6.3.13", "babel-plugin-transform-es2015-parameters": "^6.21.0", "babel-plugin-transform-es2015-shorthand-properties": "^6.3.13", "babel-plugin-transform-es2015-spread": "^6.3.13", "babel-plugin-transform-es2015-sticky-regex": "^6.3.13", "babel-plugin-transform-es2015-template-literals": "^6.3.13", "babel-plugin-transform-es2015-typeof-symbol": "^6.3.13", "babel-plugin-transform-es2015-unicode-regex": "^6.3.13", "babel-plugin-transform-object-rest-spread": "^6.5.0", "babel-plugin-transform-regenerator": "^6.21.0", "chai": "^3.4.1", "core-js": "^2.4.1", "cross-env": "^3.1.4", "eslint": "^4.19.1", "grunt": "^0.4.5", "grunt-cli": "^0.1.13", "grunt-contrib-nodeunit": "^0.4.1", "grunt-karma": "^2.0.0", "isparta-loader": "^2.0.0", "karma": "^1.3.0", "karma-chai": "0.1.0", "karma-chrome-launcher": "^2.0.0", "karma-coverage": "^1.1.1", "karma-firefox-launcher": "^0.1.7", "karma-mocha": "~0.2.0", "karma-phantomjs-launcher": "^0.2.1", "karma-sinon": "^1.0.4", "karma-sinon-chai": "^1.1.0", "karma-source-map-support": "^1.1.0", "karma-webpack": "^1.8.1", "lolex": "^1.4.0", "mocha": "^2.3.2", "phantomjs": "^1.9.18", "sinon": "^1.17.7", "sinon-chai": "^2.8.0", "webpack": "^2.2.1", "webpack-sources": "^0.1.4"}, "directories": {"doc": "doc"}, "dist": {"integrity": "sha512-BIPOFvKJkqBMQmtR1U7PuR0L4hbIklg9cf187sAJAo7JK/tbMTP1B+8gHjEczGJYiHns1mQK97tWs2u2Y02LqA==", "shasum": "998de83a9fd62b18ff3531e711c18dc6185f54d2", "tarball": "https://registry.npmjs.org/@ericblade/quagga2/-/quagga2-0.0.1.tgz", "fileCount": 15, "unpackedSize": 2370054, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdU0SLCRA9TVsSAnZWagAAsf8QAJt4SM8WxY4hVhRgyO39\n5MNaHa4C+NEFeMpx2rDHpo8inglvdrIExGRoXbegiiycRlm1llCYKRd5x6Gm\n1D9vYBxTk3aDSVOTTYqrYW9PLdClwUOH6/y2FalKdbXMlsJRC5HqYvRWWu2c\n+zYxtqusMNAbc28rPgi/fqqQBn/cX7vNRWAQ6FMPpF6XDbVlpBpT1RUJkehn\ne8z8N/puvf+SFW+neeCqmb18toMxkrqhNj3vXpIszPSsDmd/rMod+/XNahEq\nCjTn/NMy0aWshBqWURT+o0nJts97vcQ1cdqRRZSPbr6QS0V+Qw9A2inVTClR\nVdhIXZtfT1D9YrO2HTyuqkDhI3GLshhrchhsN4NKvfrf4yFVkjFzobYaqh+r\nD3CobJd2lC1k2pUkcOVwIp30HpsKREqYTPM258UlVjG8yNDGWRBvgxljV+Gv\n52fqYn7E+9qbluXi69dx8H/HD7hlW7mBnbjQqqamRwcZ+w6IEOJiJmlCFsZX\nlDPlvOgvFemi4gydaJl6+4vQBoqqDBaN5z7nFbedExF68+IfFBhAl6rqzGU+\n8sC48UC0npMOjUA6ADgzzHdUt0zyLGvRML2LKarEgSz7cg4wvjRvIgYdn2dd\nRyg6rutg/rgHObak1ZmRuIA7TvMbULfPsRu8Nvk7B1CuogSxPOqS5GKFQmuW\nKary\r\n=ncF3\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCID9/PB4sHlfKEGbOw6AnHof0LTkouuB9JQJ/AEL7kdBuAiEApEHSghz5HTiqxwmnIe9EKVPj7H1bjZVA0c+9Rg4OOks="}]}, "engines": {"node": ">= 4.0"}}, "0.0.2": {"name": "@ericblade/quagga2", "version": "0.0.2", "dependencies": {"get-pixels": "^3.3.2", "gl-mat2": "^1.0.0", "gl-vec2": "^1.3.0", "gl-vec3": "^1.0.3", "lodash": "^4.17.15", "ndarray": "^1.0.18", "ndarray-linear-interpolate": "^1.0.0", "snyk": "^1.192.4"}, "devDependencies": {"async": "^1.4.2", "babel-cli": "^6.5.1", "babel-core": "^6.21.0", "babel-eslint": "^7.1.1", "babel-istanbul": "^0.12.2", "babel-istanbul-loader": "^0.1.0", "babel-loader": "^6.2.10", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-check-es2015-constants": "^6.3.13", "babel-plugin-lodash": "^3.2.11", "babel-plugin-transform-es2015-arrow-functions": "^6.3.13", "babel-plugin-transform-es2015-block-scoped-functions": "^6.3.13", "babel-plugin-transform-es2015-block-scoping": "^6.21.0", "babel-plugin-transform-es2015-classes": "^6.3.13", "babel-plugin-transform-es2015-computed-properties": "^6.3.13", "babel-plugin-transform-es2015-destructuring": "^6.3.13", "babel-plugin-transform-es2015-for-of": "^6.3.13", "babel-plugin-transform-es2015-function-name": "^6.3.13", "babel-plugin-transform-es2015-literals": "^6.3.13", "babel-plugin-transform-es2015-modules-commonjs": "^6.3.13", "babel-plugin-transform-es2015-object-super": "^6.3.13", "babel-plugin-transform-es2015-parameters": "^6.21.0", "babel-plugin-transform-es2015-shorthand-properties": "^6.3.13", "babel-plugin-transform-es2015-spread": "^6.3.13", "babel-plugin-transform-es2015-sticky-regex": "^6.3.13", "babel-plugin-transform-es2015-template-literals": "^6.3.13", "babel-plugin-transform-es2015-typeof-symbol": "^6.3.13", "babel-plugin-transform-es2015-unicode-regex": "^6.3.13", "babel-plugin-transform-object-rest-spread": "^6.5.0", "babel-plugin-transform-regenerator": "^6.21.0", "chai": "^4.2.0", "core-js": "^2.4.1", "cross-env": "^5.2.0", "eslint": "^6.1.0", "grunt": "^1.0.4", "grunt-cli": "^1.3.2", "grunt-contrib-nodeunit": "^1.0.0", "grunt-karma": "^2.0.0", "isparta-loader": "^2.0.0", "karma": "^1.3.0", "karma-chai": "0.1.0", "karma-chrome-launcher": "^2.0.0", "karma-coverage": "^1.1.1", "karma-firefox-launcher": "^0.1.7", "karma-mocha": "~0.2.0", "karma-phantomjs-launcher": "^0.2.3", "karma-sinon": "^1.0.4", "karma-sinon-chai": "^1.3.4", "karma-source-map-support": "^1.1.0", "karma-webpack": "^1.8.1", "lolex": "^4.2.0", "mocha": "^2.3.2", "phantomjs": "^2.1.7", "sinon": "^2.1.0", "sinon-chai": "^2.8.0", "webpack": "^2.7.0", "webpack-sources": "^1.4.3"}, "directories": {"doc": "doc"}, "dist": {"integrity": "sha512-xZ8gwA0H8+TX2EFtzE59QHi5q0MGeDspJtlAKXFpYLmZ7/V5E6Ez5xHi6ZzguDmbB1SzPzc9X5TIVinNcEopgQ==", "shasum": "68b5abad3e73a977fdfdc917e1c1ee39f3062b4b", "tarball": "https://registry.npmjs.org/@ericblade/quagga2/-/quagga2-0.0.2.tgz", "fileCount": 14, "unpackedSize": 2371493, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdVLKqCRA9TVsSAnZWagAAFWMP/1b3PMsnKJD/7o5C71jg\nd88JfHvW8LcnMaczjwFzXleQcp+CcSrhoLBfkb5qD1+mfdawhTTG833a2S0z\nerh2EsRDFzZgdXS7+KsZkBAE3nicui4W20JYkKi7JbtB7HZr+lNgcfoiJowp\n0d+gmrY6GsxSYz68GtGVTp237qQgHJ4wcyr9wx79oB5GLe8wrYNEzKC++4QZ\nTOFEO5KbYKrI9zqdRhI9nyhovQqCwv+a4KHEyyius4UoSE65snWoPpk+6hT0\nelqTuMvXF/28D60yFsTA7K76uAmFxNItHaE5CCzAQhcUlCKmcaOK7d+yzOdG\nlqIEBhRUb81Ndu7LO4Hn0I1xyW20OSf8GPIzgaFMMShNgbJbcqJGEF2vWmFN\n/5kaAJ5OJCMPY/YlsQYfGmXvSbOZHG1pFH1JM6lXnnZfOmwZWOFl9WhkILfg\nq/9gPF3C4bV4Q966F4LhkOxu0CsvHZp89uTPbKfxJqRs2kl/Jqc1SQpfPFr4\ncKjvKxtPob+Wql9uH2ee/QXGbAIZnXByBWSjMHU9GyxNswFqgPJZXQ0ycc/A\ndfXwzz1zN7Yf4PJS2Cv/y2rcrVdJg2PwTgxrA5TC3c4OoiBg3BXuS8YXODm9\nGe9rpwGz8R2VAbwdcPs9cnGl+fwknkpmRLTRGHAeuiyn0+k1cFCd6N5LffHi\n3HxM\r\n=df0y\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD7oxKgseMtUErab3KjaAc7dbYde9gqKXr76JzTWEMPnAIgZCOg1fKJpevLLByaQufxS8hwH81hdABNbKrySJ+y5tg="}]}, "engines": {"node": ">= 8.0"}}, "0.0.3": {"name": "@ericblade/quagga2", "version": "0.0.3", "dependencies": {"@babel/runtime": "^7.5.5", "get-pixels": "^3.3.2", "gl-mat2": "^1.0.0", "gl-vec2": "^1.3.0", "gl-vec3": "^1.0.3", "lodash": "^4.17.15", "ndarray": "^1.0.18", "ndarray-linear-interpolate": "^1.0.0", "snyk": "^1.221.0"}, "devDependencies": {"async": "^1.4.2", "@babel/core": "^7.5.5", "@babel/plugin-proposal-class-properties": "^7.5.5", "@babel/plugin-proposal-object-rest-spread": "^7.5.5", "@babel/plugin-transform-runtime": "^7.5.5", "@babel/preset-env": "^7.5.5", "@babel/preset-typescript": "^7.3.3", "babel-loader": "^8.0.6", "babel-plugin-add-module-exports": "^1.0.2", "babel-plugin-istanbul": "^5.2.0", "chai": "^4.2.0", "core-js": "^2.4.1", "cross-env": "^6.0.0", "eslint": "^6.1.0", "grunt": "^1.0.4", "grunt-cli": "^1.3.2", "grunt-contrib-nodeunit": "^1.0.0", "grunt-karma": "^2.0.0", "isparta-loader": "^2.0.0", "karma": "^1.3.0", "karma-chai": "0.1.0", "karma-chrome-launcher": "^2.0.0", "karma-coverage": "^1.1.1", "karma-firefox-launcher": "^0.1.7", "karma-mocha": "~0.2.0", "karma-phantomjs-launcher": "^0.2.3", "karma-sinon": "^1.0.4", "karma-sinon-chai": "^2.0.2", "karma-source-map-support": "^1.1.0", "karma-webpack": "^1.8.1", "lolex": "^4.2.0", "mocha": "^2.3.2", "phantomjs": "^2.1.7", "sinon": "^2.1.0", "sinon-chai": "^2.8.0", "webpack": "^2.7.0", "webpack-sources": "^1.4.3"}, "directories": {"doc": "doc"}, "dist": {"integrity": "sha512-5QHR59KEGg2zA5Wlo9Eu4BpSR5nR7CvsI2OCgV+Btw79izOngl46tOsFzXhC7/4v9qC3TjK9Uu4aF8vYndUMDw==", "shasum": "02efde58b141bad69e28d25f9f42917dd4e2478e", "tarball": "https://registry.npmjs.org/@ericblade/quagga2/-/quagga2-0.0.3.tgz", "fileCount": 17, "unpackedSize": 3670488, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdgrTsCRA9TVsSAnZWagAAwFYP+wUSWxpPSgddVXHJSJn2\nW/d9XEJfjxN11+yMNByGEEodM1fC2uxsrWn1boDgCOtCi2AkjRmu7znhIj+d\nVAX2rN4067SkCyu34XxmKfQgOzJ8nT1MzwPO3jxFd3lkWHB8Jo53ikghqe1h\nHwLbDCu2YxBuLirrrNynRNs6uqfw7SNeCjv8TIR2fE3AhGyxMg+o/C/Mm0rw\n4Xzgblad0zo/8PgFKaHEurrfVAbdawZgLE62QLZLIKqwbo/ZXXdme1FlRv/6\nfiYQZ2+FJrUV8OJgLeQ3CTOor6zuMjJYNDVkhfD3N5mOeBoScQg5RIFki5Z8\n7Abb7H2Eq3NsTqDC0YqxnEv4sSmqZy0moUImMc3qrSXDIueWBhDCigPzZPJ2\new96DfQ+9with0b36RUhQkHiue0XXSIHihirTRXq4Vp5TjJPtcjFNs7VdI05\n6C9xNZnk0vEZweoDVyyutfOkW4bfwOEqnSaSu6babzpqw9/UmlqqGeFcKpS4\ne2ESKdgEEkBd8pgx0pE4omQJSjwCMlWNdD6OfvXcph70kK/y961B5+VC/G7n\nuKdnQkeOMMmV54dhiAXywcLsfnWH9xL5PgH/mXi6HSeiBHK/wc67T8Fkn8q6\n59lYwi/gT8R4vQcLaM3yNCeRoy6CNtIlENlVNK2oa+igPAGqxU+r+6O0mpSc\n4vK2\r\n=JP+z\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICj8RqUoPAqn/MeJVdjALhgVDzL3w5HWF16h8Mz0TiiQAiBhGz4WheFbnQ6EeiBkiEnSyUxaoynyQFI9cVUVpHDBJQ=="}]}, "engines": {"node": ">= 8.0"}}, "0.0.4": {"name": "@ericblade/quagga2", "version": "0.0.4", "dependencies": {"@babel/runtime": "^7.5.5", "get-pixels": "^3.3.2", "gl-mat2": "^1.0.0", "gl-vec2": "^1.3.0", "gl-vec3": "^1.0.3", "lodash": "^4.17.15", "ndarray": "^1.0.18", "ndarray-linear-interpolate": "^1.0.0", "snyk": "^1.221.0"}, "devDependencies": {"async": "^1.4.2", "@babel/core": "^7.5.5", "@babel/plugin-proposal-class-properties": "^7.5.5", "@babel/plugin-proposal-object-rest-spread": "^7.5.5", "@babel/plugin-transform-runtime": "^7.5.5", "@babel/preset-env": "^7.5.5", "@babel/preset-typescript": "^7.3.3", "babel-loader": "^8.0.6", "babel-plugin-add-module-exports": "^1.0.2", "babel-plugin-istanbul": "^5.2.0", "chai": "^4.2.0", "core-js": "^2.4.1", "cross-env": "^6.0.0", "eslint": "^6.1.0", "grunt": "^1.0.4", "grunt-cli": "^1.3.2", "grunt-contrib-nodeunit": "^1.0.0", "grunt-karma": "^2.0.0", "isparta-loader": "^2.0.0", "karma": "^1.3.0", "karma-chai": "0.1.0", "karma-chrome-launcher": "^2.0.0", "karma-coverage": "^1.1.1", "karma-firefox-launcher": "^0.1.7", "karma-mocha": "~0.2.0", "karma-phantomjs-launcher": "^0.2.3", "karma-sinon": "^1.0.4", "karma-sinon-chai": "^2.0.2", "karma-source-map-support": "^1.1.0", "karma-webpack": "^1.8.1", "lolex": "^4.2.0", "mocha": "^2.3.2", "phantomjs": "^2.1.7", "sinon": "^2.1.0", "sinon-chai": "^2.8.0", "webpack": "^2.7.0", "webpack-sources": "^1.4.3"}, "directories": {"doc": "doc"}, "dist": {"integrity": "sha512-SkGhc8lgJlyRzjLZ/OjYGqvytdwTeh4HSVSXwJ/TKhgq5K/GoWGR0Kgq2+etzgkQDQb/L5NXNAwrDDIGv0TK5Q==", "shasum": "dfce770795283d0d7be96ad71825733a5f4ec531", "tarball": "https://registry.npmjs.org/@ericblade/quagga2/-/quagga2-0.0.4.tgz", "fileCount": 19, "unpackedSize": 3674331, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdhwhJCRA9TVsSAnZWagAAemUP/RpbGXMBWbU4bVUc+c5T\nu8PCPh3PiaQkq8GV6jL6jvLxUas2Nr5ysNPCJret21t6EHbIBvh8VbSDKgs2\nkhpksKb39N9QqRbADH5552h9XFssJt/iSuBYhV6hN6+0k8BOobJBiq9XEqzE\nVRYeqx6KgebwmBrxku6r4gax+cdQVwW6ru3AQUY9ldc7W8FBiax0DnT5odkg\nfWw8kJK97OpuWUi1R6v+hxNfhtfN7Y4bLyPUzxvILjNiSZgS3o9ccVnmZ/1H\n16+iv2nOYSVDrov3iaUVOpAoqCOvHTalZ9BOaWtFlidrvHWMgQ7UdofdGOnJ\nxtOx/mB1EcWT212EFyFPsR58SC3FB0o6vDpnLnJeYKguELkRwTJ/YP89r6Cm\nE0/gwzMN6broU7orbLBLgEQrlA6Mp2JAGciw6qUujAnejLdtaRlLql3kiCMm\nAtNr9CTsofNd912dJMDWbXlgJb1jFjv84bk2RroGefrk1IqBqWbzbV9mSHpm\noYWNIW3e9jMM5CePmbYxuBhiC6Q4Ch7N69Rkv6YhQD1+XzDOIZ3YVVK9nHVO\nXgfrT5//R7kxjQMzHxaiiq3dhku2iqNAOUbdfwnSXcIs+gFRcz9b5vGr8ylr\nuQDboF4HZTf5l4ooXGXzkVYFvlrq3mCXMnQlghjOTKQ6uWre6qnu3Zi/Ntd3\n1jJx\r\n=ptUj\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCWOrWkHUbZU+aC1+oVZZPMCotwswB24h/OlWI68KXSxgIhAPLIquV/1MdQT2IT7MO4ZITsOhS6aGH1T63NWjzI1I9U"}]}, "engines": {"node": ">= 8.0"}}, "0.0.5": {"name": "@ericblade/quagga2", "version": "0.0.5", "dependencies": {"@babel/runtime": "^7.5.5", "get-pixels": "^3.3.2", "gl-mat2": "^1.0.0", "gl-vec2": "^1.3.0", "gl-vec3": "^1.0.3", "lodash": "^4.17.15", "ndarray": "^1.0.18", "ndarray-linear-interpolate": "^1.0.0", "snyk": "^1.221.0"}, "devDependencies": {"async": "^1.4.2", "@babel/core": "^7.5.5", "@babel/plugin-proposal-class-properties": "^7.5.5", "@babel/plugin-proposal-object-rest-spread": "^7.5.5", "@babel/plugin-transform-runtime": "^7.5.5", "@babel/preset-env": "^7.5.5", "@babel/preset-typescript": "^7.3.3", "babel-loader": "^8.0.6", "babel-plugin-add-module-exports": "^1.0.2", "babel-plugin-istanbul": "^5.2.0", "chai": "^4.2.0", "core-js": "^2.4.1", "cross-env": "^6.0.0", "eslint": "^6.1.0", "grunt": "^1.0.4", "grunt-cli": "^1.3.2", "grunt-contrib-nodeunit": "^1.0.0", "grunt-karma": "^2.0.0", "isparta-loader": "^2.0.0", "karma": "^1.3.0", "karma-chai": "0.1.0", "karma-chrome-launcher": "^2.0.0", "karma-coverage": "^1.1.1", "karma-firefox-launcher": "^0.1.7", "karma-mocha": "~0.2.0", "karma-phantomjs-launcher": "^0.2.3", "karma-sinon": "^1.0.4", "karma-sinon-chai": "^2.0.2", "karma-source-map-support": "^1.1.0", "karma-webpack": "^1.8.1", "lolex": "^4.2.0", "mocha": "^2.3.2", "phantomjs": "^2.1.7", "sinon": "^2.1.0", "sinon-chai": "^2.8.0", "webpack": "^2.7.0", "webpack-sources": "^1.4.3"}, "directories": {"doc": "doc"}, "dist": {"integrity": "sha512-yVbO3vMDRwQUv4POT4Hj7dGGLr6ITpnY1bJ8iWie4l6FAE8BPyVeBVV6AOmm3MVC6ytIGSbiiUHFpJOGHBMimg==", "shasum": "635c501c681ef62cf6925f2b41b43ccb8d7e9d6a", "tarball": "https://registry.npmjs.org/@ericblade/quagga2/-/quagga2-0.0.5.tgz", "fileCount": 11, "unpackedSize": 3669530, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdhwmSCRA9TVsSAnZWagAAU7kQAJsmXbqnLMkplbT9oWPt\n7voQ4mlAK5vhBcDfszj1cYVxiqy/52lmIKzM0yfII1PuMINXB9O1J2hL7hV+\nZft0jcZaHIYtVDZp5Z/YFzCtlsEL4Nnifa10Awd8TQstxFGnIenh/QTl4LeA\ndaiixJoxEd33aovEycLkqcMVCPIrQH+XgubtXquHbDZAZWFfPnftL/qHQIPA\n/9kM44wI6PkZzVG2TrQWuZl+Gl+ZWOuLEiHl5lJq/Oky2uZRk1Rsi5y1AXhX\nk466joYiBLD/x3ggBgjU7TPOVzdutG0SBC9/B6cIJ5wuvLt2kWU9/I9pxfUT\nGpuwQB7vEahTxJ28ip/X4wmkFoSuJeXLLf2mAgZv8sgQzYMeNrqIi2cFDyvE\nkBvCI90aSMk6DOoPnK/YIWWug6YuvjkCKRen818H/WfCGw+RcrnBpvoIp99i\nQX5s/wSkb1EwlGtPw6C7h8SZ7vLWHnsGD9I8PVevG6wRmq78Ir45bBCvIAgq\nn3tvsE8Imb1ieKmDCXfd57SO24JuYyBI6u9/mL45oMufb+jG7qIZ/IAUaJN3\ni22r7Mgb+RGrE1tNA9QmGspUW8RBZRxF5QB/gIocxFSSycHdd6KdYi3KJeIx\nkzHq2zyKm5Z9hb70qWqBtUzt3KG5uimZ6iiLTIFuVIDrCWTrLQf+3wMld+to\nywYp\r\n=64eA\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCH2lD5jw2R+NgV4r514biSggLK8wXXtdwAxqXhUQw90AIhANcNiP31/CHGqfLNEqvRO3wXmXkg9JBWx9IiSoAw2XVv"}]}, "engines": {"node": ">= 8.0"}}, "0.0.6": {"name": "@ericblade/quagga2", "version": "0.0.6", "dependencies": {"@babel/runtime": "^7.5.5", "get-pixels": "^3.3.2", "gl-mat2": "^1.0.0", "gl-vec2": "^1.3.0", "gl-vec3": "^1.0.3", "lodash": "^4.17.15", "ndarray": "^1.0.18", "ndarray-linear-interpolate": "^1.0.0", "snyk": "^1.221.0"}, "devDependencies": {"async": "^1.4.2", "@babel/core": "^7.5.5", "@babel/plugin-proposal-class-properties": "^7.5.5", "@babel/plugin-proposal-object-rest-spread": "^7.5.5", "@babel/plugin-transform-runtime": "^7.5.5", "@babel/preset-env": "^7.5.5", "@babel/preset-typescript": "^7.3.3", "babel-loader": "^8.0.6", "babel-plugin-add-module-exports": "^1.0.2", "babel-plugin-istanbul": "^5.2.0", "chai": "^4.2.0", "core-js": "^2.4.1", "cross-env": "^6.0.0", "eslint": "^6.1.0", "grunt": "^1.0.4", "grunt-cli": "^1.3.2", "grunt-contrib-nodeunit": "^1.0.0", "grunt-karma": "^2.0.0", "isparta-loader": "^2.0.0", "karma": "^1.3.0", "karma-chai": "0.1.0", "karma-chrome-launcher": "^2.0.0", "karma-coverage": "^1.1.1", "karma-firefox-launcher": "^0.1.7", "karma-mocha": "~0.2.0", "karma-phantomjs-launcher": "^0.2.3", "karma-sinon": "^1.0.4", "karma-sinon-chai": "^2.0.2", "karma-source-map-support": "^1.1.0", "karma-webpack": "^1.8.1", "lolex": "^4.2.0", "mocha": "^2.3.2", "phantomjs": "^2.1.7", "sinon": "^2.1.0", "sinon-chai": "^2.8.0", "webpack": "^2.7.0", "webpack-sources": "^1.4.3"}, "directories": {"doc": "doc"}, "dist": {"integrity": "sha512-yyTVsk1D4x92lOuyQP7h9kFmYANjBOXCuBXCAVtYfgY+ozZmTYxRGj+DEUTXzMZaWO9YB9gEdj8Xaq8tHrPP1g==", "shasum": "35dd032c7f20f8fb5b12dcfd00b4265eaae43269", "tarball": "https://registry.npmjs.org/@ericblade/quagga2/-/quagga2-0.0.6.tgz", "fileCount": 11, "unpackedSize": 3670487, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdlTexCRA9TVsSAnZWagAAldwQAKQKAm7NqFxYBo0gevaC\nQNnPIwYFWFv4OzUYr9tEic97esFAKaHooeiGEZyC83pE2zdJkQHN+QqbHTxf\noUooDXaKoMwX9DsZhqvqYSXylDTOm8XNPFbVpqTJsT1/2Yevw91OrSbGvSGj\nFdVOY5RSKY4CmXzU4BSM04dBu67zU7glXfmE5bVbt0AjUO6YNDNu6pHwFihk\nN6jRuf+7RTPfFk3fao0e8VBKs8pEVcw+nKsTls/EoIzPP22vcD5oZ1+Pl8n/\nSyEo8QHWzmLzYaAEv5FUBSDi8AqW7Ft4Cijdh/ypxr6C3Wz62hFu+h1OX4UO\nf3EhcGQ8AfFlLQYhJKZDe1ArB72BUtUZ1/ZHtNBIgfKqjz6Q7Vg3rsQbR3jn\nKkN9Fv8uZcqQLwr6U7t4gXj6wlv7SAiLcEpdQ7KJjF8u3isU2Dg1jc+3/ntS\nlmN4gvls9Pn3umQpU8PPowguWbTxwnKmXdjDhguZC3izC2A0kdtACGkp3gW4\ngoOFZzd1TVOfKq7Nzly3xR6Su5qdQgZj2LCtoXUXbhh6xAjoCOEvwA0qHwWK\n+iLwyztI1F14GYPeNhsj7KzWPZhllLGn/YdncwDsGp9R/zTc2m42RqGhtAVp\nakdO8iliNsrvOzY7Y86W+dl4uiFTfZ6DUcK8qE5KUH9XLw0t59cHkzFBnGbf\nhpV2\r\n=BKb9\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDgAhzPLp8KMONnOI7dbiD9Lf7y+errsqbBUH8JpratvQIhAPToz/Dr4S3lnJBdYdRFqFLfYijrybTrIGkcpveA+FJA"}]}, "engines": {"node": ">= 8.0"}}, "0.0.7": {"name": "@ericblade/quagga2", "version": "0.0.7", "dependencies": {"@babel/runtime": "^7.5.5", "get-pixels": "^3.3.2", "gl-mat2": "^1.0.0", "gl-vec2": "^1.3.0", "gl-vec3": "^1.0.3", "lodash": "^4.17.15", "ndarray": "^1.0.18", "ndarray-linear-interpolate": "^1.0.0", "snyk": "~1.229.0"}, "devDependencies": {"async": "^1.4.2", "@babel/core": "^7.5.5", "@babel/plugin-proposal-class-properties": "^7.5.5", "@babel/plugin-proposal-object-rest-spread": "^7.5.5", "@babel/plugin-transform-runtime": "^7.5.5", "@babel/preset-env": "^7.5.5", "@babel/preset-typescript": "^7.3.3", "babel-loader": "^8.0.6", "babel-plugin-add-module-exports": "^1.0.2", "babel-plugin-istanbul": "^5.2.0", "chai": "^4.2.0", "core-js": "^2.4.1", "cross-env": "^6.0.0", "eslint": "^6.1.0", "grunt": "^1.0.4", "grunt-cli": "^1.3.2", "grunt-contrib-nodeunit": "^1.0.0", "grunt-karma": "^2.0.0", "isparta-loader": "^2.0.0", "karma": "^1.3.0", "karma-chai": "0.1.0", "karma-chrome-launcher": "^2.0.0", "karma-coverage": "^1.1.1", "karma-firefox-launcher": "^0.1.7", "karma-mocha": "~0.2.0", "karma-phantomjs-launcher": "^0.2.3", "karma-sinon": "^1.0.4", "karma-sinon-chai": "^2.0.2", "karma-source-map-support": "^1.1.0", "karma-webpack": "^1.8.1", "lolex": "^5.0.0", "mocha": "^2.3.2", "phantomjs": "^2.1.7", "sinon": "^2.1.0", "sinon-chai": "^2.8.0", "webpack": "^2.7.0", "webpack-sources": "^1.4.3"}, "directories": {"doc": "doc"}, "dist": {"integrity": "sha512-vFevtjzYCoZKhgd/yg2BLdK9q+sPNwlMpNEH/Oumm71ivDm4IeGgC9NTRq5+OGeR0l2AVJs8EAywk1aFhuAdiQ==", "shasum": "41de84dadab3829fd56c490c1279591ffcc86c91", "tarball": "https://registry.npmjs.org/@ericblade/quagga2/-/quagga2-0.0.7.tgz", "fileCount": 11, "unpackedSize": 3671297, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdni6dCRA9TVsSAnZWagAAr6AP/AzSMJBhDfCOxj2P4UZn\nj36e7p0O4ry09ApGHQ+OHKEzLIkXePzh+Qee2pzNH8NtF4fGKqcBh6HWCXAM\n/pnun9AjTUi0IKIYgWPko0pdZofoWpUznMJwj3QFxdEZEDqFz/jzU/Yx7zFL\n9MOkwjX8wcmt5crcIIujZUNZWZqKu9kyWFkvugWKAlgl9zQpf9aTZUsIbKky\nfUh9IyOtpaT+RxZf+ytDzj5mDCgdtYxA36oxiLU31GYakoYEMnx8Ry0nBbw8\n1iiOqBCmRysvFsUd76Ac5bTR8vEmkvG5NvP4bvzVxBb9MgACc95TgOMRpKbH\nG9HHSaDVe5BECCPusX+lpruaUhafllWsPI4FTwg3kovqW788q5bvxxLZmZR4\nyj2W2jcKUEMhgStNoBm1zAe6lDuUuFBO6+pl9d7KU8F0RBhZFJls6pph3Ypv\nx8DSZnK1TRwC7XtzWp1NDxr09GfG+mV3zAFbNXoT2F3oPhyzDfF8faiM+Lm/\nOidt6NQ7gMJr6TsqmfzB8FXwME2Yk1sqE7WeaY0JKxdGRrizeunZIKlwVOIL\nFDGNxEqgtPXywEdTAWXsIlNdX/Zsty30YUuNJlPxVM8jtPIy4F9PrRmV4R0O\nrOEChj6dvvIVVBKjkdZq9qh8OtvXG97XwHfYBRdSjWTKByv9UuZcA7dCkBYM\nwaHx\r\n=ba3r\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGXCOK5pm3yL+2GNVs+p32wlalB6rc1MXd+Rd/UrPTT2AiEA8/g3x2KD582+yf5/wlIHP9DAbLbUKXtzU+s0w/vIVBU="}]}, "engines": {"node": ">= 8.0"}}, "0.0.8": {"name": "@ericblade/quagga2", "version": "0.0.8", "dependencies": {"@babel/runtime": "^7.5.5", "get-pixels": "^3.3.2", "gl-mat2": "^1.0.0", "gl-vec2": "^1.3.0", "gl-vec3": "^1.0.3", "lodash": "^4.17.15", "ndarray": "^1.0.18", "ndarray-linear-interpolate": "^1.0.0", "snyk": "~1.258.1"}, "devDependencies": {"async": "^1.4.2", "@babel/core": "^7.5.5", "@babel/plugin-proposal-class-properties": "^7.5.5", "@babel/plugin-proposal-object-rest-spread": "^7.5.5", "@babel/plugin-transform-runtime": "^7.5.5", "@babel/preset-env": "^7.5.5", "@babel/preset-typescript": "^7.3.3", "babel-loader": "^8.0.6", "babel-plugin-add-module-exports": "^1.0.2", "babel-plugin-istanbul": "^5.2.0", "chai": "^4.2.0", "core-js": "^2.4.1", "cross-env": "^6.0.0", "eslint": "^6.1.0", "grunt": "^1.0.4", "grunt-cli": "^1.3.2", "grunt-contrib-nodeunit": "^1.0.0", "grunt-karma": "^2.0.0", "isparta-loader": "^2.0.0", "karma": "^1.3.0", "karma-chai": "0.1.0", "karma-chrome-launcher": "^2.0.0", "karma-coverage": "^1.1.1", "karma-firefox-launcher": "^0.1.7", "karma-mocha": "~0.2.0", "karma-phantomjs-launcher": "^0.2.3", "karma-sinon": "^1.0.4", "karma-sinon-chai": "^2.0.2", "karma-source-map-support": "^1.1.0", "karma-webpack": "^1.8.1", "lolex": "^5.0.0", "mocha": "^2.3.2", "phantomjs": "^2.1.7", "sinon": "^2.1.0", "sinon-chai": "^2.8.0", "webpack": "^2.7.0", "webpack-sources": "^1.4.3"}, "directories": {"doc": "doc"}, "dist": {"integrity": "sha512-Vwt2WvEmSDyQjaO5lHHRJMCCMPOnWC9ql+zX8knG/Yu+FAWZu98Ov/+VWN2OBEqgBVNa9P3EO07OEnAu2SA8PQ==", "shasum": "e2ee7e564387e93810e83d1f021ceda7ee0e731e", "tarball": "https://registry.npmjs.org/@ericblade/quagga2/-/quagga2-0.0.8.tgz", "fileCount": 10, "unpackedSize": 3675303, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd8PluCRA9TVsSAnZWagAAX+AQAI6s3EB3rg6TZLGtGmwh\ntUgj+mQQeRgNDlFnKtW5HDOjnilzwmASEHpmCxgOQEPvKjj+r+okv2M6Zw0c\nZKe3FZ1IMKJfyJcgvlF5T6UaN+/Ol83Eaz0N5kIfoMUzpoQ4TRQwC7Qh7YsC\nL0gW2eKyNggsKqOHWPoXl/BBRKn0OpdpGm4ACdMd2MuiYcfCq8kMXS4Mzdvh\nrie+lqLgE5I49sXglgQ4c53d/3pqvVZNEA9BO2AaagNhx0rfm7bq88R/Gpbw\nxltESYRoX65gLhuia8aUeMhi/epBoakM3qM+Y5NTUbiDXtbM0xjkFEcxnffE\nFWPRadiQuv+dPxNDWfb/vMsi2vcAvzVjJbLP18mbjC5LJZwLVHbC9RXCb4eG\n6Wib9O46FmC8tk3y/aefKZv+1Hko6NeG/LnTdaza402dZ9TG7dcvp6g/D5M2\niYUWYQdVpQ6QqWRtvtSLWJx/lDyOaCiy+UXFUcKjNgpEkP4ubUnQRc6TEX2f\nKwtJFpL4RLKcjFcWOQPlJqqhYm4yW4jIcCT9csMMjTolffJpkeKvQt/w4Y8O\n+7AAQCqY8ch6B0gXz9huRsgo4yNm4dwZs24/VGPJ7yd63GXUCNfocNfjMkDD\nJxIM7GSbcMAJtBHns8eTIyE8utlCiitGmb+YTnp3hGk1sh5UqPnpOFpGhZiS\nxpZA\r\n=ZH6O\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDh/9oHTfKVjYWeuS8jkYuJDJlfH/Hf/0+ddyuhSaaL7AIgAKU7kYowB4TbRqUZ8PHUqbJzhYZaU9wTK7YS2YNDc04="}]}, "engines": {"node": ">= 8.0"}}, "0.0.9": {"name": "@ericblade/quagga2", "version": "0.0.9", "dependencies": {"@babel/runtime": "^7.5.5", "get-pixels": "^3.3.2", "gl-mat2": "^1.0.0", "gl-vec2": "^1.3.0", "gl-vec3": "^1.0.3", "lodash": "^4.17.15", "ndarray": "^1.0.18", "ndarray-linear-interpolate": "^1.0.0", "snyk": "~1.259.0"}, "devDependencies": {"async": "^1.4.2", "@babel/core": "^7.5.5", "@babel/plugin-proposal-class-properties": "^7.5.5", "@babel/plugin-proposal-object-rest-spread": "^7.5.5", "@babel/plugin-transform-runtime": "^7.5.5", "@babel/preset-env": "^7.5.5", "@babel/preset-typescript": "^7.3.3", "babel-loader": "^8.0.6", "babel-plugin-add-module-exports": "^1.0.2", "babel-plugin-istanbul": "^5.2.0", "chai": "^4.2.0", "core-js": "^2.4.1", "cross-env": "^6.0.0", "eslint": "^6.1.0", "grunt": "^1.0.4", "grunt-cli": "^1.3.2", "grunt-contrib-nodeunit": "^1.0.0", "grunt-karma": "^2.0.0", "isparta-loader": "^2.0.0", "karma": "^1.3.0", "karma-chai": "0.1.0", "karma-chrome-launcher": "^2.0.0", "karma-coverage": "^1.1.1", "karma-firefox-launcher": "^0.1.7", "karma-mocha": "~0.2.0", "karma-phantomjs-launcher": "^0.2.3", "karma-sinon": "^1.0.4", "karma-sinon-chai": "^2.0.2", "karma-source-map-support": "^1.1.0", "karma-webpack": "^1.8.1", "lolex": "^5.0.0", "mocha": "^2.3.2", "phantomjs": "^2.1.7", "sinon": "^2.1.0", "sinon-chai": "^2.8.0", "webpack": "^2.7.0", "webpack-sources": "^1.4.3"}, "directories": {"doc": "doc"}, "dist": {"integrity": "sha512-3d3FEfIlN8X/W4/ioYYxvthWlw8gR/hlA6yg5ahnhPf/Iex/6lZC24pvhwTtjxvr7Cx+wcGNH9CPh+fSlCZxVw==", "shasum": "ac8ea569a529f741de8a14bbe3cd39b292d24f42", "tarball": "https://registry.npmjs.org/@ericblade/quagga2/-/quagga2-0.0.9.tgz", "fileCount": 10, "unpackedSize": 3674374, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd8rvRCRA9TVsSAnZWagAAP7MQAJ9Hq1ND59/8RpAy7N/S\n+PR+gp4gk1T8S1qNH2v7YASwaNt8tSiA6T0OzHXVXV6gSyW5dyGveii8ibBr\nPjvQhqmgRmpfWM3zXOcTr3MAoyO73fYL/1kZGdH167j7Rp1J0WkVjRzu0/vk\nfnyfbhsoLf16OiCcq078x0tvX42bgVo0QU9c/hZYJzd9DMYe7ZIownF5+VEG\np2dtuCwz7tIP7tBr0lTDvAJ6JmOJ9uvDHJ6YC1Q2bvalMo9475vCdWT3Zj2P\nCp3kVTftihD/Njq2MgchUIdcf1g+YFqkR1+xIgQdKifApKZV1X6djDCf7uwT\n4xhBBLePTfnQrvCwTS/l86K7t9CaBXxqwtcOYPYNcOJ2nnwPYMeMQSoXF89g\ntSJolNjXK5tU/yqCbHTSNnrcA4+QoVRvUTuj91+CA+ubMpPde8UgG4XcZKWB\nZOFd/GUznAa08Bj/lYXnMobRg5Bzih7DKp/2BM9KXW2lxi2d0pP4s3b/goWa\nvwQecDnxpS3VeeEcaQRQiTfRCGzeJnEJYwEzGVuiDxsrsHvRiQ9YTOxPjvUl\nd5OATnFQ1byYnd8tAzLFV0iRJJ5xLomCnbZnbU/oRaeU20pZHMatv+SodtTy\ne6sSSpPso2zqWb3mv980hb3zhgAV2bM1ru/t6a289bU0eOgniPpefpPvldTX\ny+S4\r\n=xRNE\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD7tw++8/3NmtUhqWb1BKbHmo0ZxiwxRAaDrAmN9FHA2gIhAJ1gWKx2IlDKWQNPZ3gZprK80VRJnzQeFPDJip14ogtu"}]}, "engines": {"node": ">= 8.0"}}, "0.0.10": {"name": "@ericblade/quagga2", "version": "0.0.10", "dependencies": {"@babel/runtime": "^7.5.5", "get-pixels": "^3.3.2", "gl-mat2": "^1.0.0", "gl-vec2": "^1.3.0", "gl-vec3": "^1.0.3", "lodash": "^4.17.15", "ndarray": "^1.0.18", "ndarray-linear-interpolate": "^1.0.0", "snyk": "~1.260.0"}, "devDependencies": {"async": "^1.4.2", "@babel/core": "^7.5.5", "@babel/plugin-proposal-class-properties": "^7.5.5", "@babel/plugin-proposal-object-rest-spread": "^7.5.5", "@babel/plugin-transform-runtime": "^7.5.5", "@babel/preset-env": "^7.5.5", "@babel/preset-typescript": "^7.3.3", "babel-loader": "^8.0.6", "babel-plugin-add-module-exports": "^1.0.2", "babel-plugin-istanbul": "^5.2.0", "chai": "^4.2.0", "core-js": "^2.4.1", "cross-env": "^6.0.0", "eslint": "^6.1.0", "grunt": "^1.0.4", "grunt-cli": "^1.3.2", "grunt-contrib-nodeunit": "^1.0.0", "grunt-karma": "^2.0.0", "isparta-loader": "^2.0.0", "karma": "^1.3.0", "karma-chai": "0.1.0", "karma-chrome-launcher": "^2.0.0", "karma-coverage": "^1.1.1", "karma-firefox-launcher": "^0.1.7", "karma-mocha": "~0.2.0", "karma-phantomjs-launcher": "^0.2.3", "karma-sinon": "^1.0.4", "karma-sinon-chai": "^2.0.2", "karma-source-map-support": "^1.1.0", "karma-webpack": "^1.8.1", "lolex": "^5.0.0", "mocha": "^2.3.2", "phantomjs": "^2.1.7", "sinon": "^2.1.0", "sinon-chai": "^2.8.0", "webpack": "^2.7.0", "webpack-sources": "^1.4.3"}, "directories": {"doc": "doc"}, "dist": {"integrity": "sha512-tgwMNPDiCaUGvC6CWi1auW5D4CAxV754kcWZZSq5IP2UDBCZpAjdHCqYJjRzj6XG2mew/urr0ud3F/PuY0XrTA==", "shasum": "d529b37b82913429f937af5f70697abfbf482ad2", "tarball": "https://registry.npmjs.org/@ericblade/quagga2/-/quagga2-0.0.10.tgz", "fileCount": 10, "unpackedSize": 3676923, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd9eiUCRA9TVsSAnZWagAAA+QP/1Db5L/SZIWLqdiUHTZS\nou5tXbVJNM9cwBo/UN0QUW2+uTcrWy4SFcaMIGFwdG7xv2iIoOoy6Qc4QEkS\nEDTzEKAN5LL/sfdfwj1gnz5o7asB2bJyf4UrnVp91tTrRnlzFSej68dDISBe\n/bISUenOF4oOlApYqZT1RgmooFg3QDIQMO/qLtnRZ6FCmfRqBbOY8XRI/ZT1\nDwYQ0NI8bCc6bSKb6B/IrsGb+7TIecd+UPeW5mNmMEvBmk0UWg6z8m1d4QjO\nOAvB1gU4HYdUvAVgfQazWqNIpPRZyTa3gPdBEq7mDLfCtcX2x+lka7dR/A/B\nnbUkGJIirt9dxzvi7Q/aOxqpvEO/bIXPNecDgkvYmCVmzY/pJ1rBSKLrHc5S\nwmCmvlggTZHxYH7L8MbdN5w1+XMgU3Ev5CgMgM4kTx04fsN3Nemo1AjpasvB\n/vTLAT/IbjBqIvxsJfhKaoh3ob+LAeSspagKv3AhCyN8h9OfZ6/iIKYh4yYl\nLj038/VOwsKtn+uMVPy+EFNyfCO7rv5EDLBW7ISpyuoMKNk2hpbtsL90flIT\nlJRnsMw1Y8YKdZzqnC7/SOu2o60z+qpXFFRYWoQs+720JUqCjxuQ3U8ehj4F\nQWmRE9gJQ5E4tfcPjOEwXJ9qDMXPkFJ5qQ3J9+7pDxUN7EdgVbAGGt7/TNUm\n59am\r\n=PJSI\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIF//lk7jYR4MS0s+8e4Vx3tszHtNDCR6TlstJ36ozrfgAiEAk1BB74ZceHEIHObhqdLqkGjUDnOaQVtbah71EjKFn+E="}]}, "engines": {"node": ">= 8.0"}}, "0.0.11": {"name": "@ericblade/quagga2", "version": "0.0.11", "dependencies": {"@babel/polyfill": "^7.7.0", "get-pixels": "^3.3.2", "gl-mat2": "^1.0.1", "gl-vec2": "^1.3.0", "gl-vec3": "^1.1.3", "lodash": "^4.17.15", "ndarray": "^1.0.18", "ndarray-linear-interpolate": "^1.0.0", "snyk": "~1.266.0", "webpack-cli": "^3.3.10"}, "devDependencies": {"@babel/core": "^7.7.7", "@babel/plugin-proposal-class-properties": "^7.7.4", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.7.4", "@babel/plugin-proposal-object-rest-spread": "^7.7.7", "@babel/plugin-proposal-optional-chaining": "^7.7.5", "@babel/plugin-transform-runtime": "^7.7.6", "@babel/preset-env": "^7.7.7", "@babel/preset-typescript": "^7.7.7", "@babel/runtime": "^7.7.7", "@types/chai": "^4.2.7", "@types/gl-vec2": "^1.3.0", "@types/lodash": "^4.14.149", "@types/mocha": "^5.2.7", "@types/react": "^16.9.17", "@types/sinon": "^7.5.1", "@types/sinon-chai": "^3.2.3", "async": "^1.4.2", "babel-loader": "^8.0.6", "babel-plugin-add-module-exports": "^1.0.2", "babel-plugin-istanbul": "^6.0.0", "chai": "^4.2.0", "core-js": "^3.6.1", "cross-env": "^6.0.3", "eslint": "^6.8.0", "grunt": "^1.0.4", "grunt-cli": "^1.3.2", "grunt-contrib-nodeunit": "^1.0.0", "grunt-karma": "^2.0.0", "isparta-loader": "^2.0.0", "karma": "^1.3.0", "karma-chai": "0.1.0", "karma-chrome-launcher": "^2.0.0", "karma-coverage": "^1.1.1", "karma-firefox-launcher": "^0.1.7", "karma-mocha": "~0.2.0", "karma-phantomjs-launcher": "^0.2.3", "karma-sinon": "^1.0.5", "karma-sinon-chai": "^2.0.2", "karma-source-map-support": "^1.4.0", "karma-webpack": "^4.0.2", "lolex": "^5.1.2", "mocha": "^2.3.2", "phantomjs": "^2.1.7", "sinon": "5.1.1", "sinon-chai": "^3.4.0", "source-map-loader": "^0.2.4", "typescript": "^3.7.4", "webpack": "^4.41.5", "webpack-cli": "^3.3.10", "webpack-sources": "^1.4.3"}, "directories": {"doc": "doc"}, "dist": {"integrity": "sha512-kdq0PxPCg4JiAEVvxyjewUgxxf/xjRvr9iGCbYR/Ar7yuHFvFxdjWs2PfYGOhwk+xXkzFVaXDmVjE7MKCujJIg==", "shasum": "f00010002429d6ae2b64412eaf3b57604e466718", "tarball": "https://registry.npmjs.org/@ericblade/quagga2/-/quagga2-0.0.11.tgz", "fileCount": 8, "unpackedSize": 6366740, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeCN0VCRA9TVsSAnZWagAAmpcP/09YQ8lHJcD3DqDSpleh\npi7jT+8Qqlyz5yH5aCUfL/Xw+aM16uvC3Hpa7hZ0RC6WgQgjaWTstZaSPvOm\nDQ+fmRPrnxQw2U7OiCOfbCyrJVFUDZgy+qVvttFr0yALvqu99A1adMAXgQme\nAJE4FMzVB9GuUi4hFpUskU8Kqh6x55gOl8yarS5ShxmJAMFFR0NB0fHRzcFA\ndSdAJEm91K3ENL+8UfrP+N45OuGmka2jgzmpai1pq16XL/5L0AWTTLRlAPTA\nv5EAscxXv3MWxCZ9WRVEZOR43wSHkd/1XenVtSQudWXGXHaffQHi2BJljpcx\nIhIXK9uzvgwofY936FMPPGV8LU/RRw5YI1bndtdSbmN6nAZYQec1lA5wSjaY\ntpKrE4edLqnYQEhJD9VgI+VhISJO50/KVhH+mZ4NdQvv3sQJBPP1T/vmDTd1\nGZg4KpbQW07F4UNJ7yCCl0QI/IWsNG34vGZvnoS7WA4ErTZ1Bb2rnOnO0kMg\nMJGJPmndvR5N8ER5MS6Rr0Ko2Gj7rkI2GAvL3eWVAd0Ak5D282vxI7R7pcXa\nprztkgZORjM9m55Cc5PLqyH9NyhaPnRkZjkmrHyG0pxZxnIaBpKDpueWC9kx\nGvpD2BzgafUJ83kAp0ynAzjhgH7JuNOI/qCARGGDBDkD/cgVwlKWbbDDFyzO\nQHtc\r\n=xax1\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCOi+N+fR7/TYc04whP5RZUa/wmHN3lxUJComoITblE5gIhAPPL7gGU6ukZG+Fw+VGU6qQD58/IAOgrKPgXE+YOd3ay"}]}, "engines": {"node": ">= 8.0"}}, "0.0.12": {"name": "@ericblade/quagga2", "version": "0.0.12", "dependencies": {"@babel/polyfill": "^7.7.0", "get-pixels": "^3.3.2", "gl-mat2": "^1.0.1", "gl-vec2": "^1.3.0", "gl-vec3": "^1.1.3", "lodash": "^4.17.15", "ndarray": "^1.0.18", "ndarray-linear-interpolate": "^1.0.0", "snyk": "~1.267.0", "webpack-cli": "^3.3.10"}, "devDependencies": {"@babel/core": "^7.7.7", "@babel/plugin-proposal-class-properties": "^7.7.4", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.7.4", "@babel/plugin-proposal-object-rest-spread": "^7.7.7", "@babel/plugin-proposal-optional-chaining": "^7.7.5", "@babel/plugin-transform-runtime": "^7.7.6", "@babel/preset-env": "^7.7.7", "@babel/preset-typescript": "^7.7.7", "@babel/runtime": "^7.7.7", "@types/chai": "^4.2.7", "@types/gl-vec2": "^1.3.0", "@types/lodash": "^4.14.149", "@types/mocha": "^5.2.7", "@types/react": "^16.9.17", "@types/sinon": "^7.5.1", "@types/sinon-chai": "^3.2.3", "async": "^1.4.2", "babel-loader": "^8.0.6", "babel-plugin-add-module-exports": "^1.0.2", "babel-plugin-istanbul": "^6.0.0", "chai": "^4.2.0", "core-js": "^3.6.1", "cross-env": "^6.0.3", "eslint": "^6.8.0", "grunt": "^1.0.4", "grunt-cli": "^1.3.2", "grunt-contrib-nodeunit": "^1.0.0", "grunt-karma": "^2.0.0", "isparta-loader": "^2.0.0", "karma": "^1.3.0", "karma-chai": "0.1.0", "karma-chrome-launcher": "^2.0.0", "karma-coverage": "^1.1.1", "karma-firefox-launcher": "^0.1.7", "karma-mocha": "~0.2.0", "karma-phantomjs-launcher": "^0.2.3", "karma-sinon": "^1.0.5", "karma-sinon-chai": "^2.0.2", "karma-source-map-support": "^1.4.0", "karma-webpack": "^4.0.2", "lolex": "^5.1.2", "mocha": "^2.3.2", "phantomjs": "^2.1.7", "sinon": "5.1.1", "sinon-chai": "^3.4.0", "source-map-loader": "^0.2.4", "typescript": "^3.7.4", "webpack": "^4.41.5", "webpack-cli": "^3.3.10", "webpack-sources": "^1.4.3"}, "directories": {"doc": "doc"}, "dist": {"integrity": "sha512-/kZmT6JcqRpafEfp6VON4i1sd+dBtLamKpry+nbB2Ms9vmXC7Uzim5O3paAMQUJLom/HgxS80bbhTl7ZvvZhVw==", "shasum": "627a0c79e52543e7bec88eb40e468652891d41ba", "tarball": "https://registry.npmjs.org/@ericblade/quagga2/-/quagga2-0.0.12.tgz", "fileCount": 8, "unpackedSize": 6352200, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeCYvYCRA9TVsSAnZWagAAfzAP+wRsKNWKWJOOjeTpHnBi\nRHzBOqLyu78YPtO/pJJ6ZLbV9cKDHAUDNS+sA4CYbCebNugL64RG+rT4h8ge\nZWxYPYLaj7mlVnkAZWf2CdehcdEMDFsldBHYiepvNtckQIBflyeeXVUISwZE\n6HXrGVN7S6ZWakyd61evIcPiMahWT9pR78qd/HYDY64M6DVoiONQ+NtOX6gn\nm8W1YTN0TaMabWUEA+uMtsZnjIlVtkAR11wBYN20yjfJHlGqdLG+SRc06//m\nwNdbF8trs/9cpmAW6LjDHjVGRGfO3kgHKC1CD7OLKe7P39l+KCBE/O0a/Vit\ncUX7e+eMWjdjitne0AyUqz11X3sbAPcp40hIzCJRkPMVKV+ryQH4rHhAS6Ry\nha51yeSLe/mMoLtJInhGFU1yR03HIwYfHWKiPJA1//IRy+OuEy/3pmEDEacS\ny6rf5dIfwwsY9PuDg5c6SSf9EqK7kxO5F1T7jsTTJGfBvSbA/je2RKKVfYks\n6/ST2RM35C+noYQmxnLBNtlE3KEs+0x5h9TC3zaxo0pZCZ0DG0X64rRTSBzo\nXX0/jpqKYrBHUwqT/eEP7uRvtKJCriazOgS6Fn37Xca75EJ3uLLA7N5vWAKI\nKtdBUbI1kac+ml5TuiYcPRp/vuvKMzcp9epR9gciiHhBOX8VVV2qQncPMoSv\nR9Tc\r\n=cV0i\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCvHB01igKpC/dSA8lwfrBIInQdmxlifU4hlJhYJ4pcoQIhAJBgKCFWlQ+T118qILNzNCv0T49n9TCDb6uOrHzAreT9"}]}, "engines": {"node": ">= 8.0"}}, "0.0.13": {"name": "@ericblade/quagga2", "version": "0.0.13", "dependencies": {"@babel/polyfill": "^7.7.0", "get-pixels": "^3.3.2", "gl-mat2": "^1.0.1", "gl-vec2": "^1.3.0", "gl-vec3": "^1.1.3", "lodash": "^4.17.15", "ndarray": "^1.0.18", "ndarray-linear-interpolate": "^1.0.0", "snyk": "~1.268.0", "webpack-cli": "^3.3.10"}, "devDependencies": {"@babel/core": "^7.7.7", "@babel/plugin-proposal-class-properties": "^7.7.4", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.7.4", "@babel/plugin-proposal-object-rest-spread": "^7.7.7", "@babel/plugin-proposal-optional-chaining": "^7.7.5", "@babel/plugin-transform-runtime": "^7.7.6", "@babel/preset-env": "^7.7.7", "@babel/preset-typescript": "^7.7.7", "@babel/runtime": "^7.7.7", "@types/chai": "^4.2.7", "@types/gl-vec2": "^1.3.0", "@types/lodash": "^4.14.149", "@types/mocha": "^5.2.7", "@types/react": "^16.9.17", "@types/sinon": "^7.5.1", "@types/sinon-chai": "^3.2.3", "async": "^1.4.2", "babel-loader": "^8.0.6", "babel-plugin-add-module-exports": "^1.0.2", "babel-plugin-istanbul": "^6.0.0", "chai": "^4.2.0", "core-js": "^3.6.1", "cross-env": "^6.0.3", "eslint": "^6.8.0", "grunt": "^1.0.4", "grunt-cli": "^1.3.2", "grunt-contrib-nodeunit": "^1.0.0", "grunt-karma": "^2.0.0", "isparta-loader": "^2.0.0", "karma": "^1.3.0", "karma-chai": "0.1.0", "karma-chrome-launcher": "^2.0.0", "karma-coverage": "^1.1.1", "karma-firefox-launcher": "^0.1.7", "karma-mocha": "~0.2.0", "karma-phantomjs-launcher": "^0.2.3", "karma-sinon": "^1.0.5", "karma-sinon-chai": "^2.0.2", "karma-source-map-support": "^1.4.0", "karma-webpack": "^4.0.2", "lolex": "^5.1.2", "mocha": "^2.3.2", "phantomjs": "^2.1.7", "sinon": "5.1.1", "sinon-chai": "^3.4.0", "source-map-loader": "^0.2.4", "typescript": "^3.7.4", "webpack": "^4.41.5", "webpack-cli": "^3.3.10", "webpack-sources": "^1.4.3"}, "directories": {"doc": "doc"}, "dist": {"integrity": "sha512-3Yr5N7uIeEFhWL/wjckjVzMXznGo+c1sEmeJoYRHH4O8T1Ka9bwVYe9qboPwz/W05XDTD0SzEUw811pPPuqKKw==", "shasum": "364cae8266c0d2919375f4b1b9b69c1e851a89dd", "tarball": "https://registry.npmjs.org/@ericblade/quagga2/-/quagga2-0.0.13.tgz", "fileCount": 9, "unpackedSize": 6369771, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeCvVgCRA9TVsSAnZWagAAKPEP/0JtrZoXCfQjOv/oL/6t\nL7dIBykwuo3HcdsGyuj3u4tC9iwUyRMFRHOo5WHJXCL0fUCAhvgeppfdIpWT\niEJJInr466XwTFRaa+6uoSQgQ5LPDjmFNsMwgwQV1MclIWyAWh11XQNoa3Ie\nfHe3p+v8yWToLksU9ZnqN153sN3ROESc26uq9wpbXT4prXCB68XOzbaacHCD\nQmW9W7SKVbj7S2F8vqkPbXt0omcn8b5YfIz4gTcBFk/ZTAGX3g6aUBk7TRiB\nGIeiDYcDUbxMmH2WhwG0V+GeyhZrrcTBgchfk583cRbnm/3eO3qtZu8/4ePp\nhg1kAHEIbteeVKQLsZ8/zCaJraoSGCDGCTTdRwK599wzs1x9tzNWsGwLbcb9\nbzgDYdiulOKvlXccnevt20kgkx+wyMJ86vcptGxSkjeJnNYeVbmEGZWRxcqk\n/D2X3NKVfM5r4ODk1VY26o+jxRrxWVwZWeMRYBiHQSHnPj2GWa1KgV8Ph0fn\ncI5aDgwoaF3hPXnhB6BVZI6X6TRR2fG5dBA7c9pRjmKg1R3rRCYxbT0VL9kY\nymZgBp/baFcaoZmSrdR1+m+jtEen8HVZK7K5QYy9KgE/qMZDRDV964J0w9FI\n0JUwrpP7sxc63zF62fl+S7X5F8I8e84k6AotGmq/AeDOqdDodsfHYwUwx5Vm\nHA2Y\r\n=b90M\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDctiZHoJWaHJ5stqAc7pU0D8a3LiDrYObiREFNNT8dLgIgJocFMS3EPiwWiJt1EBHyP5NQEoeqJ+HBC8gDDupn0XU="}]}, "engines": {"node": ">= 8.0"}}, "0.0.14": {"name": "@ericblade/quagga2", "version": "0.0.14", "dependencies": {"@babel/polyfill": "^7.7.0", "get-pixels": "^3.3.2", "gl-mat2": "^1.0.1", "gl-vec2": "^1.3.0", "gl-vec3": "^1.1.3", "lodash": "^4.17.15", "ndarray": "^1.0.18", "ndarray-linear-interpolate": "^1.0.0", "snyk": "~1.268.0", "webpack-cli": "^3.3.10"}, "devDependencies": {"@babel/core": "^7.7.7", "@babel/plugin-proposal-class-properties": "^7.7.4", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.7.4", "@babel/plugin-proposal-object-rest-spread": "^7.7.7", "@babel/plugin-proposal-optional-chaining": "^7.7.5", "@babel/plugin-transform-runtime": "^7.7.6", "@babel/preset-env": "^7.7.7", "@babel/preset-typescript": "^7.7.7", "@babel/runtime": "^7.7.7", "@types/chai": "^4.2.7", "@types/gl-vec2": "^1.3.0", "@types/lodash": "^4.14.149", "@types/mocha": "^5.2.7", "@types/react": "^16.9.17", "@types/sinon": "^7.5.1", "@types/sinon-chai": "^3.2.3", "async": "^1.4.2", "babel-loader": "^8.0.6", "babel-plugin-add-module-exports": "^1.0.2", "babel-plugin-istanbul": "^6.0.0", "chai": "^4.2.0", "core-js": "^3.6.1", "cross-env": "^6.0.3", "eslint": "^6.8.0", "grunt": "^1.0.4", "grunt-cli": "^1.3.2", "grunt-contrib-nodeunit": "^1.0.0", "grunt-karma": "^2.0.0", "isparta-loader": "^2.0.0", "karma": "^1.3.0", "karma-chai": "0.1.0", "karma-chrome-launcher": "^2.0.0", "karma-coverage": "^1.1.1", "karma-firefox-launcher": "^0.1.7", "karma-mocha": "~0.2.0", "karma-phantomjs-launcher": "^0.2.3", "karma-sinon": "^1.0.5", "karma-sinon-chai": "^2.0.2", "karma-source-map-support": "^1.4.0", "karma-webpack": "^4.0.2", "lolex": "^5.1.2", "mocha": "^2.3.2", "phantomjs": "^2.1.7", "sinon": "5.1.1", "sinon-chai": "^3.4.0", "source-map-loader": "^0.2.4", "typescript": "^3.7.4", "webpack": "^4.41.5", "webpack-cli": "^3.3.10", "webpack-sources": "^1.4.3"}, "directories": {"doc": "doc"}, "dist": {"integrity": "sha512-yIBvG1U7mzA2y2eMWhwi9GzVX6WqRkGJvJrp9+BB+krvVh6WZMMQWxRN7sP4BmmF9/clW6qS83wBHa8MW9jTkg==", "shasum": "2eef7d74e8acbc6b54203177688a5dcb383da6cc", "tarball": "https://registry.npmjs.org/@ericblade/quagga2/-/quagga2-0.0.14.tgz", "fileCount": 8, "unpackedSize": 6370750, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeE+RuCRA9TVsSAnZWagAAlgoP/3dL4mF2DxKIt3oPsy10\ngKNv5jU7xTKNAn74sA0cy6ZtPY08RfCajmWGkOenVN+CX39H45jgwRXBB/yN\nACIKAcVSAdSfzkGVe7qelV5pdcWUZkmKjWrLQN/akBmpwI4po+yLkBV0SsZY\nmpJY34UPjo0cSmwB6+3OjlerPFdONWjeVvnNinViPShM1q1iLgz8rWtMBvE8\nLlrvS5SjqiQz1OTNPsv9RhnxVQ1fHwVAjGxdJKql8SQSA88wW6hFC87QcuuR\n53HdZC8ZMFZROjXhXg5ZnFbJCpMc7yKhGWQCllbyVQOIMitFJOM85hUwOTGU\ndfNBFXO6jb7BYM7QRMOve+LymdgRx3529bY8bCPFwkI6x7kaKxONaYA1NiE0\ncoqwPm1h5Jux4MT8cY5sh0iGLaewJgOCIO6Ov+a8a7RagUWlyaMeyz8Z52nO\nV1ZegCBg8fMwGSrXaP1QvWBF8+7Dm9f8YJGOTYH/901AvDO1JGInE9xQ/4Ta\nOP3fbr4JgMjPVCznfIxXccAwXUW1IspBhttUl8JC8ABxxSWGxrUtVnODdkTU\nrG5fNq6J1UEBjEYR/DTQKBZqtjWoa6uSiENry+G2wNNEuqC85DtjCDp09klE\n9xpMfzpQEE56rtbI6As4vUZWTZlvCmmu6VmKP9AJRzETEBZycHXqk9gaco94\np21d\r\n=hA+Z\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIElqvTjPUnGT1+hNNNrTrjjshYf7qVl1S/WIQpszVRuDAiBy+giqkcn2zP/rSI3/bsQ3rdHkHnVUEnJMFkyFuziADQ=="}]}, "engines": {"node": ">= 8.0"}}, "0.0.15": {"name": "@ericblade/quagga2", "version": "0.0.15", "dependencies": {"@babel/polyfill": "^7.7.0", "get-pixels": "^3.3.2", "gl-mat2": "^1.0.1", "gl-vec2": "^1.3.0", "gl-vec3": "^1.1.3", "lodash": "^4.17.15", "ndarray": "^1.0.18", "ndarray-linear-interpolate": "^1.0.0", "snyk": "~1.268.0", "webpack-cli": "^3.3.10"}, "devDependencies": {"@babel/core": "^7.7.7", "@babel/plugin-proposal-class-properties": "^7.7.4", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.7.4", "@babel/plugin-proposal-object-rest-spread": "^7.7.7", "@babel/plugin-proposal-optional-chaining": "^7.7.5", "@babel/plugin-transform-runtime": "^7.7.6", "@babel/preset-env": "^7.7.7", "@babel/preset-typescript": "^7.7.7", "@babel/runtime": "^7.7.7", "@types/chai": "^4.2.7", "@types/gl-vec2": "^1.3.0", "@types/lodash": "^4.14.149", "@types/mocha": "^5.2.7", "@types/react": "^16.9.17", "@types/sinon": "^7.5.1", "@types/sinon-chai": "^3.2.3", "async": "^1.4.2", "babel-loader": "^8.0.6", "babel-plugin-add-module-exports": "^1.0.2", "babel-plugin-istanbul": "^6.0.0", "chai": "^4.2.0", "core-js": "^3.6.1", "cross-env": "^6.0.3", "eslint": "^6.8.0", "grunt": "^1.0.4", "grunt-cli": "^1.3.2", "grunt-contrib-nodeunit": "^1.0.0", "grunt-karma": "^2.0.0", "isparta-loader": "^2.0.0", "karma": "^1.3.0", "karma-chai": "0.1.0", "karma-chrome-launcher": "^2.0.0", "karma-coverage": "^1.1.1", "karma-firefox-launcher": "^0.1.7", "karma-mocha": "~0.2.0", "karma-phantomjs-launcher": "^0.2.3", "karma-sinon": "^1.0.5", "karma-sinon-chai": "^2.0.2", "karma-source-map-support": "^1.4.0", "karma-webpack": "^4.0.2", "lolex": "^5.1.2", "mocha": "^2.3.2", "phantomjs": "^2.1.7", "sinon": "5.1.1", "sinon-chai": "^3.4.0", "source-map-loader": "^0.2.4", "typescript": "^3.7.4", "webpack": "^4.41.5", "webpack-cli": "^3.3.10", "webpack-sources": "^1.4.3"}, "directories": {"doc": "doc"}, "dist": {"integrity": "sha512-XEf49tHBxUNZsqe2K1L3rmffdHPL/l3Mw+JH7zuTbq7b4evpfQutFXpBl3U+kk0XkqxkCzVnDkbEZ7XMYKcn8Q==", "shasum": "2a6d938003b94141ce32d6009a750da88293c7fd", "tarball": "https://registry.npmjs.org/@ericblade/quagga2/-/quagga2-0.0.15.tgz", "fileCount": 8, "unpackedSize": 6353800, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeIAFICRA9TVsSAnZWagAAcS4P/0McAyZBrze5NAW2A9Mz\na5C/BrwiAYPm0xZY0JUvUf0hbMejEogwP2V705eQ/kc+VbcjNKoWOqbOx2nD\nsaL1lfd3dKtauapYvt7AQutZhmaisgQI8ZlUfwRd2ibOC+tUdS4R2C+qQqsE\ntYUiFwCG1YnNp8TQShCGfxDCe0eGsn82yrvLXnwpThjT7x7UYuKmg/L7us6o\nhpCarqW326UVZGv2F6lfWBxztsiNwy0xkEtMBJ54R0lbBHsPRI3GZ9IXwUXv\n59S1V+IsbJNwTeLJveeDGXtZ6pOxa545916IDXsRjpqgOTTHIO7svQZ2rUTH\nokrHp/hOSL88zKRI+akryszj2lFcjaZxD0V+vAxTJkc/pAb7pNIdgmKT0Jct\nBKBNlZtWlhu4iQofuJssY3GDnd26L8Hvq7Sk6ZdJm6pCB5+LNkKalSDD/vuw\noySTd1D2rckwhCe6wdQco3ubU3DjYTFh3/xZlSLGYqCtds+/Vs2L5f8mhrXn\nszdP0l1vdSSiPMoznjfr7a8nD7pXBTri6QUFnMg4RXwb4ATMZShgQHaJRO3T\n8HSsdk9aJszwqD0Rb1V5U5TZ/qSneIaiJfhsNpvx1kPTH7zs4FGuDU0MfRhc\nweoxXbCcrksabfHZ0GTTmnz5tk5yeC/tn2l/hbezfkUkygEr26z25RpKLo/f\nXLi/\r\n=yIVD\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCNOzAJeMC0+CWcfOLyCeZ6cNywjAPNjmugOmYaWVdDxgIgd93XUW6n3ij+1KqYGaSIsw4eMvoMPArPEYunt+gpgwM="}]}, "engines": {"node": ">= 8.0"}}, "0.0.16": {"name": "@ericblade/quagga2", "version": "0.0.16", "dependencies": {"@babel/polyfill": "^7.7.0", "get-pixels": "^3.3.2", "gl-mat2": "^1.0.1", "gl-vec2": "^1.3.0", "gl-vec3": "^1.1.3", "lodash": "^4.17.15", "ndarray": "^1.0.18", "ndarray-linear-interpolate": "^1.0.0", "snyk": "~1.268.0", "webpack-cli": "^3.3.10"}, "devDependencies": {"@babel/core": "^7.7.7", "@babel/plugin-proposal-class-properties": "^7.7.4", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.7.4", "@babel/plugin-proposal-object-rest-spread": "^7.7.7", "@babel/plugin-proposal-optional-chaining": "^7.7.5", "@babel/plugin-transform-runtime": "^7.7.6", "@babel/preset-env": "^7.7.7", "@babel/preset-typescript": "^7.7.7", "@babel/runtime": "^7.7.7", "@types/chai": "^4.2.7", "@types/gl-vec2": "^1.3.0", "@types/lodash": "^4.14.149", "@types/mocha": "^5.2.7", "@types/react": "^16.9.17", "@types/sinon": "^7.5.1", "@types/sinon-chai": "^3.2.3", "async": "^1.4.2", "babel-loader": "^8.0.6", "babel-plugin-add-module-exports": "^1.0.2", "babel-plugin-istanbul": "^6.0.0", "chai": "^4.2.0", "core-js": "^3.6.1", "cross-env": "^6.0.3", "eslint": "^6.8.0", "grunt": "^1.0.4", "grunt-cli": "^1.3.2", "grunt-contrib-nodeunit": "^1.0.0", "grunt-karma": "^2.0.0", "isparta-loader": "^2.0.0", "karma": "^1.3.0", "karma-chai": "0.1.0", "karma-chrome-launcher": "^2.0.0", "karma-coverage": "^1.1.1", "karma-firefox-launcher": "^0.1.7", "karma-mocha": "~0.2.0", "karma-phantomjs-launcher": "^0.2.3", "karma-sinon": "^1.0.5", "karma-sinon-chai": "^2.0.2", "karma-source-map-support": "^1.4.0", "karma-webpack": "^4.0.2", "lolex": "^5.1.2", "mocha": "^2.3.2", "phantomjs": "^2.1.7", "sinon": "5.1.1", "sinon-chai": "^3.4.0", "source-map-loader": "^0.2.4", "typescript": "^3.7.4", "webpack": "^4.41.5", "webpack-cli": "^3.3.10", "webpack-sources": "^1.4.3"}, "directories": {"doc": "doc"}, "dist": {"integrity": "sha512-RD0PZjXtrCdQ7UvoxanD8nheWAej4hCaxGtI1k1cwDT827vLjh1/2ZGy2dfMqqpu0q5CTbElp1k6Dmj5SX1IaQ==", "shasum": "920082c4eaf2bb9c34fff36f77dfc5fceb4929a5", "tarball": "https://registry.npmjs.org/@ericblade/quagga2/-/quagga2-0.0.16.tgz", "fileCount": 8, "unpackedSize": 6352965, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeIASQCRA9TVsSAnZWagAAp/MP/AyxCTJTj04Gg0fNQfKy\n9HVR6gif2nydoulXOeCNXiIikbKj7yoQ3aO1UBNPBiG0mh5CWTVgwLmd2D+A\nl0/Muj1Oz4QkBrzBQKXPJXdhRt6Bo68Gqm3+Jr8AIBv1DRHuAUlGksj7iZpR\n+zOdxczzAwMw2zI/9NFe1XdH/21KJ2JCcU44gEzTQ24P+o9COYJfup4OcQur\nzkL8QAW1RN52ma3inZ+EIO+zp6/H/wL7yfD44PYS+03WYhHYV1t015zJve/w\nfXuUrGTb1lsp54fv7ImLSfIqw4xk85pXcpmT3Jp5QObtEGRVmPMwwnujFInr\n9/Fy7+u0o/zIB5WGW3AGIUD8Mg0SHM5yFTCMs1v9s9dCmXEWyj6sJaTEBKGx\nOgoorw8TLLjqlXuUz2/PUINx9uGHTWDesCO02sO5RZLzUXBBOrXQaWCgRx/1\ntf3w5X4NxPUodcXonpbuSMuZygWnYFpMB0GrfwH771I65JET00duSurwFoN/\n9/gW68okQp1EPkQjYZTavNl2uAWCcdy+40khtPXhWRS8vOft8NzzzYiCpWxq\nN8ookYn6yLT5ErY1LvS572RDLvSzMwaX7TOXNCCHC2X4g9hiu+OMOt5Lu2R8\nFkaBMvj3L6IBKvgEQ2xrwlf/3iV1WKk3Px52tqPreQUJV86r8Be7GHFJKAoz\nZGEz\r\n=reRO\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCID9ao26zJqp5h4X3OPlq/zJbtF5UAhr6R6gk9wePVOKKAiEA12P+edV2lsjh3WslvY554trImXoD5UK1CWhIHoXD9To="}]}, "engines": {"node": ">= 8.0"}}, "0.0.17": {"name": "@ericblade/quagga2", "version": "0.0.17", "dependencies": {"@babel/polyfill": "^7.7.0", "get-pixels": "^3.3.2", "gl-mat2": "^1.0.1", "gl-vec2": "^1.3.0", "gl-vec3": "^1.1.3", "lodash": "^4.17.15", "ndarray": "^1.0.18", "ndarray-linear-interpolate": "^1.0.0", "snyk": "~1.280.1", "webpack-cli": "^3.3.10"}, "devDependencies": {"@babel/core": "^7.7.7", "@babel/plugin-proposal-class-properties": "^7.7.4", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.7.4", "@babel/plugin-proposal-object-rest-spread": "^7.7.7", "@babel/plugin-proposal-optional-chaining": "^7.7.5", "@babel/plugin-transform-runtime": "^7.7.6", "@babel/preset-env": "^7.7.7", "@babel/preset-typescript": "^7.7.7", "@babel/runtime": "^7.7.7", "@types/chai": "^4.2.7", "@types/gl-vec2": "^1.3.0", "@types/lodash": "^4.14.149", "@types/mocha": "^5.2.7", "@types/react": "^16.9.17", "@types/sinon": "^7.5.1", "@types/sinon-chai": "^3.2.3", "async": "^1.4.2", "babel-loader": "^8.0.6", "babel-plugin-add-module-exports": "^1.0.2", "babel-plugin-istanbul": "^6.0.0", "chai": "^4.2.0", "core-js": "^3.6.1", "cross-env": "^7.0.0", "eslint": "^6.8.0", "grunt": "^1.0.4", "grunt-cli": "^1.3.2", "grunt-contrib-nodeunit": "^1.0.0", "grunt-karma": "^2.0.0", "isparta-loader": "^2.0.0", "karma": "^1.3.0", "karma-chai": "0.1.0", "karma-chrome-launcher": "^2.0.0", "karma-coverage": "^1.1.1", "karma-firefox-launcher": "^0.1.7", "karma-mocha": "~0.2.0", "karma-phantomjs-launcher": "^0.2.3", "karma-sinon": "^1.0.5", "karma-sinon-chai": "^2.0.2", "karma-source-map-support": "^1.4.0", "karma-webpack": "^4.0.2", "lolex": "^5.1.2", "mocha": "^2.3.2", "phantomjs": "^2.1.7", "sinon": "5.1.1", "sinon-chai": "^3.4.0", "source-map-loader": "^0.2.4", "typescript": "^3.7.4", "webpack": "^4.41.5", "webpack-cli": "^3.3.10", "webpack-sources": "^1.4.3"}, "directories": {"doc": "doc"}, "dist": {"integrity": "sha512-SKm1oWwm4b7KSUP44KKIV3DfM4QPkvTK1WaQiXwFx2Vai95p/cAJ8lZgebAcMdI4RKapP4IZtbkasp6eIVy3yA==", "shasum": "15de00ef07610ff7a196099c1a63a89a7652f2ae", "tarball": "https://registry.npmjs.org/@ericblade/quagga2/-/quagga2-0.0.17.tgz", "fileCount": 8, "unpackedSize": 6353010, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeLjXdCRA9TVsSAnZWagAAX5IP/Rbx336Z3QpQu4FFiajp\nYxr5cm4KrhtRYZmdLqdYSnXuY1WBReh84+VNHql/jwKk1xGIqhSbOl3jqVN5\n0AlzJqLHS9aWWLUUAcAB7hUMr5SaCjyiyrY4y+LDU+4GggTL6PTMkVrbJlzu\nQJkf2Ge02OT5WsF+R32w+r+1mmuEqn3GhJ0W+OzxINnKRWm3891mNbYUIUTS\n6klxEE2A0eOUzFRvJU9nC+qMN5FYHwjIkDcZlwv2EIGbFuJjX3C+ZFwiss7D\ns6MO/b2GgYW0sibDRmO/PtB63BFJ+KxqNS1HIjDHAdruTsjSMAwrJ6xVJk/i\n2vqP+RRFpJQr+E7nOEHq728UDIyDooTV70oHImaq2GQr/mq3up4NwtOEfdnW\niC3xCqu8ehiM7gKvvYwvwbVNaFD0XVuHEFwB6GP780F7eadtr/kMxLngInBJ\n57Ccph5RClTfb8ElpeQMBIyBHb3QTWnvaMs1Bqe3GYDEcQn2wJ87+aFqPlws\nyBqRdA/yj2rerbWShhqiE32fFxjc+6t4IgdlRIjABWVG31WEiB8Z18WqNSGb\nJ3tffkhR8JnxGkM+aUDPEKZm7K+oKfVbdYUL9zwX+ZwG8gcluvfm6ulxfvNr\nIh+1TUJnUf+ONFPbZd0ANiyP2vZzdDaGD0eq2vAaIRqqCTuIa3ZlrPDJPueF\nnSgV\r\n=XL9E\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDhj75wIQ0hXiicxpvvOwr/6RfAwT4efyjHlWWZsrFSegIhANJpYEVAkVFVy51MwvSp4GciIXbJ3V9qf/4A3x46z4TO"}]}, "engines": {"node": ">= 8.0"}}, "0.0.18": {"name": "@ericblade/quagga2", "version": "0.0.18", "dependencies": {"@babel/polyfill": "^7.8.3", "get-pixels": "^3.3.2", "gl-mat2": "^1.0.1", "gl-vec2": "^1.3.0", "gl-vec3": "^1.1.3", "lodash": "^4.17.15", "ndarray": "^1.0.19", "ndarray-linear-interpolate": "^1.0.0", "snyk": "~1.290.1"}, "devDependencies": {"@babel/core": "^7.8.4", "@babel/plugin-proposal-class-properties": "^7.8.3", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.8.3", "@babel/plugin-proposal-object-rest-spread": "^7.8.3", "@babel/plugin-proposal-optional-chaining": "^7.8.3", "@babel/plugin-transform-runtime": "^7.8.3", "@babel/preset-env": "^7.8.4", "@babel/preset-typescript": "^7.8.3", "@babel/runtime": "^7.8.4", "@types/chai": "^4.2.8", "@types/gl-vec2": "^1.3.0", "@types/lodash": "^4.14.149", "@types/mocha": "5.2.7", "@types/react": "^16.9.19", "@types/sinon": "^7.5.1", "@types/sinon-chai": "^3.2.3", "async": "1.5.2", "babel-loader": "^8.0.6", "babel-plugin-add-module-exports": "^1.0.2", "babel-plugin-istanbul": "^6.0.0", "chai": "^4.2.0", "core-js": "^3.6.4", "cross-env": "^7.0.0", "eslint": "^6.8.0", "grunt": "^1.0.4", "grunt-cli": "^1.3.2", "grunt-contrib-nodeunit": "1.0.0", "grunt-karma": "2.0.0", "isparta-loader": "^2.0.0", "karma": "1.3.0", "karma-chai": "0.1.0", "karma-chrome-launcher": "2.2.0", "karma-coverage": "1.1.2", "karma-firefox-launcher": "0.1.7", "karma-mocha": "0.2.2", "karma-phantomjs-launcher": "0.2.3", "karma-sinon": "^1.0.5", "karma-sinon-chai": "^2.0.2", "karma-source-map-support": "^1.4.0", "karma-webpack": "^4.0.2", "mocha": "2.5.3", "phantomjs": "^2.1.7", "sinon": "5.1.1", "sinon-chai": "^3.4.0", "source-map-loader": "^0.2.4", "typescript": "^3.7.5", "webpack": "^4.41.5", "webpack-cli": "^3.3.10", "webpack-sources": "^1.4.3"}, "directories": {"doc": "doc"}, "dist": {"integrity": "sha512-7Zt4Qmo9PI1fKWBpmzWKn2tljPQXrPX5ByAkRd/cSIofe9KmbORjNQhiZvcZ18QW7sEsUK5Znb4cnwLNeHZcUQ==", "shasum": "65c75e888dfa1a37a33458f704a9b83a0e89fc61", "tarball": "https://registry.npmjs.org/@ericblade/quagga2/-/quagga2-0.0.18.tgz", "fileCount": 8, "unpackedSize": 4049409, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJePKa9CRA9TVsSAnZWagAAK3wP/1bzFKbAgCt9MmQww7FV\nYWOWectICC9pAzDYKg5mctASjxpeH+N6qCpcHAudNeCFD4289KAuZmjjUfms\n0jv4RT6f5Q8QfP0hZJT4eqRKPjKnr6vHDA2wRFCNFMMJvnCrCfalrF12xUtB\ns6y19BSszhgD6dC+TMA2HKENz9c6BHdHeRYhPPAEnETlgksnUcELbC7hGl1g\nW+GrMdqD5eb+U1suIaLXu4wgfCiw40reCe7dyfQ7W83Gir/sOcMfqEpxacHZ\nbZdOWbvZ8aJ6f62KVWSydBv/Kh+GNH3mNsVqNNGSfcBwFrONUfi3COUG+jwW\nfnBFU0VVRW+vByUeKaUo8jmlpvbgu9LJIkNFKXcSbB9Y4F0lrVi7vbXsSEi3\ngHd76KVMtlmDi7wxEpoxBa+h1qLBMpNQcghznQbFUTibSdsPZCQYVsyiNY+Y\nAydglF7+qmt0yTwJnSva7oDnd5ig2c2HRrWxU2cb3DC4uu1WO0pwQchE9pYZ\nWTAubob9DqIKvU+JMsKwhHY1LmNyLal2SaI1OGcbf5IdQkUZjA2srjR5WUJQ\nLpk7dUiqKPSvjZmShHn6Dz/h199g2mGziT6tqf5yn1HTHyZdq3U/suhMrwvW\njTMeNrh04gnblOtnfAChIX5D0HJQdR6C/qfO2OcxHDzBrBZu6fFXDN0MtBbb\nP859\r\n=jX8T\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGvH51bca6NL9BzI0FfFrX5UlEcmtfb1kDfqGKk+L9FVAiAQN/V1/LLwO+59UsYKANAkPPmifMu6M+Otle0Hkj5WTQ=="}]}, "engines": {"node": ">= 8.0"}}, "0.0.19": {"name": "@ericblade/quagga2", "version": "0.0.19", "dependencies": {"@babel/polyfill": "^7.8.3", "get-pixels": "^3.3.2", "gl-mat2": "^1.0.1", "gl-vec2": "^1.3.0", "gl-vec3": "^1.1.3", "lodash": "^4.17.15", "ndarray": "^1.0.19", "ndarray-linear-interpolate": "^1.0.0", "snyk": "~1.302.0"}, "devDependencies": {"@babel/core": "^7.8.4", "@babel/plugin-proposal-class-properties": "^7.8.3", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.8.3", "@babel/plugin-proposal-object-rest-spread": "^7.8.3", "@babel/plugin-proposal-optional-chaining": "^7.8.3", "@babel/plugin-transform-runtime": "^7.8.3", "@babel/preset-env": "^7.8.4", "@babel/preset-typescript": "^7.8.3", "@babel/runtime": "^7.8.4", "@types/chai": "^4.2.8", "@types/gl-vec2": "^1.3.0", "@types/lodash": "^4.14.149", "@types/mocha": "5.2.7", "@types/react": "^16.9.19", "@types/sinon": "^7.5.1", "@types/sinon-chai": "^3.2.3", "async": "1.5.2", "babel-loader": "^8.0.6", "babel-plugin-add-module-exports": "^1.0.2", "babel-plugin-istanbul": "^6.0.0", "chai": "^4.2.0", "core-js": "^3.6.4", "cross-env": "^7.0.0", "eslint": "^6.8.0", "grunt": "^1.0.4", "grunt-cli": "^1.3.2", "grunt-contrib-nodeunit": "1.0.0", "grunt-karma": "2.0.0", "isparta-loader": "^2.0.0", "karma": "1.3.0", "karma-chai": "0.1.0", "karma-chrome-launcher": "2.2.0", "karma-coverage": "1.1.2", "karma-firefox-launcher": "0.1.7", "karma-mocha": "0.2.2", "karma-phantomjs-launcher": "0.2.3", "karma-sinon": "^1.0.5", "karma-sinon-chai": "^2.0.2", "karma-source-map-support": "^1.4.0", "karma-webpack": "^4.0.2", "mocha": "2.5.3", "phantomjs": "^2.1.7", "sinon": "5.1.1", "sinon-chai": "^3.4.0", "source-map-loader": "^0.2.4", "typescript": "^3.7.5", "webpack": "^4.41.5", "webpack-cli": "^3.3.10", "webpack-sources": "^1.4.3"}, "directories": {"doc": "doc"}, "dist": {"integrity": "sha512-IwPEhuKbP3gAf+tLXC37ghmpPfBslUp8YajL6LtKCfiB4AFIxqEHZxnRMHqPju3S0SXGBnbY/6dz69iLv/OG8Q==", "shasum": "2eb0d4ce0589c1da011178848976a28fe2e1372c", "tarball": "https://registry.npmjs.org/@ericblade/quagga2/-/quagga2-0.0.19.tgz", "fileCount": 10, "unpackedSize": 5216744, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeeE2ECRA9TVsSAnZWagAAU1MP/1VzFP+wrCCGl49oWMAU\nnhEcEfVPygPX3PUwjYLFHw3bOG1jwltMwcleB7M900Qx5BDwKepVQrr6c8mg\nS8ZKabdqtr+cI+WDe6fNF8W4gQ+oRuR4fFHl+LOqYbzs34jqLIVBBG0nM33z\nd4KBjRNTvYBXlo/QRZK8Zesx0m789K7SWt63dRK35AozUXal+a9sE5t5VN6E\nfaeRN440oLRHA2TO34MH0E+5R7GQIpl/TruCUVime6erd2VVaY05s/M8xu8Q\njy+F5ulWZqQy7R4li8qgLSpEl3S0zjyfG2lcLwtnNoaT3DNuzdoFIINy0IgQ\nMpKYEcsrLdNp/E7DWc6izIlY4DqXz0aAHjM2YhUgfx3gkN0rCIokrOSl1Jj9\nsrPJ7GLY8d97KK1KRNmqAPxsOFhNz6nCM9VWd8heRAJA+krVlrv3PYnqYz1N\nAPC2wL7kAo0SYJ8Q2MjrkInKbx0E2GVQI8Eox+CfN5QQsLq5X3QnxYbQ/Xyn\nLA3FMFM0+D9/v49ahEkKYz3lUSyoriwq+UB/AQBqySgwOB9jmL0+4c6rupBB\n8Z7zAy2sqMnA2OTCah0mbDn60nfaGXKA2Q6ieGjqCsylxmzBJah1nXzdkCRV\nrxfx/TtgvzALrCfDVooX+DZb4GmRoMzpu0mVLGxchfV9dUCoG09GoSLtQRs+\nL4oF\r\n=rvGo\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGflGNTWMR3a/PzUlzdJvGPQHkaFlCHAnT8DR4gVtdPvAiEAmOpF2fdiXPsJA/UcXp/8opYPjSmfj6JggwDK8IhtX0I="}]}, "engines": {"node": ">= 8.0"}}, "0.0.20": {"name": "@ericblade/quagga2", "version": "0.0.20", "dependencies": {"@babel/polyfill": "^7.8.7", "get-pixels": "^3.3.2", "gl-mat2": "^1.0.1", "gl-vec2": "^1.3.0", "gl-vec3": "^1.1.3", "lodash": "^4.17.15", "ndarray": "^1.0.19", "ndarray-linear-interpolate": "^1.0.0", "snyk": "~1.305.0"}, "devDependencies": {"@babel/core": "^7.8.4", "@babel/plugin-proposal-class-properties": "^7.8.3", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.8.3", "@babel/plugin-proposal-object-rest-spread": "^7.8.3", "@babel/plugin-proposal-optional-chaining": "^7.8.3", "@babel/plugin-transform-runtime": "^7.8.3", "@babel/preset-env": "^7.8.4", "@babel/preset-typescript": "^7.8.3", "@babel/runtime": "^7.8.4", "@types/chai": "^4.2.9", "@types/gl-vec2": "^1.3.0", "@types/lodash": "^4.14.149", "@types/mocha": "5.2.7", "@types/react": "^16.9.19", "@types/sinon": "^7.5.1", "@types/sinon-chai": "^3.2.3", "async": "1.5.2", "babel-loader": "^8.0.6", "babel-plugin-add-module-exports": "^1.0.2", "babel-plugin-istanbul": "^6.0.0", "chai": "^4.2.0", "core-js": "^3.6.4", "cross-env": "^7.0.0", "eslint": "^6.8.0", "grunt": "^1.0.4", "grunt-cli": "^1.3.2", "grunt-contrib-nodeunit": "1.0.0", "grunt-karma": "2.0.0", "isparta-loader": "^2.0.0", "karma": "1.3.0", "karma-chai": "0.1.0", "karma-chrome-launcher": "2.2.0", "karma-coverage": "1.1.2", "karma-firefox-launcher": "0.1.7", "karma-mocha": "0.2.2", "karma-phantomjs-launcher": "0.2.3", "karma-sinon": "^1.0.5", "karma-sinon-chai": "^2.0.2", "karma-source-map-support": "^1.4.0", "karma-webpack": "^4.0.2", "mocha": "2.5.3", "phantomjs": "^2.1.7", "sinon": "5.1.1", "sinon-chai": "^3.5.0", "source-map-loader": "^0.2.4", "typescript": "^3.8.2", "webpack": "^4.41.6", "webpack-cli": "^3.3.11", "webpack-sources": "^1.4.3"}, "directories": {"doc": "doc"}, "dist": {"integrity": "sha512-FxltTV19npTLkPgf8MUI4l5Mt8dr0CWtS01VOgO67r/dUWaFxK2IO/MyQto1e/7GTFAQHiHa2d1Zte17mTHkbQ==", "shasum": "a6da4e745ea3ea09fa008f7b1489cd02e4f3eeb1", "tarball": "https://registry.npmjs.org/@ericblade/quagga2/-/quagga2-0.0.20.tgz", "fileCount": 10, "unpackedSize": 5354496, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeltMcCRA9TVsSAnZWagAAoB8QAJ4clMizC0iIlAYKZipa\n171XlshqjLCOuZs71qJRIQNQZDeUYzb/dV80Zat6VgoXxIOa8p7IZ+hgFsUG\nlf5zSwKP9XOzVJi+EfwFg1E6ddDs+mA7/evd9mcKRztYsYXC7/D8AMUY1W5N\njSu26V2lc3r5JmcRWL2BIxpHn9tRxtedARd/9hTlLMQGjXkpCepLhN/78Y+0\nbHUPGVSGLFMRAWl+koHsG0lXzTgeBO5nr+Op2NMRis/+/hC9Mcc1Rv3+WQ5s\nhb7VKtlPRNB+cwTYErr8CxK7nMKvw0V4mVQIyMjVs0nQ8N1HybF7H0kcIvA5\n95i0q7NvZllgP4AsFEyM42+1jlAgMZjpjyNCym6xCVJg0gAUeJPT4YwkLy6X\nw45ILSLlgpe+F4c/seqE3gp5TRHPdCZEk+hjN6keDZ172oHevDRJNq9oLOWx\nrfa+lPXO5PtatDBmYu/8cq0jjAYRxNHdU96TkfzUZAu/1XRSL1gLxH8uvJ4L\nt6+AxV94tpHMa07HbdXiRbIB6yjd1WB3pwxpO+LzBArtNx2lbmYLKIFq5ayT\nOPErgvTr/2xNSrWN5XKa84+0WecQ2Ydmy3RmoI4lh/5d4w7TQQFhfCVqBIwy\n4Nlu+t4Jnpu6w7i1n1i5kUWodLkhDkTgNd2MUk6p/nvSRicrMY1X7sT7YIYs\n7B4h\r\n=m7H6\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCpiBDAq8JJX+TuisDUmY3Z2BaJzsPEsT0xm3k7jDRa4QIgcnR/+J90NVDfKFufT3+JSe+m78BcnAu/yDwjgLtUfoI="}]}, "engines": {"node": ">= 8.0"}}, "0.0.21": {"name": "@ericblade/quagga2", "version": "0.0.21", "dependencies": {"@babel/polyfill": "^7.8.7", "get-pixels": "^3.3.2", "gl-mat2": "^1.0.1", "gl-vec2": "^1.3.0", "gl-vec3": "^1.1.3", "lodash": "^4.17.15", "ndarray": "^1.0.19", "ndarray-linear-interpolate": "^1.0.0", "snyk": "~1.307.0"}, "devDependencies": {"@babel/core": "^7.8.4", "@babel/plugin-proposal-class-properties": "^7.8.3", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.8.3", "@babel/plugin-proposal-object-rest-spread": "^7.8.3", "@babel/plugin-proposal-optional-chaining": "^7.8.3", "@babel/plugin-transform-runtime": "^7.8.3", "@babel/preset-env": "^7.8.4", "@babel/preset-typescript": "^7.8.3", "@babel/runtime": "^7.8.4", "@types/chai": "^4.2.9", "@types/gl-vec2": "^1.3.0", "@types/lodash": "^4.14.149", "@types/mocha": "5.2.7", "@types/react": "^16.9.19", "@types/sinon": "^7.5.1", "@types/sinon-chai": "^3.2.3", "async": "1.5.2", "babel-loader": "^8.0.6", "babel-plugin-add-module-exports": "^1.0.2", "babel-plugin-istanbul": "^6.0.0", "chai": "^4.2.0", "core-js": "^3.6.4", "cross-env": "^7.0.0", "eslint": "^6.8.0", "grunt": "^1.0.4", "grunt-cli": "^1.3.2", "grunt-contrib-nodeunit": "1.0.0", "grunt-karma": "2.0.0", "isparta-loader": "^2.0.0", "karma": "1.3.0", "karma-chai": "0.1.0", "karma-chrome-launcher": "2.2.0", "karma-coverage": "1.1.2", "karma-firefox-launcher": "0.1.7", "karma-mocha": "0.2.2", "karma-phantomjs-launcher": "0.2.3", "karma-sinon": "^1.0.5", "karma-sinon-chai": "^2.0.2", "karma-source-map-support": "^1.4.0", "karma-webpack": "^4.0.2", "mocha": "2.5.3", "phantomjs": "^2.1.7", "sinon": "5.1.1", "sinon-chai": "^3.5.0", "source-map-loader": "^0.2.4", "typescript": "^3.8.2", "webpack": "^4.41.6", "webpack-cli": "^3.3.11", "webpack-sources": "^1.4.3"}, "directories": {"doc": "doc"}, "dist": {"integrity": "sha512-KsbwUdpOmhOZ7OSu88leQdZtsU5X7D7PHjSs6udmvkrwNquyMEKOkvnyGKiJZGJ4Ut+PFowOZ42/9Tx1Xm6kyw==", "shasum": "26c1e487643be735d777c9da321f2b356e3703ce", "tarball": "https://registry.npmjs.org/@ericblade/quagga2/-/quagga2-0.0.21.tgz", "fileCount": 12, "unpackedSize": 5356586, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJepS1vCRA9TVsSAnZWagAABvsP/AvRKQOMiVzw7AlpT56x\nH5FDN07iVKqr+CPhfOW9/HGYenPYUqEMkRx3HR3mB8UnslErsejXPJ0ajUjR\n6RzwagA3lYVxwwb34ZKmI6ibTwjhoxn+Xl126zdHdJMdheCr5+u2P3yzLlcx\nKSlWoluzMQDH8AJAKKrXXQ/WgFcD4bHhXAFItqOWEFhuYrKnJsOkzWvyNs8c\nCzaa5wDkpxI/Ibd7V92NAqGSxdJAu4cIabd9E3AS0Ozg2ogEia6A132c4bT6\nNM1P5sw9GITk0CPHlg6SUa13bCnm7Jkn1HRJ7hdBRWOMrc6m0n6/V3PTqf2d\nQdYZ7GK2V6a4X9FDdmbNwaY/yHBX3bwRMAj3EGfIOLijs8d6R4VW/H2NNerR\n216OXuAvg5mTMb/pLelzc1ZkXelTh4EJGbPgrDjqWS5nAk9XJYJ3PGG0G3m2\nh+utn9SGu6W8h5Je+jMw4zH0x6573FROpa3xVpdtgfzpPeOD4J2DMVsnmAAv\npbLlr6XCyVuUO5OFPopDNWRLqHAKR4bKt4Z//QNKi0TJ9pByeu6mdHyRIt0M\nAj2ZBc9I73u9uV6397yTEmk9JXdb115kVfxXOTlipKz6TcreLnc0+m/2gJp/\nggq//ZjO27ZZCHm4uQU7WNZNIagECpdCJk30rzBhPOSZBRR6pQzB+5WdUnNa\nLHS9\r\n=f0HN\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDIIjj5HTaMMIzIVsQrl4JKpeuTfK0I41t6hEk/ZM9XggIgDmf9Em4Tccee+5DXJDJn62qgM84n1QzrwsN/fnBBahY="}]}, "engines": {"node": ">= 8.0"}}, "1.0.0": {"name": "@ericblade/quagga2", "version": "1.0.0", "dependencies": {"@babel/polyfill": "^7.8.7", "get-pixels": "^3.3.2", "gl-mat2": "^1.0.1", "gl-vec2": "^1.3.0", "gl-vec3": "^1.1.3", "lodash": "^4.17.15", "ndarray": "^1.0.19", "ndarray-linear-interpolate": "^1.0.0", "snyk": "~1.314.0"}, "devDependencies": {"@babel/core": "^7.8.4", "@babel/plugin-proposal-class-properties": "^7.8.3", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.8.3", "@babel/plugin-proposal-object-rest-spread": "^7.8.3", "@babel/plugin-proposal-optional-chaining": "^7.8.3", "@babel/plugin-transform-runtime": "^7.8.3", "@babel/preset-env": "^7.8.4", "@babel/preset-typescript": "^7.8.3", "@babel/runtime": "^7.8.4", "@types/chai": "^4.2.9", "@types/gl-vec2": "^1.3.0", "@types/lodash": "^4.14.149", "@types/mocha": "5.2.7", "@types/react": "^16.9.19", "@types/sinon": "^7.5.1", "@types/sinon-chai": "^3.2.3", "async": "1.5.2", "babel-loader": "^8.0.6", "babel-plugin-add-module-exports": "^1.0.2", "babel-plugin-istanbul": "^6.0.0", "chai": "^4.2.0", "core-js": "^3.6.4", "cross-env": "^7.0.0", "eslint": "^6.8.0", "grunt": "^1.0.4", "grunt-cli": "^1.3.2", "grunt-contrib-nodeunit": "1.0.0", "grunt-karma": "2.0.0", "isparta-loader": "^2.0.0", "karma": "1.3.0", "karma-chai": "0.1.0", "karma-chrome-launcher": "2.2.0", "karma-coverage": "1.1.2", "karma-firefox-launcher": "0.1.7", "karma-mocha": "0.2.2", "karma-phantomjs-launcher": "0.2.3", "karma-sinon": "^1.0.5", "karma-sinon-chai": "^2.0.2", "karma-source-map-support": "^1.4.0", "karma-webpack": "^4.0.2", "mocha": "2.5.3", "phantomjs": "^2.1.7", "sinon": "5.1.1", "sinon-chai": "^3.5.0", "source-map-loader": "^0.2.4", "typescript": "^3.8.2", "webpack": "^4.41.6", "webpack-cli": "^3.3.11", "webpack-sources": "^1.4.3"}, "directories": {"doc": "doc"}, "dist": {"integrity": "sha512-<PERSON><PERSON><PERSON><PERSON>et9tPHZ6xS0QDiueFEFCXgD5tEBvLfNZYLSoVOXjUohBy/ocfFQSpe675GeZ5laAOIgyBzN0gfTigQBeCQ==", "shasum": "a865bfc4e521e586ba02fc97e3a68e2669462f4e", "tarball": "https://registry.npmjs.org/@ericblade/quagga2/-/quagga2-1.0.0.tgz", "fileCount": 11, "unpackedSize": 5336229, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeq/RzCRA9TVsSAnZWagAAclQQAJxPCQqjHqHyPO3q0c85\ni5k3UH5TvU/ky6AP7hysunR3s+7CTmV36CYP7sV0BR0G3toPjKRMQ2IFv/RE\nbNTK1NAmV+GevtJppjnWlC5MXunzLlVWeB/JTR5c1x44pmaeBcfKV7wtVymp\nhCHxVxwF3ruh4OS1ycZFMuShCvjmPwXzxn0BirPnDTvV/79jvWbLhUGZ5B8s\nTtG1fOlf5T/02nDWHlsRwVAtoato9IXMcY2ubPxUEKKgmvf+5KmlLKtI5y4y\n+jun7FmTbU6BOSQvZCujcKCUg4TMbdyY0c77YjYWvQKkqL9DKPKv+Qf0zKZ7\n9+1SX16BVXsBtTEgjkVHkx5DbmiforEbL5dsBvzw5yTIHIK6TW00XEan1+yy\nTdXsgjNet2QmmDeo3cGULtMc2WfLG6fk3V/pLvyF4C0ZyWeKkLgbzGGOOKmM\ntxl22lTKNUB57NdlHPZr/EReE6bg2/yB6RahRBW9dvkUx/npYkSwlDQaIaT0\nihV2vzoqliP0R4Byk2yhiPptcmJNixvrIMSmUM8mVGqH/yWe3XvgVlVgW54N\nEclfjyU4WR/aZqvgKp+94Z/pcGgtkGgCLyJel+ni0LsDPPXfobDFc64qa2TT\npI8crr1rM6hVgu1svXklEnKaxx36kdRFwrhnwEiKhS9m6w/q71Tgcj5PwR+X\nmKln\r\n=jJ9G\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICtjeOa4Xczw4lECiSW0x4I7Kg7i7Mqp/aAt3I1gedq9AiEAte0G6O1NB4UW8mNWP1VC56mnqAFp+1yTm+tbGQiea/Y="}]}, "engines": {"node": ">= 8.0"}}, "1.0.1": {"name": "@ericblade/quagga2", "version": "1.0.1", "dependencies": {"@babel/polyfill": "^7.8.7", "get-pixels": "^3.3.2", "gl-mat2": "^1.0.1", "gl-vec2": "^1.3.0", "gl-vec3": "^1.1.3", "lodash": "^4.17.15", "ndarray": "^1.0.19", "ndarray-linear-interpolate": "^1.0.0", "snyk": "~1.315.0"}, "devDependencies": {"@babel/core": "^7.8.4", "@babel/plugin-proposal-class-properties": "^7.8.3", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.8.3", "@babel/plugin-proposal-object-rest-spread": "^7.8.3", "@babel/plugin-proposal-optional-chaining": "^7.8.3", "@babel/plugin-transform-runtime": "^7.8.3", "@babel/preset-env": "^7.8.4", "@babel/preset-typescript": "^7.8.3", "@babel/runtime": "^7.8.4", "@types/chai": "^4.2.9", "@types/gl-vec2": "^1.3.0", "@types/lodash": "^4.14.149", "@types/mocha": "5.2.7", "@types/react": "^16.9.19", "@types/sinon": "^7.5.1", "@types/sinon-chai": "^3.2.3", "async": "1.5.2", "babel-loader": "^8.0.6", "babel-plugin-add-module-exports": "^1.0.2", "babel-plugin-istanbul": "^6.0.0", "chai": "^4.2.0", "core-js": "^3.6.4", "cross-env": "^7.0.0", "eslint": "^6.8.0", "grunt": "^1.0.4", "grunt-cli": "^1.3.2", "grunt-contrib-nodeunit": "1.0.0", "grunt-karma": "2.0.0", "isparta-loader": "^2.0.0", "karma": "1.3.0", "karma-chai": "0.1.0", "karma-chrome-launcher": "2.2.0", "karma-coverage": "1.1.2", "karma-firefox-launcher": "0.1.7", "karma-mocha": "0.2.2", "karma-phantomjs-launcher": "0.2.3", "karma-sinon": "^1.0.5", "karma-sinon-chai": "^2.0.2", "karma-source-map-support": "^1.4.0", "karma-webpack": "^4.0.2", "mocha": "2.5.3", "phantomjs": "^2.1.7", "sinon": "5.1.1", "sinon-chai": "^3.5.0", "source-map-loader": "^0.2.4", "typescript": "^3.8.2", "webpack": "^4.41.6", "webpack-cli": "^3.3.11", "webpack-sources": "^1.4.3"}, "directories": {"doc": "doc"}, "dist": {"integrity": "sha512-FF8M2f+gS5uHRQvdfvbmaT66z+hLEbBjsWJvvj4AB67ipGBseYIHUzVAY3MmJ57zq1je5HluSeq3bL2HvDTSGg==", "shasum": "d028fafd0d7592120bd51a95355b1b5fa0902591", "tarball": "https://registry.npmjs.org/@ericblade/quagga2/-/quagga2-1.0.1.tgz", "fileCount": 11, "unpackedSize": 5336229, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeq/nlCRA9TVsSAnZWagAAU1kP/j+PXB+Dg+xMT5QM6hk0\nVxEdt8GZGSPvJtgS3iK9u8FGDUTbmXQba3JNgxLG/CdAH3yD57VVwK+fM72c\nPs4+wqke3zYmswzem73D3YGw2X6jKcGDR3veci74Bq0jNj1UMgp31D5qXw9N\nETY7rn6jQSr7e87+OXz/HlAZ/8pS7UTCD1Kycjmja2jtZiZDTkOKgv7lC2/3\nTPJHFRQnfWIvk/GmMMZlwM4WY/xn0WfkjV3Fp7YZ0r/e9DEYvSMJFctUVu+r\nqP3S0p5h2DRkoJNl9Nq6P7/TAKapq2OZNwkBUBs1JbAdnCKR8XXHnReS/vYi\nXe72gbh+1Pb1hCINbJ/b5BXImcu2kLC15QzJgzGrEYKKPiDimiT5xY1QIgNV\n53e7AqOC89zRCYBqXPZ8GMma5UQB9ASesibgxNjUnfenBFBiL7mEXigKGeQT\nXXLVDdvzFw2gzab4m5rpQ7+but4uUuAFzjvK1OuM4qoRIvEYCZG9iB5tok23\ndXoVeVD1kjV4A+5MK7UcSrMdy5VREelQP1rFJBV09wQ/XCHhkw176iOAZuJ9\n0nzI5s4cwNft0UUw0uK86wcODp/Mum9T1xdt9ts2i/qNwI5uUvS7r0LFBtg7\ngdqqT5P5fDsAt/5qa4/Lg/nHHyA0Nl3rYqBPD4IVqX7OBHFKDWZbFvwWLRiE\nLEJg\r\n=gGol\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDq65mS9UOhgMQr4RBwNIs7P03tcSd6DYiLpzVZHpTYEAIgGZ/xBS8OPiQNoEycdtVWMamKKhzDx/l/tO5CPhhoBz4="}]}, "engines": {"node": ">= 8.0"}}, "1.0.2": {"name": "@ericblade/quagga2", "version": "1.0.2", "dependencies": {"@babel/polyfill": "^7.8.7", "get-pixels": "^3.3.2", "gl-mat2": "^1.0.1", "gl-vec2": "^1.3.0", "gl-vec3": "^1.1.3", "lodash": "^4.17.15", "ndarray": "^1.0.19", "ndarray-linear-interpolate": "^1.0.0", "snyk": "^1.315.1"}, "devDependencies": {"@babel/core": "^7.9.6", "@babel/plugin-proposal-class-properties": "^7.8.3", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.8.3", "@babel/plugin-proposal-object-rest-spread": "^7.9.6", "@babel/plugin-proposal-optional-chaining": "^7.8.3", "@babel/plugin-transform-runtime": "^7.9.6", "@babel/preset-env": "^7.9.6", "@babel/preset-typescript": "^7.8.3", "@babel/runtime": "^7.9.6", "@types/chai": "^4.2.9", "@types/gl-vec2": "^1.3.0", "@types/lodash": "^4.14.150", "@types/mocha": "5.2.7", "@types/react": "^16.9.19", "@types/sinon": "^7.5.1", "@types/sinon-chai": "^3.2.3", "babel-loader": "^8.0.6", "babel-plugin-add-module-exports": "^1.0.2", "babel-plugin-istanbul": "^6.0.0", "chai": "^4.2.0", "core-js": "^3.6.5", "cross-env": "^7.0.0", "eslint": "^6.8.0", "grunt": "^1.0.4", "grunt-cli": "^1.3.2", "grunt-contrib-nodeunit": "1.0.0", "grunt-karma": "2.0.0", "isparta-loader": "^2.0.0", "karma": "1.3.0", "karma-chai": "0.1.0", "karma-chrome-launcher": "2.2.0", "karma-coverage": "1.1.2", "karma-firefox-launcher": "0.1.7", "karma-mocha": "1.3.0", "karma-phantomjs-launcher": "0.2.3", "karma-sinon": "^1.0.5", "karma-sinon-chai": "^2.0.2", "karma-source-map-support": "^1.4.0", "karma-webpack": "^4.0.2", "mocha": "5.2.0", "phantomjs": "^2.1.7", "sinon": "5.1.1", "sinon-chai": "^3.5.0", "source-map-loader": "^0.2.4", "typescript": "^3.8.2", "webpack": "^4.43.0", "webpack-cli": "^3.3.11", "webpack-sources": "^1.4.3"}, "directories": {"doc": "doc"}, "dist": {"integrity": "sha512-ci5swNL4skbsqgSKudx+VGejvaOSC7STnk5xhtFbDU+hGctG9rOGRIpcozfyvaxjf3S/gpCQzghdTJF67XPg2g==", "shasum": "2c4c1aae0cafae91396adec5a30eb8337f22da6c", "tarball": "https://registry.npmjs.org/@ericblade/quagga2/-/quagga2-1.0.2.tgz", "fileCount": 11, "unpackedSize": 5344904, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJerxTyCRA9TVsSAnZWagAAu9MP/1hM2eP7XBq5iVtKJC+9\nz4W7SvdkAlw/fUvTkSkwR7MaDyitJe+vsu+aT4A+E/mnE4Zj/PCxylstP/J+\n2TGF0UsYAryPnMmuTTs0T9gz1twGASfETPzbxHyyzoCRrWYCAooZiIhBn6L9\npPo7RGFBsZnzY56eghpYjtiZrwpd4dK9RMq0ntIa31cHk/Ijpkty5Ur/mfwy\n4DkwBedhhW6qjD6YY5l8mmlkSSF+PapzLlsoxlKlfGxO9ox32hYES1hZt0DN\nS26G9jrbHAayk6q65fWPU+x/It+w8mIJZlIQnqlj6OK+m5KC2Db/HrEQp1fM\n07deW2nFhYzi0hwvG6VXpyUTxwWZBLYHfq9G6FVq1oaPlSMs25zbkTeTv42n\nwEV6X87oQo101RmFLzyc5nCrFgD2aeqdN+l6LRb5s13h4k/rUiYSodUYq0rP\nC7p0REeOqIlEphc4ucC7kcUucIQBTstJP+tUtGeZ0VEl8r55BdJT/oCmqCEq\n7cGleq6OywXTH1SHW30d75PxePbjUiExq81P9ftdAmS9Iycn28rE6rQYsVw2\nxm4i/l+ilnnRl5xFFAnf9on8N2gGduEA47C9RgTbm61iqbYnn3l44d/zia/C\nxQ7y9d/Dz3am2K08KtP3fxjotc0Ts/hmoi80BYxr74eUed1dozEm3w15ejiL\np5HJ\r\n=64Fh\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBwCEsXNC6DnZ2vz4xcIcQXZnyNOg6SZSvlqNhGG0sJRAiEAtINySU/wA+eGNsn0VoLaTcDubtL1sbomc5v3pdS5YBg="}]}, "engines": {"node": ">= 10.0"}}, "1.0.3": {"name": "@ericblade/quagga2", "version": "1.0.3", "dependencies": {"@babel/polyfill": "^7.8.7", "get-pixels": "^3.3.2", "gl-mat2": "^1.0.1", "gl-vec2": "^1.3.0", "gl-vec3": "^1.1.3", "lodash": "^4.17.15", "ndarray": "^1.0.19", "ndarray-linear-interpolate": "^1.0.0", "snyk": "^1.315.1"}, "devDependencies": {"@babel/core": "^7.9.6", "@babel/plugin-proposal-class-properties": "^7.8.3", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.8.3", "@babel/plugin-proposal-object-rest-spread": "^7.9.6", "@babel/plugin-proposal-optional-chaining": "^7.8.3", "@babel/plugin-transform-runtime": "^7.9.6", "@babel/preset-env": "^7.9.6", "@babel/preset-typescript": "^7.8.3", "@babel/runtime": "^7.9.6", "@cypress/code-coverage": "^3.7.0", "@cypress/webpack-preprocessor": "^5.1.2", "@std/esm": "^0.26.0", "@types/chai": "^4.2.9", "@types/gl-vec2": "^1.3.0", "@types/lodash": "^4.14.150", "@types/mocha": "5.2.7", "@types/react": "^16.9.19", "@types/sinon": "^7.5.1", "@types/sinon-chai": "^3.2.3", "babel-loader": "^8.0.6", "babel-plugin-add-module-exports": "^1.0.2", "babel-plugin-istanbul": "^6.0.0", "chai": "^4.2.0", "core-js": "^3.6.5", "cross-env": "^7.0.0", "cypress": "^4.5.0", "eslint": "^6.8.0", "grunt": "^1.0.4", "grunt-cli": "^1.3.2", "grunt-contrib-nodeunit": "1.0.0", "grunt-karma": "2.0.0", "isparta-loader": "^2.0.0", "istanbul-lib-coverage": "^3.0.0", "karma": "1.3.0", "karma-chai": "0.1.0", "karma-chrome-launcher": "2.2.0", "karma-coverage": "1.1.2", "karma-mocha": "1.3.0", "karma-phantomjs-launcher": "0.2.3", "karma-sinon": "^1.0.5", "karma-sinon-chai": "^2.0.2", "karma-source-map-support": "^1.4.0", "karma-webpack": "^4.0.2", "mocha": "5.2.0", "nyc": "^15.0.1", "phantomjs": "^2.1.7", "sinon": "5.1.1", "sinon-chai": "^3.5.0", "source-map-loader": "^0.2.4", "ts-mocha": "^7.0.0", "ts-node": "^8.10.1", "typescript": "^3.8.2", "webpack": "^4.43.0", "webpack-cli": "^3.3.11", "webpack-sources": "^1.4.3"}, "directories": {"doc": "doc"}, "dist": {"integrity": "sha512-aIFbUohjUYSoCkYpOf9IBzv7Lz/1nKiPY3StlgjeaRhkm5NLb9ePX3rWLmmT6xF6z/KkSsfJzwqsTkeyKw8zfA==", "shasum": "be7e514faea635d52d29f3473c92781b4202c0ea", "tarball": "https://registry.npmjs.org/@ericblade/quagga2/-/quagga2-1.0.3.tgz", "fileCount": 13, "unpackedSize": 5551936, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeuP2+CRA9TVsSAnZWagAAo3gP/16SK5fE1wZPheCiN3h0\n3ZX5pC7s3+xUiGskk8m0aCnFPpx6GZT3uQp/EHDXBRybY7XDAcMTKMFBecyi\nIdSYZICvlc0fkV+1HzqxxF52FdB+i4awdp1E7npAVPe9dujUb005fg4L6XEg\ntCsQINZBuSPK2nWV+RFNhHADIa71S2ohemqHXoCQYgs3ZTnyFKRO5yzOb1UT\nbB0tW6DWllMca1WdA1Hc5Ndm0+EMt+QvGOJwkzNupCh7QWMFFCFqch6ILtpd\n6mjvjHEJ9+kM6yGJKJAvfwM/3SLfQ/wZnnX86Z9rtxN2bsu06YzKozPhcoQy\nmDL57Fy0nbsiK57yvyTuYJth1l2d0VyUioglOLndJ97603YW/xWRJtGkUxjK\nPJz/Dwo5Yq11vhHTityOVmD/dau4AHUNKPdcSXp+ug2CYnbisEro4BDN2bBv\nUEWZtDtlQgPs6PfNPR1ghvmEzn3vlkivClhipI18/QfNWQxXViVJ0HCHSAl/\n+KHTbcZzJuzccGqjky/fAAUamynbniOBzfVt3Jr9SjjId0Kqkce3/8h1d/jc\nFhk9IIwNUx1ZPmTzeB8MHHs1CruQD3BLXI/Mh9y3S2wNd1uOPopdTwyMQNWy\n9ftj7qS36Vnt3sv1L+jgT9GtY07L9iwn5yqZ4wYYcUAPmryURKibeAOP1VW2\nwQGG\r\n=wLKp\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDc+bXcB44cRt139pL26tqwrEuc/YGAIdJD8FitCKtigwIhANflzMi25s5x5ZXLZZEaY5qb/B+Zv4T8ProfeuVyMesP"}]}, "engines": {"node": ">= 10.0"}}, "1.0.4": {"name": "@ericblade/quagga2", "version": "1.0.4", "dependencies": {"@babel/polyfill": "^7.8.7", "get-pixels": "^3.3.2", "gl-mat2": "^1.0.1", "gl-vec2": "^1.3.0", "gl-vec3": "^1.1.3", "lodash": "^4.17.15", "ndarray": "^1.0.19", "ndarray-linear-interpolate": "^1.0.0", "snyk": "^1.320.2"}, "devDependencies": {"@babel/core": "^7.9.6", "@babel/plugin-proposal-class-properties": "^7.8.3", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.8.3", "@babel/plugin-proposal-object-rest-spread": "^7.9.6", "@babel/plugin-proposal-optional-chaining": "^7.9.0", "@babel/plugin-transform-runtime": "^7.9.6", "@babel/preset-env": "^7.9.6", "@babel/preset-typescript": "^7.9.0", "@babel/runtime": "^7.9.6", "@cypress/code-coverage": "^3.7.2", "@cypress/webpack-preprocessor": "^5.2.0", "@std/esm": "^0.26.0", "@types/chai": "^4.2.11", "@types/gl-vec2": "^1.3.0", "@types/lodash": "^4.14.150", "@types/mocha": "^5.2.7", "@types/react": "^16.9.35", "@types/sinon": "^9.0.0", "@types/sinon-chai": "^3.2.4", "babel-loader": "^8.1.0", "babel-plugin-add-module-exports": "^1.0.2", "babel-plugin-istanbul": "^6.0.0", "chai": "^4.2.0", "core-js": "^3.6.5", "cross-env": "^7.0.2", "cypress": "^4.5.0", "eslint": "^6.8.0", "mocha": "^5.2.0", "nyc": "^15.0.1", "sinon": "^9.0.2", "sinon-chai": "^3.5.0", "source-map-loader": "^0.2.4", "ts-mocha": "^7.0.0", "ts-node": "^8.10.1", "typescript": "^3.8.3", "webpack": "^4.43.0", "webpack-cli": "^3.3.11"}, "directories": {"doc": "doc"}, "dist": {"integrity": "sha512-naQHMLFifExzHIEQgqDlfGCePO1zDjbWj5GU534pN/xNvoRGY6JP/qYXHhsR9pjcO16ihgZKBABl0FcWkhUHWg==", "shasum": "c4881473f9cc3c8a9782752de52e724daf4f62f0", "tarball": "https://registry.npmjs.org/@ericblade/quagga2/-/quagga2-1.0.4.tgz", "fileCount": 13, "unpackedSize": 5557656, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJevHz1CRA9TVsSAnZWagAArigP/jRhrMzIlJjapvsstUan\nwuT5wNKuqHWymwqBhjBKukdCYbwQme65q5UnE6L+8h+P8ep9HwXctXN+afqI\noEXtKl4Iw6g7Wv/SyZQVi4c4J27A1hiT8X7Q/uZApW4FKhU4cFn9FNgvB5pb\nVnFkPiD1DyFyaz76Ody+CyLR9XEnPa6wwZzbS7rllhU9+y5AALpg56PKFfcE\nytjvf1IBEmMlHOSTMCBI2Yj64mPHclyjzyGW8MQwlc74XSbQp4OsWTe83PNG\nbHspNn2V6ENQhmST5m/9Dxj2a1GkylqLVbOfFgp9DrevpAGD2sEswlv7NlId\nZWyjNUANpLRx1vKw0qbPoqklSyTee6Y4Wc5U4jL5HabLxOPbO/gx0WlKHIF0\noFTm4Ssd5So4Fy3xQU5Fy7JzKH3eedfnnx5WckJNoNUh7PRDqaJe0/Lbo0Yk\n+972psAoBg9n/IE/nMXy1Xtj+x62AwrW11rOCn8bdXiszQu+wZRzwwSlIEGt\n2cQXsSOj2KzC9zczrpEvbkH++We2ipaBU8Dbl3Xh35JrCYhuWt3v2Csttnc+\n5RcbaLe1wEUV220ArOMPa1+E36RVlJHwP6zsDulWWF8OKlDDusdQfZgjLmp4\nfYFKyVDSXQdihyQFy2antyf5ZB/aPZAWKFpJ4yF8BiagjD5UiOVImVFjhNnQ\nV7Wu\r\n=rILd\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD7jQK6+bJtNKKGPOnejsgP76S+MhmuxdvpBRaZEdkcCQIgMS1RHCqN73F3MkY7BJ5+QFEA6OVAMLRZMXR4cDQ7DP0="}]}, "engines": {"node": ">= 10.0"}}, "1.0.5": {"name": "@ericblade/quagga2", "version": "1.0.5", "dependencies": {"@babel/polyfill": "^7.8.7", "get-pixels": "^3.3.2", "gl-mat2": "^1.0.1", "gl-vec2": "^1.3.0", "gl-vec3": "^1.1.3", "lodash": "^4.17.15", "ndarray": "^1.0.19", "ndarray-linear-interpolate": "^1.0.0", "snyk": "^1.321.0"}, "devDependencies": {"@babel/core": "^7.9.6", "@babel/plugin-proposal-class-properties": "^7.8.3", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.8.3", "@babel/plugin-proposal-object-rest-spread": "^7.9.6", "@babel/plugin-proposal-optional-chaining": "^7.9.0", "@babel/plugin-transform-runtime": "^7.9.6", "@babel/preset-env": "^7.9.6", "@babel/preset-typescript": "^7.9.0", "@babel/runtime": "^7.9.6", "@cypress/code-coverage": "^3.7.4", "@cypress/webpack-preprocessor": "^5.2.1", "@std/esm": "^0.26.0", "@types/chai": "^4.2.11", "@types/gl-vec2": "^1.3.0", "@types/lodash": "^4.14.151", "@types/mocha": "^5.2.7", "@types/react": "^16.9.35", "@types/sinon": "^9.0.1", "@types/sinon-chai": "^3.2.4", "@typescript-eslint/eslint-plugin": "^2.33.0", "babel-loader": "^8.1.0", "babel-plugin-add-module-exports": "^1.0.2", "babel-plugin-istanbul": "^6.0.0", "chai": "^4.2.0", "core-js": "^3.6.5", "cross-env": "^7.0.2", "cypress": "^4.5.0", "eslint": "^6.8.0", "eslint-config-airbnb-typescript": "^7.2.1", "eslint-plugin-import": "^2.20.2", "eslint-plugin-jsx-a11y": "^6.2.3", "eslint-plugin-react": "^7.20.0", "mocha": "^5.2.0", "nyc": "^15.0.1", "sinon": "^9.0.2", "sinon-chai": "^3.5.0", "source-map-loader": "^0.2.4", "ts-mocha": "^7.0.0", "ts-node": "^8.10.1", "typescript": "^3.9.2", "webpack": "^4.43.0", "webpack-cli": "^3.3.11"}, "directories": {"doc": "doc"}, "dist": {"integrity": "sha512-Wufwgclg+2FaKbD+5KTN2eojNXA8NSS0w63Fuay8+bi/4/9iDv3FcyDa/dxHkhrIzzqXpoEQjqecdCUFXzb2gg==", "shasum": "0c96677cf2990037112a414f934eb61f486dc4a6", "tarball": "https://registry.npmjs.org/@ericblade/quagga2/-/quagga2-1.0.5.tgz", "fileCount": 13, "unpackedSize": 5556292, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJewtKfCRA9TVsSAnZWagAAQ7YP/0/3V7qmcEmzt3y9B0uz\nRzGfzDlHIIEN5LkZvTyZrdWb70kF1As1hYEfmGTFWU7pap5cWvYMNnVBe1xK\nuNVnUfDq/UzxxuIYfLTnmOtEhaJNrGGi+GHqQ64W+mdyC9idktReaVqXigo5\ncjwpFTluFJH0Msk4Al3TbESWS+EHOnb79o16qDKkMOm1FdiyyTeol0rKfGeI\nRsIl5R8pAHFTNO+m4zvej9UgNSs60EbAwGdmCo6z7Ms9rkX9c3gJgZfGaOa4\nYtW5+p0J9saJGNFtmKYQ2JGY3iWhMrNrGfjUZrEJ7NpR3zWDZ9Ii49BmymzF\nF3y3vm8Gt+ECGEpNu86fToVUxc62Wn1AD1CS7ub8cMbmQzNibLpRR82jhylO\n4F/G4gfZ+lmJ1N+ma32Qqn3dyZOI25XnqL1twFSmha6cByyl4e+bA6xOMgYv\nUQc2XNqdltJTo/o4/2kGhpLwypvuSCykX/BWG+0dWjVNLH/Bf/7CERxE2HvF\nN2Q2FL/50Hzh7hoGQyHbd7fku8/UKrpXWOHjAG1bnETfC8XvGtjLP6u9wGwF\nCtQHXcQHKY/jccRyDjyAM/tLvc1iLDbxpkps98HEAFhJp5GI/G0wJurr8ujl\nelDtx/rGYBahc+PZfZXSQbE5PAy0GABBTqGzBcjXzaupWPSfIdfzhOXqXr6r\n8cDc\r\n=coxH\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCTaSb5L/uO2ywd01xC7wVZ0pZIzgB9/rmd5jVtqcRtuwIhANCbC7UnLPta1/pUwuqNCdg4XDftRqPPvx5Mqh48Mvlc"}]}, "engines": {"node": ">= 10.0"}}, "1.0.6": {"name": "@ericblade/quagga2", "version": "1.0.6", "dependencies": {"@babel/polyfill": "^7.8.7", "get-pixels": "^3.3.2", "gl-mat2": "^1.0.1", "gl-vec2": "^1.3.0", "gl-vec3": "^1.1.3", "lodash": "^4.17.15", "ndarray": "^1.0.19", "ndarray-linear-interpolate": "^1.0.0", "snyk": "^1.323.1"}, "devDependencies": {"@babel/core": "^7.9.6", "@babel/plugin-proposal-class-properties": "^7.8.3", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.8.3", "@babel/plugin-proposal-object-rest-spread": "^7.9.6", "@babel/plugin-proposal-optional-chaining": "^7.9.0", "@babel/plugin-transform-runtime": "^7.9.6", "@babel/preset-env": "^7.9.6", "@babel/preset-typescript": "^7.9.0", "@babel/runtime": "^7.9.6", "@cypress/code-coverage": "^3.7.4", "@cypress/webpack-preprocessor": "5.2.1", "@std/esm": "^0.26.0", "@types/chai": "^4.2.11", "@types/gl-vec2": "^1.3.0", "@types/lodash": "^4.14.152", "@types/mocha": "^5.2.7", "@types/react": "^16.9.35", "@types/sinon": "^9.0.3", "@types/sinon-chai": "^3.2.4", "@typescript-eslint/eslint-plugin": "^3.0.0", "babel-loader": "^8.1.0", "babel-plugin-add-module-exports": "^1.0.2", "babel-plugin-istanbul": "^6.0.0", "chai": "^4.2.0", "core-js": "^3.6.5", "cross-env": "^7.0.2", "cypress": "4.5.0", "eslint": "^6.8.0", "eslint-config-airbnb-typescript": "^7.2.1", "eslint-plugin-import": "^2.20.2", "eslint-plugin-jsx-a11y": "^6.2.3", "eslint-plugin-react": "^7.20.0", "mocha": "^5.2.0", "nyc": "^15.0.1", "sinon": "^9.0.2", "sinon-chai": "^3.5.0", "source-map-loader": "^0.2.4", "ts-mocha": "^7.0.0", "ts-node": "^8.10.1", "typescript": "^3.9.3", "webpack": "^4.43.0", "webpack-cli": "^3.3.11"}, "directories": {"doc": "doc"}, "dist": {"integrity": "sha512-mQ9hEUJAfo5RpCjWAJd9XwZN8ERD5PNQ3R02NEmW51WKC/wasKvlEyKqBqnCFHA1IQdCkrcvXm8Vg/Es/gY2Ig==", "shasum": "7f74db906ff32412f562d22531417e5a33bf9090", "tarball": "https://registry.npmjs.org/@ericblade/quagga2/-/quagga2-1.0.6.tgz", "fileCount": 13, "unpackedSize": 5572810, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeyCNoCRA9TVsSAnZWagAAlEQP/1NECE1pZeF8+1dnKNUg\nXmhc1XNCsFNFgr27EUYZPGL/f2vKJcyAjKJUk/xlawiTR2Qe6cnS9yF6bfHo\ndzvKda7RjKn92rbvaidlgOlWkeauCEXi4+DcPyYsOI87jP56sbq+ylDz3Jfa\nqUDezqYAlEPIfHG/9lFVc9YT5fBFaZuQThbBXbhrCRJLNm2OnY74nB0hM895\nKa3XhNGOyTHT8ODZNM78s6sh75puyQu9QKnRDOZLHjdoy3Gw1YLPG98PcyPE\nqcdIiF3/HVy+WYWU7x8CbZvzYSQTVq58YwcBhcpLLHrQEVC9rCpqmMyVYMdQ\nNkHWWoFnVk+pQJBCgiAwmbrM5pULo22qnhmL75/YgBuTxISplF9afKIbwmdw\nDeq3scksKKU+OcOSc97jcfXnG/crOMm8QZ1HPUwsaHEchs7L4R9L8bdTAPtH\nasMgAvGhOp7pujN+TWLRe5aQvqaJZ7nMoLuAbIPWI+YqzYqVmhb72WQNBRaZ\nOfzalFe4j/YzKosu1v0p49Q/StChRdyRWUzL49sx7XG8d8wNrXgDQ8jNjvfv\nhZsPWOSaQL3Yj8aePDol8wwFYnCDRzzxrQ5xyeSgonG9cWkLXprNghEQ6df7\nKF6F5m8P3Q5GydZXhkyebcW6G+rRdAbOIQFj8oiayQ3ekXM9QGOEALhhn3b6\nizKm\r\n=Ec53\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEEFslK8OU0yYSRGrQ/Z/8gSwb9mXE6qT7fFDoB63VppAiEA2uKn1RyAEb7cxk5ii4nY1xcvS8HFEMQw5hYDDAEa0/4="}]}, "engines": {"node": ">= 10.0"}}, "1.1.0": {"name": "@ericblade/quagga2", "version": "1.1.0", "dependencies": {"@babel/polyfill": "^7.8.7", "get-pixels": "^3.3.2", "gl-mat2": "^1.0.1", "gl-vec2": "^1.3.0", "gl-vec3": "^1.1.3", "lodash": "^4.17.15", "ndarray": "^1.0.19", "ndarray-linear-interpolate": "^1.0.0", "snyk": "^1.323.1"}, "devDependencies": {"@babel/core": "^7.9.6", "@babel/plugin-proposal-class-properties": "^7.8.3", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.8.3", "@babel/plugin-proposal-object-rest-spread": "^7.9.6", "@babel/plugin-proposal-optional-chaining": "^7.9.0", "@babel/plugin-transform-runtime": "^7.9.6", "@babel/preset-env": "^7.9.6", "@babel/preset-typescript": "^7.9.0", "@babel/runtime": "^7.9.6", "@cypress/code-coverage": "^3.7.4", "@cypress/webpack-preprocessor": "5.2.1", "@std/esm": "^0.26.0", "@types/chai": "^4.2.11", "@types/gl-vec2": "^1.3.0", "@types/lodash": "^4.14.152", "@types/mocha": "^5.2.7", "@types/react": "^16.9.35", "@types/sinon": "^9.0.3", "@types/sinon-chai": "^3.2.4", "@typescript-eslint/eslint-plugin": "^3.0.0", "babel-loader": "^8.1.0", "babel-plugin-add-module-exports": "^1.0.2", "babel-plugin-istanbul": "^6.0.0", "chai": "^4.2.0", "core-js": "^3.6.5", "cross-env": "^7.0.2", "cypress": "4.5.0", "eslint": "^6.8.0", "eslint-config-airbnb-typescript": "^8.0.0", "eslint-plugin-import": "^2.20.2", "eslint-plugin-jsx-a11y": "^6.2.3", "eslint-plugin-react": "^7.20.0", "mocha": "^5.2.0", "nyc": "^15.0.1", "sinon": "^9.0.2", "sinon-chai": "^3.5.0", "source-map-loader": "^0.2.4", "ts-mocha": "^7.0.0", "ts-node": "^8.10.1", "typescript": "^3.9.3", "webpack": "^4.43.0", "webpack-cli": "^3.3.11"}, "directories": {"doc": "doc"}, "dist": {"integrity": "sha512-gdWG6RHreXJpjGSLJUOHoTTSc/rdWG7+JgrHi/w60dledgAX3C/4psvVP4b3Shsy5m/Xqts5uEgsHvFO0Znz0Q==", "shasum": "ddc2b68b75e4eb372f3f0f1744f47289db9c87c6", "tarball": "https://registry.npmjs.org/@ericblade/quagga2/-/quagga2-1.1.0.tgz", "fileCount": 13, "unpackedSize": 5574485, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe4n4vCRA9TVsSAnZWagAAQywP/j8FpGlx9T4Dd/ipx1Im\nRJ4WBOp4E0C2+Z6tQtSmabxKmQ1NXmtGS9CHBpHa31NzBVRmakH5L6DwgiwR\n691lmVk1K6nW1RlKAqFd6ota1nbjLKyoWIaw3U2k5Am1R49veESqSwIMNPtf\n8H9V6XGhaUxEfR36JKqtalBaJEY1i4a+jfjq/JAebRCinXVLgjS322wwzwzz\nXpV8KINPVdEDI4p3uXQx/9rHz3LRrM8+mymPZcnoaKj4h0N0O9mn6YKhGLiA\nh4CRZGe27Y2fobdNW4rq3zkxeRjQtWtTvmOY5cpbc2Qcgx1AOttdhtg26glv\nO8iVQrDvQNn8piSSfHKTQMDUurwiEQCbkvCoMiHwc3m8CDagZRuQFyJyxzNU\nusz27oj6pwSz865DhPKsEIT17Ye4P3By7/oxcFuC3LgS639aGpJkAB/OC4NX\nSy3w51bah+aPaxZ3UjVA8+02H6HsK8oWL0xqOZTUc9v1LyYIlSE5McAFPOTw\nPIuuKuaLhoYRUMBurwkaNzms38SXJYiLC2LfNebolAJKKB1G47GHujGLMGPb\ncNHiOziV0u1SO4m/rMhPSw9W4uPQSyKmybnOMu6MPdRgO4Xn5dt+X6vmNs+T\n5eOeQ3nw3XannmR67i8gl2fgtK6h/XvOPSbN8d1np9BiVq112ierUMS68vtM\nw61d\r\n=61cY\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC124F0ammQT0fi6Xikf1Ai2Nr/OpSymfKsESADwvfxogIhAMpoSQvnqcZ4nyT98pUltmc9352IDogx/NppY8EdP67T"}]}, "engines": {"node": ">= 10.0"}}, "1.2.0": {"name": "@ericblade/quagga2", "version": "1.2.0", "dependencies": {"@babel/polyfill": "^7.10.1", "get-pixels": "^3.3.2", "gl-mat2": "^1.0.1", "gl-vec2": "^1.3.0", "gl-vec3": "^1.1.3", "lodash": "^4.17.15", "ndarray": "^1.0.19", "ndarray-linear-interpolate": "^1.0.0", "snyk": "^1.348.2"}, "devDependencies": {"@babel/core": "^7.10.3", "@babel/plugin-proposal-class-properties": "^7.10.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.10.1", "@babel/plugin-proposal-object-rest-spread": "^7.10.3", "@babel/plugin-proposal-optional-chaining": "^7.10.3", "@babel/plugin-transform-runtime": "^7.10.3", "@babel/preset-env": "^7.10.3", "@babel/preset-typescript": "^7.10.1", "@babel/runtime": "^7.10.3", "@cypress/code-coverage": "^3.8.1", "@cypress/webpack-preprocessor": "5.4.1", "@std/esm": "^0.26.0", "@types/chai": "^4.2.11", "@types/gl-vec2": "^1.3.0", "@types/lodash": "^4.14.157", "@types/mocha": "^5.2.7", "@types/react": "^16.9.41", "@types/sinon": "^9.0.4", "@types/sinon-chai": "^3.2.4", "@typescript-eslint/eslint-plugin": "^3.4.0", "@typescript-eslint/parser": "^3.4.0", "babel-loader": "^8.1.0", "babel-plugin-add-module-exports": "^1.0.2", "babel-plugin-istanbul": "^6.0.0", "chai": "^4.2.0", "core-js": "^3.6.5", "cross-env": "^7.0.2", "cypress": "^4.9.0", "eslint": "^7.3.1", "eslint-config-airbnb-typescript": "^8.0.2", "eslint-plugin-import": "^2.21.2", "eslint-plugin-jsx-a11y": "^6.3.1", "eslint-plugin-react": "^7.20.0", "eslint-plugin-react-hooks": "^4.0.4", "mocha": "^5.2.0", "nyc": "^15.1.0", "sinon": "^9.0.2", "sinon-chai": "^3.5.0", "source-map-loader": "^1.0.0", "ts-mocha": "^7.0.0", "ts-node": "^8.10.2", "typescript": "^3.9.5", "webpack": "^4.43.0", "webpack-cli": "^3.3.12"}, "directories": {"doc": "doc"}, "dist": {"integrity": "sha512-NgmhcJPkycI9zpwBcb836tjdyGYpFb+PQIltGtMX4kZGRmKAjm/svSJ2MRmAfOUD7voP5gkFyPmRYKg3WmDpPw==", "shasum": "94ce4ddcb397a472230c99dbaa71280d31331688", "tarball": "https://registry.npmjs.org/@ericblade/quagga2/-/quagga2-1.2.0.tgz", "fileCount": 13, "unpackedSize": 5653840, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe9puwCRA9TVsSAnZWagAA4CwQAIce8/mgmyZRgsQuFEfh\nyvmTEqqwbx8ZHyu0e0PTg7NX9WoiiGk2yP8lEqHdskcm7YhKk2k7ATSboa63\naTgP4/5kMTr5ouJXoK+hQ6xJKAeuU77Is9z9dERogx//2UAt7MbfLA5WH0JV\nQ5hZzL7Xc8s44HdpDRc6omz3WmL0RwAZXpkRI9LuYXFgmpEY2VZMgRkJ2wVl\n9Eo5uxyI7GZDjGW8smSAeZGal4ByCsGJwdJKUiM1QtcqSbh7RKi7j0sShr3S\n9c65XkldD5aS8UZ9fyHBPWkYW8jsW67FUtz86pwMIIaw/iWdePoT+m3K673G\noN31829aRDpJfAH8KJuhPDxW/0t3lgHDxK2Oo+g0HRwfY5YdzYM8SwUElf2u\nCezfKD3dcnuIK95dE47P8yMvoKL6BBlI4Gm70OzR4cypZsN8Pw4tRXMpvXgA\nwPogX9+2BdOUB6roUn6G++QI2Sze09o/bOASAAiVhR8cSetBX1NkunTqX7GU\nj22DJoIL2xG3EtBsRfkB5yGHZc6EuyXTjW8I1SXpRGwdM3Tfnz2aUptujiO9\n9tkVjX/kLS9/bTYDKkatIjixVnHUQBufrhrLx3GxCjazyGyzuaY4CBw0fTu3\nlZ+pNhFQDOOGV4O6FHfkfIjuj1AGX9DFU6zOw7L7xU+96iTCZUtDCvxQmMI8\nhKNp\r\n=L9io\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD7rEpBx4tMuTNdF+pZlaUCWM0ikU4vLsBNsFtwP6LO1gIgNl3fUYD8WqS0I2YnmdQRn8QlfaIraJIWcOw9MB1qwR8="}]}, "engines": {"node": ">= 10.0"}}, "1.2.1": {"name": "@ericblade/quagga2", "version": "1.2.1", "dependencies": {"@babel/polyfill": "^7.10.4", "get-pixels": "^3.3.2", "gl-mat2": "^1.0.1", "gl-vec2": "^1.3.0", "gl-vec3": "^1.1.3", "lodash": "^4.17.19", "ndarray": "^1.0.19", "ndarray-linear-interpolate": "^1.0.0", "snyk": "^1.361.3"}, "devDependencies": {"@babel/core": "^7.10.4", "@babel/plugin-proposal-class-properties": "^7.10.4", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.10.4", "@babel/plugin-proposal-object-rest-spread": "^7.10.4", "@babel/plugin-proposal-optional-chaining": "^7.10.4", "@babel/plugin-transform-runtime": "^7.10.4", "@babel/preset-env": "^7.10.4", "@babel/preset-typescript": "^7.10.4", "@babel/runtime": "^7.10.4", "@cypress/code-coverage": "^3.8.1", "@cypress/webpack-preprocessor": "5.4.1", "@std/esm": "^0.26.0", "@types/chai": "^4.2.11", "@types/gl-vec2": "^1.3.0", "@types/lodash": "^4.14.157", "@types/mocha": "^5.2.7", "@types/react": "^16.9.41", "@types/sinon": "^9.0.4", "@types/sinon-chai": "^3.2.4", "@typescript-eslint/eslint-plugin": "^3.6.0", "@typescript-eslint/parser": "^3.6.0", "babel-loader": "^8.1.0", "babel-plugin-add-module-exports": "^1.0.2", "babel-plugin-istanbul": "^6.0.0", "chai": "^4.2.0", "core-js": "^3.6.5", "cross-env": "^7.0.2", "cypress": "^4.10.0", "eslint": "^7.4.0", "eslint-config-airbnb-typescript": "^8.0.2", "eslint-plugin-import": "^2.22.0", "eslint-plugin-jsx-a11y": "^6.3.1", "eslint-plugin-react": "^7.20.3", "eslint-plugin-react-hooks": "^4.0.6", "mocha": "^5.2.0", "nyc": "^15.1.0", "sinon": "^9.0.2", "sinon-chai": "^3.5.0", "source-map-loader": "^1.0.1", "ts-mocha": "^7.0.0", "ts-node": "^8.10.2", "typescript": "^3.9.6", "webpack": "^4.43.0", "webpack-cli": "^3.3.12"}, "directories": {"doc": "doc"}, "dist": {"integrity": "sha512-eBFqcehqFcqbkOIZidYq5frTapJKbRhaC/x5Rm4nEQ0OdXbj18iCUUz3FzGkWLL3P2X4QDto4uEWuKP4CHQ2Eg==", "shasum": "23a0e50e5c9f9010e94b52469f2b2bb5638ed63e", "tarball": "https://registry.npmjs.org/@ericblade/quagga2/-/quagga2-1.2.1.tgz", "fileCount": 13, "unpackedSize": 5660034, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfBxfkCRA9TVsSAnZWagAA08oQAJDhDxBv+DzziU9Re+VE\neIx6op31dGsEAmjdLomi/e54QAhp8/MdDRhIf/TAIJcMYuPM9KeZsjk/HFsj\nnhXaacN9xfxC/2sdBzBQFE2JA1J4B8yfXjEx1hJNxCvppMCb3dSxI9MYzwUx\nJOlGI3Bxi+33gNOBfdEogeXq8O/VeQcPr4Ku0QV6MjgqFrnICWhwunTloX+8\n3/mq09MRvdQo77LdOK/woo/eJN93NCFAteQ9RSqeUwoXEV2lpf8PQx0iSs63\nHoXwzEqnZDFzJX49IjaOxEFjk8HOEEOH51TdVeqSj1z+mM0q2P2zb5fl7tfP\nhqunorj8UBBKl5obIXrUppvXPrlr3WvXJVj6rO3lbHtonWg87pld3m6Tq2AF\nzuIFz915bb0Aro3zhkOqWXxQcqCuq8HJzAmQfFeD/OR0AbkqIO/ae0QJcCi3\ng4zdi4SkN2LNselS+e93KF88FhkgMSzVbAkqXvFcRhG4MukELTTgeZ0yX0+D\n+2RJO5je1ZC0ZeO/20ixNcowChx0reRQ5Y5t/gyCasJFqnD6kK9ncrf00F31\nt8m5FD7/7g1vJS6fhnBt2eJjTPdNvV0AxxQBoLza0fgwoEQhnjVTqGMKb4iX\nhqm1dGQm3jNSvPcKRy5y2SkZL5W5bC7oa3UkK6EifEplPQAonPzfRFqrJCZJ\nTFLm\r\n=B/lH\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHWZcKZXBV5qW21AjSdcetqrDaPa49ndPqEoRf9H2GTYAiAw1b3Qx4RSuvcMqvqBUyyTc9C5vQyibHwS+hm5oEQb5g=="}]}, "engines": {"node": ">= 10.0"}}, "1.2.2": {"name": "@ericblade/quagga2", "version": "1.2.2", "dependencies": {"@babel/polyfill": "^7.10.4", "get-pixels": "^3.3.2", "gl-mat2": "^1.0.1", "gl-vec2": "^1.3.0", "gl-vec3": "^1.1.3", "lodash": "^4.17.20", "ndarray": "^1.0.19", "ndarray-linear-interpolate": "^1.0.0", "snyk": "^1.374.0"}, "devDependencies": {"@babel/core": "^7.11.1", "@babel/plugin-proposal-class-properties": "^7.10.4", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.10.4", "@babel/plugin-proposal-object-rest-spread": "^7.11.0", "@babel/plugin-proposal-optional-chaining": "^7.11.0", "@babel/plugin-transform-runtime": "^7.11.0", "@babel/preset-env": "^7.11.0", "@babel/preset-typescript": "^7.10.4", "@babel/runtime": "^7.11.2", "@cypress/code-coverage": "^3.8.1", "@cypress/webpack-preprocessor": "5.4.4", "@types/chai": "^4.2.12", "@types/gl-vec2": "^1.3.0", "@types/lodash": "^4.14.159", "@types/mocha": "^5.2.7", "@types/react": "^16.9.46", "@types/sinon": "^9.0.4", "@types/sinon-chai": "^3.2.4", "@typescript-eslint/eslint-plugin": "^3.9.0", "@typescript-eslint/parser": "^3.9.0", "babel-loader": "^8.1.0", "babel-plugin-add-module-exports": "^1.0.2", "babel-plugin-istanbul": "^6.0.0", "chai": "^4.2.0", "core-js": "^3.6.5", "cross-env": "^7.0.2", "cypress": "^4.12.1", "eslint": "^7.7.0", "eslint-config-airbnb-typescript": "^9.0.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-jsx-a11y": "^6.3.1", "eslint-plugin-react": "^7.20.6", "eslint-plugin-react-hooks": "^4.0.8", "esm": "^3.2.25", "mocha": "^5.2.0", "nyc": "^15.1.0", "sinon": "^9.0.3", "sinon-chai": "^3.5.0", "source-map-loader": "^1.0.1", "ts-mocha": "^7.0.0", "ts-node": "^8.10.2", "typescript": "^4.1.0-dev.20200814", "webpack": "^4.44.1", "webpack-cli": "^3.3.12"}, "directories": {"doc": "doc"}, "dist": {"integrity": "sha512-s7PgiFXn/pX07RpnLCuKa26gpXwP+JAdeYhyRsPTzhkyfPW9Mq2oscnkctKIAbgLNl48mxO4acW3KYMkBA5GMg==", "shasum": "cb4c3212b55c4d1e352f927e0565a4cd5a025e1a", "tarball": "https://registry.npmjs.org/@ericblade/quagga2/-/quagga2-1.2.2.tgz", "fileCount": 13, "unpackedSize": 5646893, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfQ+dSCRA9TVsSAnZWagAAVsAP/2ZXGmlghfnbsMiagbp+\n7o1GXDhO6mzEBwMIoaDlGLWprcPLZ4xQk8SuhhQo//Z7ZHovU88uF/6YtYJA\n0sWxwqJf9wiAXFJXbwowmhZZ3p25+8JPUf6YOOFqkJ7sx5Pt75YcfqSs2BcF\nClelreh1664v23YizExlgmcYGzW3HMp1Uwn+//7fM4DXNySMJZHaio4/SnWU\nD9Hj2gzVwhw67ldommKOYGyMor51Z+r2XVK5JOpRr50iHCohf7j4vYL2H1LL\nAtYMj4xfq5qe1s3bwIoVQDZWxNlYpRFSiKAC0QHQ920/XXsFi85RsyghCOO9\nrvyL3mHUWXB5Q85QP1Gc5bGDQ6QodE4xIWFsSg53VDciplVJvXwZ+KZMOvRx\nvq2wYgorw2ZJKXrICy4DJkiuOMUK1jY1YYiUZoOVua0jQH0uZDsjxJ/T5gjq\nr8940FR3wjAjTIM9m00ayArCGQszR9RofBpQhq1xgwosDNR4U8rxZJMUlqkK\niDGdbwzfIAB5ile+qMYClDFpeRZK2eWQNftO4ijpA2coocPmNypHw7zGNhOB\nZ4ptDqJlTEdGUcyI17WA9OG7pq7yBjgxSxq3x+VXbGo1GNpvRd/XVYnFEVBO\nHYRQ6Td206czGcdXvXJq6R+LVv3U7VWd53pjsSySZ6EyLO3q4jsQNbo+NgVb\nuRSw\r\n=eIRo\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD6/BDMlcr9/4TK4+ZBKCNBESvZYiQ0Rlk6F7q6krJRgwIhAId1QfdMPEtl6zVijCuwAwyjCyFJoMXbkiFbYd1sWQSe"}]}, "engines": {"node": ">= 10.0"}}, "1.2.3": {"name": "@ericblade/quagga2", "version": "1.2.3", "dependencies": {"@babel/polyfill": "^7.11.5", "get-pixels": "^3.3.2", "gl-mat2": "^1.0.1", "gl-vec2": "^1.3.0", "gl-vec3": "^1.1.3", "lodash": "^4.17.20", "ndarray": "^1.0.19", "ndarray-linear-interpolate": "^1.0.0", "snyk": "^1.398.1"}, "devDependencies": {"@babel/core": "^7.11.1", "@babel/plugin-proposal-class-properties": "^7.10.4", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.10.4", "@babel/plugin-proposal-object-rest-spread": "^7.11.0", "@babel/plugin-proposal-optional-chaining": "^7.11.0", "@babel/plugin-transform-runtime": "^7.11.0", "@babel/preset-env": "^7.11.0", "@babel/preset-typescript": "^7.10.4", "@babel/runtime": "^7.11.2", "@cypress/code-coverage": "^3.8.1", "@cypress/webpack-preprocessor": "5.4.4", "@types/chai": "^4.2.12", "@types/gl-vec2": "^1.3.0", "@types/lodash": "^4.14.159", "@types/mocha": "^5.2.7", "@types/react": "^16.9.46", "@types/sinon": "^9.0.4", "@types/sinon-chai": "^3.2.4", "@typescript-eslint/eslint-plugin": "^3.9.0", "@typescript-eslint/parser": "^3.9.0", "babel-loader": "^8.1.0", "babel-plugin-add-module-exports": "^1.0.2", "babel-plugin-istanbul": "^6.0.0", "chai": "^4.2.0", "core-js": "^3.6.5", "cross-env": "^7.0.2", "cypress": "^4.12.1", "eslint": "^7.7.0", "eslint-config-airbnb-typescript": "^9.0.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-jsx-a11y": "^6.3.1", "eslint-plugin-react": "^7.20.6", "eslint-plugin-react-hooks": "^4.0.8", "esm": "^3.2.25", "mocha": "^5.2.0", "nyc": "^15.1.0", "sinon": "^9.0.3", "sinon-chai": "^3.5.0", "source-map-loader": "^1.0.1", "ts-mocha": "^7.0.0", "ts-node": "^8.10.2", "typescript": "^4.1.0-dev.20200814", "webpack": "^4.44.1", "webpack-cli": "^3.3.12"}, "directories": {"doc": "doc"}, "dist": {"integrity": "sha512-xVdg5NZ0o5V8LxKlNDVwLQxbTL/r7M/875WSeqVtGEPisnzHhLWtp8FKQfHdiNtONxOnW0Bw4VorceoGu4u/cQ==", "shasum": "2a0f5e84a7a2ac93c7a1b1931fa6feac363d96cc", "tarball": "https://registry.npmjs.org/@ericblade/quagga2/-/quagga2-1.2.3.tgz", "fileCount": 14, "unpackedSize": 5650403, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfgUgNCRA9TVsSAnZWagAAX0MP/A1EcZ4WcsLoVBEUI/pR\nT/jcsJcXFeT2A5oBHRAjfpu7qmGRkuUETNyqlDyDwVNhADavXMDvDOqsSoyg\nQ+66CHlHSklGDd4K4q7tgR8H6DJ9odo+5hfv3luz4nygwNR6JVrl7mhYR+H3\nLTGr//r49TJuD7dUVotFmst7/fDFygcNVZyFog8Kqxpn1ZQwx9EWgVhq1mzR\n4ji+21Ts+MC4/9vssku8d5roTOjf9z7PCIUkAKnsUZviSXtSKAVSgbS7W56N\nu9x/u08gS0NNO/IKiODZO+ZRor1G/e4zIVPbUJzFF/ygDOWxt5ZJRM1an0NV\nS+9syfh83ELXWWH8TnqZB/fxGj71VS5g6XDT6/oZ3zhqJSzYkjuKfLJ5ueAO\nOBntj7mn3byp+p77PJVWlBJobOD65u8HkiaZLIK3PcfWFw2ksnx1q1QhvzsP\nDKqPX1iFv5KpNYqZQ7OeESgJXeyKhm6KIrRJIou0urNaKRYI1uwtADRXQqlD\nuJnMWQvSjjc4z7hk7/gJALdrX25WG1LA+7rj0onCc3w8rIaxIXjuRJolYViD\nBB5wnfjuXYj5KQby/IUPm/5TsNoF0/ImRfBgdmiOlI3yoBgL2YvtOSjdNvGO\nbyZGJpoq6k1Xcu3SEZVJROwOxNAw7b7yWuXf7/IcE9xYLIPb65wcm30uJEhP\n/0pn\r\n=3DzH\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEs+YE79Ifv4LVuZyGLW7re0f1ak43LQb/JlApGFlFk6AiA4ls3YuYO9yvAc8QKijc8fdEhrEoRqSRMSFFXXm0FsWg=="}]}, "engines": {"node": ">= 10.0"}}, "1.2.4": {"name": "@ericblade/quagga2", "version": "1.2.4", "dependencies": {"@babel/polyfill": "^7.11.5", "get-pixels": "^3.3.2", "gl-mat2": "^1.0.1", "gl-vec2": "^1.3.0", "gl-vec3": "^1.1.3", "lodash": "^4.17.20", "ndarray": "^1.0.19", "ndarray-linear-interpolate": "^1.0.0", "snyk": "^1.413.2"}, "devDependencies": {"@babel/core": "^7.11.6", "@babel/plugin-proposal-class-properties": "^7.10.4", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.10.4", "@babel/plugin-proposal-object-rest-spread": "^7.11.0", "@babel/plugin-proposal-optional-chaining": "^7.11.0", "@babel/plugin-transform-runtime": "^7.11.5", "@babel/preset-env": "^7.11.5", "@babel/preset-typescript": "^7.10.4", "@babel/runtime": "^7.11.2", "@cypress/code-coverage": "^3.8.1", "@cypress/webpack-preprocessor": "5.4.6", "@types/chai": "^4.2.13", "@types/gl-vec2": "^1.3.0", "@types/lodash": "^4.14.161", "@types/mocha": "^5.2.7", "@types/react": "^16.9.51", "@types/sinon": "^9.0.8", "@types/sinon-chai": "^3.2.5", "@typescript-eslint/eslint-plugin": "^4.4.0", "@typescript-eslint/parser": "^4.4.0", "babel-loader": "^8.1.0", "babel-plugin-add-module-exports": "^1.0.4", "babel-plugin-istanbul": "^6.0.0", "chai": "^4.2.0", "core-js": "^3.6.5", "cross-env": "^7.0.2", "cypress": "^4.12.1", "eslint": "^7.11.0", "eslint-config-airbnb-typescript": "^11.0.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jsx-a11y": "^6.3.1", "eslint-plugin-react": "^7.21.4", "eslint-plugin-react-hooks": "^4.1.2", "esm": "^3.2.25", "mocha": "^5.2.0", "nyc": "^15.1.0", "sinon": "^9.2.0", "sinon-chai": "^3.5.0", "source-map-loader": "^1.1.1", "ts-mocha": "^7.0.0", "ts-node": "^9.0.0", "typescript": "^4.1.0-dev.20200814", "webpack": "^4.44.2", "webpack-cli": "^3.3.12"}, "directories": {"doc": "doc"}, "dist": {"integrity": "sha512-WiPsCqA3Ooczo7xCsXJB8Q5CQ632aTyjf0d8/yZ/XvE36fiHb+XGHfPAQ5BOT6ND028Qk1lrBKRZcONv2s4fhg==", "shasum": "229a6f7638bb1864280e873323c04ab5a7998f23", "tarball": "https://registry.npmjs.org/@ericblade/quagga2/-/quagga2-1.2.4.tgz", "fileCount": 14, "unpackedSize": 4609027, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfgyKYCRA9TVsSAnZWagAAlBkP/0nnbe4Gn52AXw6Q2kyW\n0Zdw89JR64g9qeSJUg66gq/jenYpsTGwROpTV8ar0cc80x2ySqhW0ND11fF1\nqQdUclSW6AVFZ0YJsTFVH6XN2ay6LqeA+xAvp1xl9/UEillprstaisxLoXEK\nNI869T6J9rpiVPb6G4/7LvWar0PnfJ7CBa1eM7L1RRfwpmGguHayyDgO0UvU\nT0bpbMdsbaVeOhcXVUq6T5vdmMCjxqGfdOJSlQM2zFJGwyX1S3MD9+05hAGJ\naLJYnZ5WgueePBY5P3r0KapBctv5SjAIyIb7NIbziooJzCKYdJSFc4krLFqx\n6VjF2SRlpExjqrGy5OzyGbxRwXxD6r65xv1oovwGZDlNnohn7smSQy3g2BV/\npiZfHNAolR0ErM163+26ZhEqcoJRxmIsXI8HKBvlxY+ETej1qX0f+lzS5MM6\nc9lwWVcoYPK/X/rQrBWqSm8ROvea2AhyY/vSS0vF3162VjuF4YggYePBOncc\noYGzXCATqBydv9XqMgIF44QSnkimcojQWsCyTJn4/vDGHogqBLsUJgdBOrdL\nUWjwHw6kKhKASGyzXvFZwqelDFTIXgTceVIrxaq5KAbFgVQCdlxg2O+KMiV6\nK1HAYx8Fl/n1AAxGggw4VPgJbErOMbfUGW45Act/lyjLkVI9FuHQNgRYcquH\nWcsh\r\n=4q9C\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCazEhIbkpAbsIKplZCx13svq/kwi9BnYrQCX7mPiQd5gIhAOVaRI96HVAuD4CftyZIojViJRM8dlP9Tkr30R5FhbMt"}]}, "engines": {"node": ">= 10.0"}}, "1.2.5": {"name": "@ericblade/quagga2", "version": "1.2.5", "dependencies": {"@babel/polyfill": "^7.12.1", "get-pixels": "^3.3.2", "gl-mat2": "^1.0.1", "gl-vec2": "^1.3.0", "gl-vec3": "^1.1.3", "lodash": "^4.17.20", "ndarray": "^1.0.19", "ndarray-linear-interpolate": "^1.0.0", "snyk": "^1.413.2"}, "devDependencies": {"@babel/core": "^7.11.6", "@babel/plugin-proposal-class-properties": "^7.10.4", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.10.4", "@babel/plugin-proposal-object-rest-spread": "^7.11.0", "@babel/plugin-proposal-optional-chaining": "^7.11.0", "@babel/plugin-transform-runtime": "^7.11.5", "@babel/preset-env": "^7.11.5", "@babel/preset-typescript": "^7.10.4", "@babel/runtime": "^7.11.2", "@cypress/code-coverage": "^3.8.1", "@cypress/webpack-preprocessor": "5.4.6", "@types/chai": "^4.2.13", "@types/gl-vec2": "^1.3.0", "@types/lodash": "^4.14.161", "@types/mocha": "^5.2.7", "@types/react": "^16.9.51", "@types/sinon": "^9.0.8", "@types/sinon-chai": "^3.2.5", "@typescript-eslint/eslint-plugin": "^4.4.0", "@typescript-eslint/parser": "^4.4.0", "babel-loader": "^8.1.0", "babel-plugin-add-module-exports": "^1.0.4", "babel-plugin-istanbul": "^6.0.0", "chai": "^4.2.0", "core-js": "^3.6.5", "cross-env": "^7.0.2", "cypress": "^4.12.1", "eslint": "^7.11.0", "eslint-config-airbnb-typescript": "^11.0.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jsx-a11y": "^6.3.1", "eslint-plugin-react": "^7.21.4", "eslint-plugin-react-hooks": "^4.1.2", "esm": "^3.2.25", "mocha": "^5.2.0", "nyc": "^15.1.0", "sinon": "^9.2.0", "sinon-chai": "^3.5.0", "source-map-loader": "^1.1.1", "ts-mocha": "^7.0.0", "ts-node": "^9.0.0", "typescript": "^4.1.0-dev.20200814", "webpack": "^4.44.2", "webpack-cli": "^3.3.12"}, "directories": {"doc": "doc"}, "dist": {"integrity": "sha512-FCj2amC/9v9L2V0atHTOis8Sh1gDo85vv4zjrXem5NOEYQWZj4sH5/13UI3cQqukX3CCysmnhaA4KPfZ6/RHwg==", "shasum": "a2d106010ca59e9af175204fe3873c93969f3f7e", "tarball": "https://registry.npmjs.org/@ericblade/quagga2/-/quagga2-1.2.5.tgz", "fileCount": 14, "unpackedSize": 3490074, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfqXCDCRA9TVsSAnZWagAA2MkP/ikCmFCOwcscXsFvWQQw\nxNEFv94+1Oyf3kgRTj2idmQPBKAPVH6Qw0GRNToeOcqnXoUwvv1o/f1fvNYJ\nmlK68Rqin6QRaG9wRIjso/YN8xkSTvOX6Z8jz+tq9NzUWBwV7vu+yCCffJUr\nN/YETvkrBJImtFtjEI1aIysDLi95tq/elNa+Gh7io5HeFMcXlCFsMLnZKi8G\n28idOpvlVz2p01DeS7sADKTuBeoFPTFzEZmfAjroaUqfvCLX9WT6+2b8+wra\nfmUp2UbNbFQtxR6EkXl0Sd1jT/Yw/czHPtmt16y/kw041MDpvLi2hG+3zwgd\n7r/4tfJxpphKbr1oTk9tUDAvj8RCdJQISXUQvbKNYLTeeRkkiTxXZpaGVxda\nJfwseeh7PoDY5ORdlJZN6xiLJRiqc8tSGGnc9h7wX2KJTykBev+NcbLqxIHg\n1oIiY4v++6SG6o+55ufP4kFuU47Xfqzqq5lPnMfoDT0BVEiowzaPrvZGih7S\nO4H0unXoMN38ioWgsHXSd1M7O6apkRxA0Zoh/zRSqKzKP/OMkPeFQWI/uSpL\nE81zW4WgMfE07ZlQ9xV3z8XSkX8AqwYIuGyrkEcBFCB70Z16JtW5FiYWGloL\nUOXbchmRUpPPZjOd97D1e5LCbD86dtWW4DOpIQCxIdQtXkfONtb7iX2NuOug\n5KYG\r\n=2KhB\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDwA4Y7KTgZYF6alDi4VrHWfcuZd2nW9XePjV7c5/SRlwIhALD2X340iieskbiwzb2GXlW07XOMPDkSJRPZDbTVMOc4"}]}, "engines": {"node": ">= 10.0"}}, "1.2.6": {"name": "@ericblade/quagga2", "version": "1.2.6", "dependencies": {"@babel/polyfill": "^7.12.1", "get-pixels": "^3.3.2", "gl-mat2": "^1.0.1", "gl-vec2": "^1.3.0", "gl-vec3": "^1.1.3", "lodash": "^4.17.20", "ndarray": "^1.0.19", "ndarray-linear-interpolate": "^1.0.0", "snyk": "^1.419.0"}, "devDependencies": {"@babel/core": "^7.11.6", "@babel/plugin-proposal-class-properties": "^7.10.4", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.10.4", "@babel/plugin-proposal-object-rest-spread": "^7.11.0", "@babel/plugin-proposal-optional-chaining": "^7.11.0", "@babel/plugin-transform-runtime": "^7.11.5", "@babel/preset-env": "^7.11.5", "@babel/preset-typescript": "^7.10.4", "@babel/runtime": "^7.11.2", "@cypress/code-coverage": "^3.8.1", "@cypress/webpack-preprocessor": "5.4.6", "@types/chai": "^4.2.13", "@types/gl-vec2": "^1.3.0", "@types/lodash": "^4.14.161", "@types/mocha": "^5.2.7", "@types/react": "^16.9.51", "@types/sinon": "^9.0.8", "@types/sinon-chai": "^3.2.5", "@typescript-eslint/eslint-plugin": "^4.4.0", "@typescript-eslint/parser": "^4.4.0", "babel-loader": "^8.1.0", "babel-plugin-add-module-exports": "^1.0.4", "babel-plugin-istanbul": "^6.0.0", "chai": "^4.2.0", "core-js": "^3.6.5", "cross-env": "^7.0.2", "cypress": "^4.12.1", "eslint": "^7.11.0", "eslint-config-airbnb-typescript": "^11.0.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jsx-a11y": "^6.3.1", "eslint-plugin-react": "^7.21.4", "eslint-plugin-react-hooks": "^4.1.2", "esm": "^3.2.25", "mocha": "^5.2.0", "nyc": "^15.1.0", "sinon": "^9.2.0", "sinon-chai": "^3.5.0", "source-map-loader": "^1.1.1", "ts-mocha": "^7.0.0", "ts-node": "^9.0.0", "typescript": "^4.1.0-dev.20200814", "webpack": "^4.44.2", "webpack-cli": "^3.3.12"}, "directories": {"doc": "doc"}, "dist": {"integrity": "sha512-ZO1b97yU7Sm9iIupVVz+QlAWaC508k1e5Z94vaUSKOycjvdbZp8pMQNm8HTf0zD1IQrc704bXtYwjORMYsZvnQ==", "shasum": "2a1a2332c61c89acbc0c3b16a5fd19ccd50c67d9", "tarball": "https://registry.npmjs.org/@ericblade/quagga2/-/quagga2-1.2.6.tgz", "fileCount": 14, "unpackedSize": 3385168, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfrKlYCRA9TVsSAnZWagAAEWUP/AhcO8RBavf/yB/W2PRm\njCng3Fiv28WsS/gWgt8V1QvAOAKek22h+RK7hZk7oLgVCokqmPzAy7gTa6r7\nIOnFKzD0JeDgKWyq6xqsGH3jxVWWlPR4DuGL5l8jYGa7CfhK+MhmyzzBQm9r\n/y/tUFfvlnfl/Th1ewz3yucw82SyT0yyUPx5CmTAXPQK75UDdNkAVzRwNUCk\nbdbXwQKrxUOmsKBF8tAITRSfPL8g6o0/tewMGZko+pl51u67csKr+8YlKRAk\neFkbWNrlmdCMVGNkZ1xKwB/SmnBY8DeD5zuwa4egl7QtDv/VHE1EoGc54uBQ\nibo+eKvP7FywBxWsL+KVS+hzmmyhYVlH+q95k3mjoKzG2ytcINMNWvtNrRNP\nX3vLLWgrQMj6Y4MYlSFej7/V4F6idc2xzfM5k3UJfalWmubFT1/g0oUaLrvf\n+9YHSAiRXppXX8IS2F5ZHz9LdgBkrU2j0WMvByhlp9sPhehHsgDxxQDs9I72\n05gQ3ldGlpbqR0dqcApfjT6Pjw1QGeYzii+zBrPKlDWkdO4bOvaQ5EVWmq5D\nJGezmHbYqe9IDDoFv4gjWukmP362oEncfhdZoBVEuargUEN5pNm9AU6NQt2Z\nmdKOM/BCp0uhF7YE3LQHCc3gyX9biJ82BhfWkIUcBcil4WUAgD9ZNveD51Z+\n2geq\r\n=uDUo\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCo4SrZX9WWRb8CwFR0sQnEPZEsugifgREwtmk7AvShfAIgfCuiXfHpCSbZltGfqnLH8K9i0Go3Xab7zmhClsXhvKk="}]}, "engines": {"node": ">= 10.0"}}, "1.3.0": {"name": "@ericblade/quagga2", "version": "1.3.0", "dependencies": {"@babel/polyfill": "^7.12.1", "get-pixels": "^3.3.2", "gl-mat2": "^1.0.1", "gl-vec2": "^1.3.0", "gl-vec3": "^1.1.3", "lodash": "^4.17.20", "ndarray": "^1.0.19", "ndarray-linear-interpolate": "^1.0.0", "snyk": "^1.449.0"}, "devDependencies": {"@babel/core": "^7.12.13", "@babel/plugin-proposal-class-properties": "^7.12.13", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.13", "@babel/plugin-proposal-object-rest-spread": "^7.12.13", "@babel/plugin-proposal-optional-chaining": "^7.12.13", "@babel/plugin-transform-runtime": "^7.12.15", "@babel/preset-env": "^7.12.13", "@babel/preset-typescript": "^7.12.13", "@babel/runtime": "^7.12.13", "@cypress/code-coverage": "^3.9.2", "@cypress/webpack-preprocessor": "5.5.0", "@types/chai": "^4.2.14", "@types/gl-vec2": "^1.3.0", "@types/lodash": "^4.14.168", "@types/mocha": "^5.2.7", "@types/react": "^17.0.1", "@types/sinon": "^9.0.10", "@types/sinon-chai": "^3.2.5", "@typescript-eslint/eslint-plugin": "^4.15.0", "@typescript-eslint/parser": "^4.15.0", "babel-loader": "^8.2.2", "babel-plugin-add-module-exports": "^1.0.4", "babel-plugin-istanbul": "^6.0.0", "chai": "^4.3.0", "core-js": "^3.8.3", "cross-env": "^7.0.3", "cypress": "^4.12.1", "eslint": "^7.19.0", "eslint-config-airbnb-typescript": "^12.3.1", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jsx-a11y": "^6.4.1", "eslint-plugin-react": "^7.22.0", "eslint-plugin-react-hooks": "^4.2.0", "esm": "^3.2.25", "mocha": "^5.2.0", "nyc": "^15.1.0", "sinon": "^9.2.4", "sinon-chai": "^3.5.0", "source-map-loader": "^1.1.1", "ts-mocha": "^8.0.0", "ts-node": "^9.1.1", "typescript": "^4.1.4", "webpack": "^4.44.2", "webpack-cli": "^3.3.12"}, "directories": {"doc": "doc"}, "dist": {"integrity": "sha512-xws5+np97OvcRkxkjWyQiLk0KtL5gU3bPS92XoOEeqOMbxGNGO5liPAmaxYyDyAZY3MEKNGMoO5GqBhQqbRhBQ==", "shasum": "95e9e01afc975b157700c0d5fa1240d1b4723900", "tarball": "https://registry.npmjs.org/@ericblade/quagga2/-/quagga2-1.3.0.tgz", "fileCount": 14, "unpackedSize": 4644958, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgOe0/CRA9TVsSAnZWagAASVsP/3DDhs8LpGVVdW6qk3Y7\nBm0Z8yhgrD8nWn6cC3quQsmh2gNwz64bjHORXq8ihLyHslxBI3E8tESzojOg\nLlE850u1WOELdyS6SDfid7deb65h6IiX6Z9cM8ppq1h5ILBMXLuR064xO2rE\nOPe4nquU7g8qdscbqf0/Rg4krvenR9osDOK5nv47G27TUO93nKiQ71aeV3rm\nN9infSv5qMYGi7VJd4kOdboesUmP6YfgGGdiS+FANz+HWWw5EjJraTNOIFZz\n6xkiQsNc9K2FXmdP45LZp7x7JV9yeibf1c/Zo10AHmM14UmHV34RoLG4MTCD\nuyTmVcGvEnDSqkI8tNsXd33IxEyjDCLIVM0HsECtmUp2KjXQiAe1Sqt18U+E\n9++MEQtO8C6iL8FmGzbocn3VheQ91YVBh4dleqD2ecjPparU+eQfV52N2ST+\ndKIyzPFU2W2rhk+W6jogvoE9PRv7kRHtzeKREhKZofb07C101wXwDsHbzPST\nPseH1Fmj/wWojoVNtQHPRVxbXqKNFvWXJ7Tr6FVHXAET2swHIr0vHo4VylY0\nstwPR2UV1oEr4HJdiRezCF7FfC2oDtGscl71sUM1+NuEUl7ejV8LzEWqi7fB\nKAPlLn1noI666kx5pewMQ8LUeYYOkfE8NFrPZSxecxJbxt/l9QPJu9OLvqQC\nkWzQ\r\n=WofP\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC279f66KlbRY3DnvebG2j4fWG9sHdFEyXoiLh6jITXyQIhANTKArbcGOmuoiIm7e9llOGrYrnfIXCIZoFq2IlbZCSg"}]}, "engines": {"node": ">= 10.0"}}, "1.3.1": {"name": "@ericblade/quagga2", "version": "1.3.1", "dependencies": {"@babel/polyfill": "^7.12.1", "get-pixels": "^3.3.2", "gl-mat2": "^1.0.1", "gl-vec2": "^1.3.0", "gl-vec3": "^1.1.3", "lodash": "^4.17.21", "ndarray": "^1.0.19", "ndarray-linear-interpolate": "^1.0.0", "snyk": "^1.449.0"}, "devDependencies": {"@babel/core": "^7.12.13", "@babel/plugin-proposal-class-properties": "^7.12.13", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.13", "@babel/plugin-proposal-object-rest-spread": "^7.12.13", "@babel/plugin-proposal-optional-chaining": "^7.12.13", "@babel/plugin-transform-runtime": "^7.12.15", "@babel/preset-env": "^7.12.13", "@babel/preset-typescript": "^7.12.13", "@babel/runtime": "^7.12.13", "@cypress/code-coverage": "^3.9.2", "@cypress/webpack-preprocessor": "5.5.0", "@types/chai": "^4.2.14", "@types/gl-vec2": "^1.3.0", "@types/lodash": "^4.14.168", "@types/mocha": "^5.2.7", "@types/react": "^17.0.1", "@types/sinon": "^9.0.10", "@types/sinon-chai": "^3.2.5", "@typescript-eslint/eslint-plugin": "^4.15.0", "@typescript-eslint/parser": "^4.15.0", "babel-loader": "^8.2.2", "babel-plugin-add-module-exports": "^1.0.4", "babel-plugin-istanbul": "^6.0.0", "chai": "^4.3.0", "core-js": "^3.8.3", "cross-env": "^7.0.3", "cypress": "^4.12.1", "eslint": "^7.19.0", "eslint-config-airbnb-typescript": "^12.3.1", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jsx-a11y": "^6.4.1", "eslint-plugin-react": "^7.22.0", "eslint-plugin-react-hooks": "^4.2.0", "esm": "^3.2.25", "mocha": "^5.2.0", "nyc": "^15.1.0", "sinon": "^9.2.4", "sinon-chai": "^3.5.0", "source-map-loader": "^1.1.1", "ts-mocha": "^8.0.0", "ts-node": "^9.1.1", "typescript": "^4.1.4", "webpack": "^4.44.2", "webpack-cli": "^3.3.12"}, "directories": {"doc": "doc"}, "dist": {"integrity": "sha512-u7a8iXO/pycEAdHRFOoZQt1G9wtVSAnZXqrR0TAbXlJC8lF3CvdYV1ikOUOgFosN5/uxRMdhVhHnxhboGTs5/Q==", "shasum": "f93f3615e6ceb984d13b82c6ad827726f7075e33", "tarball": "https://registry.npmjs.org/@ericblade/quagga2/-/quagga2-1.3.1.tgz", "fileCount": 9, "unpackedSize": 3250211, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgQKRcCRA9TVsSAnZWagAAzx4P/jDblVJ918zkOF0og+nC\nL34pR3rxjScWs3tqeTpgj9uo3Q3q19cGLUc+6NNnPT3YMYEAQFDEFMu4Cfbz\nzaV1YIPVYPXoSAC5g489GjzuHN0eXPihghuS9OWIxdwC9ZBomkXCDmvK0iQe\n2FvTbDOfO7Dx7Akn6tnc1Qjw6QXBX/cShLDL6QR3qKeaQNHYpweyMsRR5lyd\nGv4f2BHCujwwgOrTcRs5KKglDO31e20QBTw0zXXhceO9UDcltVa/p53oJ7/o\nKxPZACJy4jOQL2xr81LesyXGYK4SuDiheFu8Zj4JVhFSWoP1aWcZKbiLVvUb\nTSTGy/kqTfxt9Cs+KKUZkM9Vqwc38iCZ69AcrjvoTpAc4nX7VLhiFEmlHjJF\njOEW4AkX1ZiTl1I2o68p93+po8pArDn4Rxdw6xi8OL5ThGaROhzp/mFmrv53\nN0Ic0UsCUKzEKXCc0STkJwqBoC97injtoeWw7J4hQrrJjBWVHQEpoRQXHBod\nCU10Ye4VFyTZPUuWDIor1Lh4KBS4/IQ+logJbftVZ3t9MG6TotrkACc6BtKA\n8BHb2Swigm0sPWurXO1EO9/BWz5n6kgtgsdW4VFlwwjR6tFsQGbySlX0Fads\n9+rneOd5Ezwklm12bpESrA02FziYlt+ZxK9/hEMg3n4afacoGQDlc5PBeJrl\nG8Gx\r\n=4i/j\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDaXigkVFf0xFANWIKrFlzHfoS3ZcyERZnPXf0ICoPb3AIgMlA9CGciHb4VCVyjR2J4K8dpxuAd2Q722EyWmlD4oEs="}]}, "engines": {"node": ">= 10.0"}}, "1.4.0": {"name": "@ericblade/quagga2", "version": "1.4.0", "dependencies": {"@babel/polyfill": "^7.12.1", "get-pixels": "^3.3.2", "gl-mat2": "^1.0.1", "gl-vec2": "^1.3.0", "gl-vec3": "^1.1.3", "lodash": "^4.17.21", "ndarray": "^1.0.19", "ndarray-linear-interpolate": "^1.0.0", "snyk": "^1.518.0"}, "devDependencies": {"@babel/core": "^7.12.13", "@babel/plugin-proposal-class-properties": "^7.12.13", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.13", "@babel/plugin-proposal-object-rest-spread": "^7.12.13", "@babel/plugin-proposal-optional-chaining": "^7.12.13", "@babel/plugin-transform-runtime": "^7.12.15", "@babel/preset-env": "^7.12.13", "@babel/preset-typescript": "^7.12.13", "@babel/runtime": "^7.12.13", "@cypress/code-coverage": "^3.9.2", "@cypress/webpack-preprocessor": "5.5.0", "@types/chai": "^4.2.14", "@types/gl-vec2": "^1.3.0", "@types/lodash": "^4.14.168", "@types/mocha": "^5.2.7", "@types/react": "^17.0.1", "@types/sinon": "^9.0.10", "@types/sinon-chai": "^3.2.5", "@typescript-eslint/eslint-plugin": "^4.15.0", "@typescript-eslint/parser": "^4.15.0", "babel-loader": "^8.2.2", "babel-plugin-add-module-exports": "^1.0.4", "babel-plugin-istanbul": "^6.0.0", "chai": "^4.3.0", "core-js": "^3.8.3", "cross-env": "^7.0.3", "cypress": "^4.12.1", "eslint": "^7.19.0", "eslint-config-airbnb-typescript": "^12.3.1", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jsx-a11y": "^6.4.1", "eslint-plugin-react": "^7.22.0", "eslint-plugin-react-hooks": "^4.2.0", "esm": "^3.2.25", "mocha": "^5.2.0", "nyc": "^15.1.0", "sinon": "^9.2.4", "sinon-chai": "^3.5.0", "source-map-loader": "^1.1.1", "ts-mocha": "^8.0.0", "ts-node": "^9.1.1", "typescript": "^4.1.4", "webpack": "^4.44.2", "webpack-cli": "^3.3.12"}, "directories": {"doc": "doc"}, "dist": {"integrity": "sha512-xyva/LRbpu59GtrIsIubzSmba1+YycZPIbciI82g8Mi6PJXw9q8T16yFWete+4+3m4RhlO7QBWI4xKNeYz54Nw==", "shasum": "f31719bc5138d32dad8505a16dfb3450998bedd4", "tarball": "https://registry.npmjs.org/@ericblade/quagga2/-/quagga2-1.4.0.tgz", "fileCount": 9, "unpackedSize": 3276120, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgtUkWCRA9TVsSAnZWagAAyisQAIY3/rc3NRgtm/Gv3lCA\nk+Iy1zFb7tjskMr+ViNRrmYHfwLxZBhBNoZKwC+o+yRnMyUebw8KcJan1dHZ\nZWnMjVUbiUZWmiFHwBTXignQGQ7jVUrSkLr8j0Y3pW+nn5iNbaLZDkiLjyrO\nvl96eRvwHp2ixjpH3xLL1qQrGYUmRkmtM/Ao17A1QRzBFtzVDeoQ4GSLPqvZ\nnHWR4GzIWpD6VHuk0UD+opi0Q78bGuWT1U5IbH7YlcbY/pfp3puaGUHvY3au\ndVBB7/uzXRQtLtTcooHiCpXylPfMYEi4wlZpj3MvWq6CMspoGU+ETORDvlbI\nzJE4RElBJSDXwawP4uakSDmSTzWtl3iIX7W9aHvVU2PlQwhjSid1StgcUCAK\nDkOd4NdpDd0LkKB4stIL4YFWPC4T4HZdgUfCu1wPZQotkWSVpadEaMR9absm\nQI+/3biXmvHf9xSzHjASRyI/7hlvGsH16GWl3RnJBPp3XieyBSIeNzb0gp3E\n14sP/DtDDG1c7BJrs3ehxgIPkf1bXNbLYkOSxr23oX5XIm0DmbdRpizkrEW6\neKcmURR9/oZ1C63YQgyjMB1Du5xZ4aUs/p4cim113h6RXygDhZKRgvCBmQjN\n2/WifLft+eg2tjmWq8fltIQeB01DveDWsJpM4fZmQTs5eWixYkbwntkyyOJC\nMHCV\r\n=hWuC\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDAYsiXwrceRSAl4UKI/Hu1VXuNN/QnzQPeMiWhcpU61gIgI7UegJzu5JLN8aE39eptUSW8ZwLcRvVa8b7co15ViVc="}]}, "engines": {"node": ">= 10.0"}}, "1.4.1": {"name": "@ericblade/quagga2", "version": "1.4.1", "dependencies": {"@babel/polyfill": "^7.12.1", "get-pixels": "^3.3.2", "gl-mat2": "^1.0.1", "gl-vec2": "^1.3.0", "gl-vec3": "^1.1.3", "lodash": "^4.17.21", "ndarray": "^1.0.19", "ndarray-linear-interpolate": "^1.0.0"}, "devDependencies": {"@babel/core": "^7.14.6", "@babel/plugin-proposal-class-properties": "^7.14.5", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.14.5", "@babel/plugin-proposal-object-rest-spread": "^7.14.7", "@babel/plugin-proposal-optional-chaining": "^7.14.5", "@babel/plugin-transform-runtime": "^7.14.5", "@babel/preset-env": "^7.14.7", "@babel/preset-typescript": "^7.14.5", "@babel/runtime": "^7.14.6", "@cypress/code-coverage": "^3.9.9", "@cypress/webpack-preprocessor": "5.9.1", "@types/chai": "^4.2.21", "@types/gl-vec2": "^1.3.1", "@types/lodash": "^4.14.171", "@types/mocha": "^5.2.7", "@types/react": "^17.0.14", "@types/sinon": "^10.0.2", "@types/sinon-chai": "^3.2.5", "@typescript-eslint/eslint-plugin": "^4.28.2", "@typescript-eslint/parser": "^4.28.2", "babel-loader": "^8.2.2", "babel-plugin-add-module-exports": "^1.0.4", "babel-plugin-istanbul": "^6.0.0", "chai": "^4.3.4", "core-js": "^3.15.2", "cross-env": "^7.0.3", "cypress": "^4.12.1", "eslint": "^7.30.0", "eslint-config-airbnb-typescript": "^12.3.1", "eslint-plugin-import": "^2.23.4", "eslint-plugin-jsx-a11y": "^6.4.1", "eslint-plugin-react": "^7.24.0", "eslint-plugin-react-hooks": "^4.2.0", "esm": "^3.2.25", "mocha": "^5.2.0", "nyc": "^15.1.0", "sinon": "^11.1.1", "sinon-chai": "^3.7.0", "source-map-loader": "^1.1.1", "ts-mocha": "^8.0.0", "ts-node": "^10.1.0", "typescript": "^4.3.5", "webpack": "^4.44.2", "webpack-cli": "^3.3.12"}, "directories": {"doc": "doc"}, "dist": {"integrity": "sha512-F7q898llifXw5UQ8Ws1+i7pH5TR1biE9E9wfjvzZr2SnTND/8I4OimTOZgrS1cChJAXnBrK7PTZrZWijkbpb2g==", "shasum": "60e2441fecbab34d8c9a0ced0e5f2af6a17afe4f", "tarball": "https://registry.npmjs.org/@ericblade/quagga2/-/quagga2-1.4.1.tgz", "fileCount": 9, "unpackedSize": 3275989, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg6yC3CRA9TVsSAnZWagAAESgQAI2ozPTo+nq9/83jN6QH\nM/I4K8YcLDXAe2Aqe1LWRGos7rr6bJJIPzg1S5pJL9S/PyttMORaZLJ18nQW\nR38ly5AdW61UWcOVbXGPMlmHK3xZn/uQ9vJ87N/FGT0dfe0oz6JKVDWl+zpG\nBdCXMMx0NOycxdbZUYzCN5ad5eM1lDWBZ0y73cILfql3ZVBb6dkXPPg0c5a4\n27KLo99XlfSrJ2bS3Li7dKl+EtJTQxzewYZrrkr7BkfHrP+P0AoXh5sBKJ3P\n3sTcMlm24XKRcLwhMoZ752u0DayPzkUg5LRDrNeeCQUFvGqnsZbbc64TzXYA\njEWDnfsH2E6ZbFlAh1DsoGQsP1XZJ0D2PKfX2RcX6nt80UCE6tbrQ5pd8pQd\nJx7ucED4I90cMPqlOeJpMc8oL15gp8vEX3Eg1WL4fkVclOr1qq2vi/xpMeQc\nDtZjp7mgEyVBdb1odnGuPBOeGi8H7vpjJEgWKF2wr2VmD6u239kMW3urOvj6\nY0lMLwgnHX9BZgqiyCKLwZyKbtw7s/XFWshjdfuRMvIL2drSwEJLoE2MFbbR\nvsH2MBhTsYGD2Y63XEVI6adpwEBfxe3PAfCcUrHbDHnai7yU+kAjPYll8CJt\nw2EvSLd3fkKCe6op7E6TeU1kD3JvQmNcoUhWDgXs2/PG3IjGDqULxyqOHSFX\nwSJ9\r\n=NY6A\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDF/cESmxQb9mt0A+xfNs+AhDrbNSn0N7eucuvswjbf7AIgXNrJuYzxXlioDKWlapAOeYU4J2vV8KyeTHLwlU7vxjE="}]}, "engines": {"node": ">= 10.0"}}, "1.4.2": {"name": "@ericblade/quagga2", "version": "1.4.2", "dependencies": {"@babel/polyfill": "^7.12.1", "get-pixels": "^3.3.3", "gl-mat2": "^1.0.1", "gl-vec2": "^1.3.0", "gl-vec3": "^1.1.3", "lodash": "^4.17.21", "ndarray": "^1.0.19", "ndarray-linear-interpolate": "^1.0.0"}, "devDependencies": {"@babel/core": "^7.15.0", "@babel/plugin-proposal-class-properties": "^7.14.5", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.14.5", "@babel/plugin-proposal-object-rest-spread": "^7.14.7", "@babel/plugin-proposal-optional-chaining": "^7.14.5", "@babel/plugin-transform-runtime": "^7.15.0", "@babel/preset-env": "^7.15.0", "@babel/preset-typescript": "^7.15.0", "@babel/runtime": "^7.15.3", "@cypress/code-coverage": "^3.9.10", "@cypress/webpack-preprocessor": "5.9.1", "@types/chai": "^4.2.21", "@types/gl-vec2": "^1.3.1", "@types/lodash": "^4.14.172", "@types/mocha": "^5.2.7", "@types/react": "^17.0.19", "@types/sinon": "^10.0.2", "@types/sinon-chai": "^3.2.5", "@typescript-eslint/eslint-plugin": "^4.29.3", "@typescript-eslint/parser": "^4.29.3", "babel-loader": "^8.2.2", "babel-plugin-add-module-exports": "^1.0.4", "babel-plugin-istanbul": "^6.0.0", "chai": "^4.3.4", "core-js": "^3.16.3", "cross-env": "^7.0.3", "cypress": "^4.12.1", "eslint": "^7.32.0", "eslint-config-airbnb-typescript": "^14.0.0", "eslint-plugin-import": "^2.24.2", "eslint-plugin-jsx-a11y": "^6.4.1", "eslint-plugin-react": "^7.24.0", "eslint-plugin-react-hooks": "^4.2.0", "esm": "^3.2.25", "mocha": "^5.2.0", "nyc": "^15.1.0", "sinon": "^11.1.2", "sinon-chai": "^3.7.0", "source-map-loader": "^1.1.1", "ts-mocha": "^8.0.0", "ts-node": "^10.2.1", "typescript": "^4.4.2", "webpack": "^4.44.2", "webpack-cli": "^3.3.12"}, "directories": {"doc": "doc"}, "dist": {"integrity": "sha512-MZXX8Sh5wHNLrB+1qfMi0c8tz+LJibOJv/c6WoSc5SbGdqWBMAMZv6p1N8Wu/JtFlhlXtw7RzvIz3I3ZMuaJjw==", "shasum": "caaa8793f52ef4a99ea703de95c15731aa41e60e", "tarball": "https://registry.npmjs.org/@ericblade/quagga2/-/quagga2-1.4.2.tgz", "fileCount": 9, "unpackedSize": 3328908, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhKRHUCRA9TVsSAnZWagAA6gMP/imxo8mX50feQ72JAvzX\nduAkCP7hoFI51O1Vuf5ac1B/yv9CI0c5DIurbdi9lVP9n7dGa/E7JXWygqj2\nHmmdW0XhxvO/JVY5/C+/3XwGhOHxanOG0HT2a77FoQPI0yfN+aTaXyiwKSk3\n7uYz4CMIgdVZTI9wIK/JdJMUOuAaIDmCAnWcwCjx8r74MSulBN1Q7mUn+fLO\nXfqjIds8TxWjy6CaczHsNQAsXZZvqJMUqV/iRoVbNEA1YVKCijDK9yXE6pMy\nnjpfmsPjfbbh/vzhzlY5mMVeSOM5r7TBn/erWmRxpv5lG9XIqomjjSUgapWZ\nph7qcqzvbEzSQQLkT59nrOSj92h/qHjcY/kreQBzAjikDcYb+OWya8zHgOCu\nI7SgsE9MLktJEOAqK3oz3f/F+sEPsYW5gtCrI/VvoJxNVqOWpCsNvaxqjnyl\nJZqSKDZyFyHhaldHtgU2lpZOJ+4fj4egvVKPApGxdzlz6jcDGiOSiR5w322+\nMOAMo5NRMwByEE2TBaFjMkiSgVqmqnxJCf3STHvCMPi/sse36qtImNh12mXQ\nd5+BZA1C30ze2sa2UKYDSlGKbXYCtRmAIhS8gIwO5QvGTnh89qYd0ugwVb4d\nPr1Ic3xL1i4e8LsxaWyC/E/dIG9z0kos1S6GPEF2Z+mAuwY1uv6DH/DiZf5H\nHd7a\r\n=KNl4\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDrT/mkj4U7D9peD/ohsvXzDtaXS5+erRMrPO7+fW5aHAiEA5DQ81pqYTZpswdbRrYiaw3yT5eBML4iczMkoCCr5hfM="}]}, "engines": {"node": ">= 10.0"}}, "1.5.0": {"name": "@ericblade/quagga2", "version": "1.5.0", "dependencies": {"@babel/polyfill": "^7.12.1", "get-pixels": "^3.3.3", "gl-mat2": "^1.0.1", "gl-vec2": "^1.3.0", "gl-vec3": "^1.1.3", "lodash": "^4.17.21", "ndarray": "^1.0.19", "ndarray-linear-interpolate": "^1.0.0"}, "devDependencies": {"@babel/core": "^7.17.5", "@babel/plugin-proposal-class-properties": "^7.16.7", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.16.7", "@babel/plugin-proposal-object-rest-spread": "^7.17.3", "@babel/plugin-proposal-optional-chaining": "^7.16.7", "@babel/plugin-transform-runtime": "^7.17.0", "@babel/preset-env": "^7.16.11", "@babel/preset-typescript": "^7.16.7", "@babel/runtime": "^7.17.2", "@cypress/code-coverage": "^3.9.12", "@cypress/webpack-preprocessor": "5.11.1", "@types/chai": "^4.3.0", "@types/gl-vec2": "^1.3.2", "@types/lodash": "^4.14.179", "@types/mocha": "^5.2.7", "@types/sinon": "^10.0.11", "@types/sinon-chai": "^3.2.8", "@typescript-eslint/eslint-plugin": "^5.13.0", "@typescript-eslint/parser": "^5.13.0", "babel-loader": "^8.2.3", "babel-plugin-add-module-exports": "^1.0.4", "babel-plugin-istanbul": "^6.1.1", "chai": "^4.3.6", "core-js": "^3.21.1", "cross-env": "^7.0.3", "cypress": "^4.12.1", "eslint": "^8.10.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^16.1.0", "eslint-config-airbnb-typescript-base": "^3.0.1", "eslint-plugin-import": "^2.25.4", "eslint-plugin-jsx-a11y": "^6.5.1", "eslint-plugin-typescript-sort-keys": "^2.1.0", "esm": "^3.2.25", "mocha": "^5.2.0", "nyc": "^15.1.0", "sinon": "^13.0.1", "sinon-chai": "^3.7.0", "source-map-loader": "^1.1.1", "ts-mocha": "^9.0.2", "ts-node": "^10.6.0", "typescript": "^4.6.2", "webpack": "^4.44.2", "webpack-cli": "^3.3.12"}, "directories": {"doc": "doc"}, "dist": {"integrity": "sha512-sDJXK/lUisubXMTa/pSnRsqIoJt8NyzZGsTHgsv5lJ74KHsB5qulKVqTzrrvr0BIWbfnUAOe02SOoWUbi03GyA==", "shasum": "f641ff358bb84e60738206901feddc793c056595", "tarball": "https://registry.npmjs.org/@ericblade/quagga2/-/quagga2-1.5.0.tgz", "fileCount": 8, "unpackedSize": 3340395, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiIxuZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmowqQ/+LXcswK7y20Y17SADPkeim4H4VcAj5WMUZngahd0PjXvoEcHC\r\nIIGIVQcHtJZTu9muJ5WIQr01fAP3RvGMIoXtYV1UAPQFTux30Yx9hTVqKIeZ\r\nMHR9o0VMmBqGBNfohLVHkpm1e/znQGNGuGLG/x6di7SM82PJdS71Sr297wUI\r\nIwPAAy1Aai29dNOpBcGSOEKqoU1EW40r/U+De8tINQtxnP6G6lysuTV1NW/W\r\nRnZ2C7a6eiUrF44MjgWBabJR+UK5/VWyKn7O48QW/SMmDXpviSBCPQp2gDVK\r\nrH+yh8Q+dr0tYp9HOLtaa5XygC7VA5QeGY+ip3myur5vBsRs3kfg1BdwVq3B\r\ndtawEio/7wO/hRSCP5O4V83yIAAI8s/jlAlAb0aUlS1wucFSqkEjvBO+Q4OA\r\n4RWTlO8R/Vy33D80tpBzkFwC8ndBwIY9pP+Uw754e2x4T3M+73dZB4oT+iID\r\nOUXXMAu9Ss5EjbPHmk4ygrC8fykSzdfm8NkF/BHCqU9SoYkqAO/5YECJfrlS\r\n5khI0Jdc26Rl5MxOQS3+9KGsWVCjqx54mqLGK3r+CStcg36uc1AJg5CkSSUb\r\nuq4KKsPASO+IYqA3+JuOAwRN5nLGFeQSCy6JNWf0I+nU6N9UeEANOrQYkQbP\r\nz5dILawk/M6Xony17MVMsPTgh0tomJKhRgQ=\r\n=qzwa\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAirrHAjDTPHcBJ/xQUqStE0wkQLXxCtQ4qgP+KbttVUAiA+Hw2aepy0gRRvb7yGAQZUcBqWTPCUPLd06SQOrsNBgw=="}]}, "engines": {"node": ">= 10.0"}}, "1.6.0": {"name": "@ericblade/quagga2", "version": "1.6.0", "dependencies": {"@babel/polyfill": "^7.12.1", "get-pixels": "^3.3.3", "gl-mat2": "^1.0.1", "gl-vec2": "^1.3.0", "gl-vec3": "^1.1.3", "lodash": "^4.17.21", "ndarray": "^1.0.19", "ndarray-linear-interpolate": "^1.0.0"}, "devDependencies": {"@babel/core": "^7.17.5", "@babel/plugin-proposal-class-properties": "^7.16.7", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.16.7", "@babel/plugin-proposal-object-rest-spread": "^7.17.3", "@babel/plugin-proposal-optional-chaining": "^7.16.7", "@babel/plugin-transform-runtime": "^7.17.0", "@babel/preset-env": "^7.16.11", "@babel/preset-typescript": "^7.16.7", "@babel/runtime": "^7.17.2", "@cypress/code-coverage": "^3.9.12", "@cypress/webpack-preprocessor": "5.11.1", "@types/chai": "^4.3.0", "@types/gl-vec2": "^1.3.2", "@types/lodash": "^4.14.179", "@types/mocha": "^5.2.7", "@types/sinon": "^10.0.11", "@types/sinon-chai": "^3.2.8", "@typescript-eslint/eslint-plugin": "^5.13.0", "@typescript-eslint/parser": "^5.13.0", "babel-loader": "^8.2.3", "babel-plugin-add-module-exports": "^1.0.4", "babel-plugin-istanbul": "^6.1.1", "chai": "^4.3.6", "core-js": "^3.21.1", "cross-env": "^7.0.3", "cypress": "^4.12.1", "eslint": "^8.10.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^16.1.0", "eslint-config-airbnb-typescript-base": "^3.0.1", "eslint-plugin-import": "^2.25.4", "eslint-plugin-jsx-a11y": "^6.5.1", "eslint-plugin-typescript-sort-keys": "^2.1.0", "esm": "^3.2.25", "mocha": "^5.2.0", "nyc": "^15.1.0", "sinon": "^13.0.1", "sinon-chai": "^3.7.0", "source-map-loader": "^1.1.1", "ts-mocha": "^9.0.2", "ts-node": "^10.6.0", "typescript": "^4.6.2", "webpack": "^4.44.2", "webpack-cli": "^3.3.12"}, "directories": {"doc": "doc"}, "dist": {"integrity": "sha512-iOhmb+ULT074vbndJZHUBCSj1gFZum1yunMg/M3A0DOhSQPFdOhq6i/a0MrKOi47i+UvwouBmJySwhIDlSJF9g==", "shasum": "3c8c535476212281a3860d84caa72ac40318b72b", "tarball": "https://registry.npmjs.org/@ericblade/quagga2/-/quagga2-1.6.0.tgz", "fileCount": 9, "unpackedSize": 3305082, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiKNx2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmowLBAAjJxJrM4rXlryzL43FuHSt6AxVKab+u6skrQZ8V+l4xDa8pS3\r\nba+wmUt0DwTll6JBDJedFpeFlTU8OppMnYqisn0eZxNeB7G2t071HpHOzgQ4\r\nyBTvaIor6ovxY/CKzh6pCUGu1JQsuN0fryNilz4YLsTF+zyRvGSwqRSjK4/a\r\n+wJfHaAVWlwo98vRwrtN+G+hYH14zBmDwZLwfVCn7fH7rwNPdzCYMuRmLGHD\r\nK/EKu+irSFwXBl6dxxn3YwuSuNP89WorSRPJlOcviuUPrlEMn+/TJWgjCAQ5\r\nx28XBmni8nLY/1QJ+u5qHP3zKxqcvEYIyi5oncuJcHR9rHlF8jH/j0+jBg4Y\r\nyq1m5gkRQkprG8K1j60XfD0H7IjZ2ByTFFiwxX0Aza/sTq3QqzO1Ycf/ccxw\r\nDvqNcOjroWwfetpCPiz7FIodb1yeck1x8P0nLSgj4ZkkDJ2ds/1Ds39voiew\r\n/88KlnQNdDxcy/cq08h9Mveb+IUDaX6o7nZL7eJ4vJShgWxACLmkGvGTOUAT\r\nq5eOr9enf/PNFFU+kYSq81WKEbO2a+R7tkcOlN2Ak1bMTBN3OS6su0T9S9Pw\r\nB1jO0pEUq1gUpmYbi7UoYj4QJC3+hIQdCgLEDKNRYarSuOFfxHol8AIpU1m6\r\nAcgyUm3FFL9lwwXyiwu6zgBEQBcfMxJyGK8=\r\n=m3zD\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBmN5CMFGkRzkDm0WICj7Tvjr2V43cfjpWRULumROHwFAiEAtgQUWR0+HCMrLCMqqDGOBT2JTWdlrifXfpDoD+E2AhE="}]}, "engines": {"node": ">= 10.0"}}, "1.7.0": {"name": "@ericblade/quagga2", "version": "1.7.0", "dependencies": {"@babel/polyfill": "^7.12.1", "get-pixels": "^3.3.3", "gl-mat2": "^1.0.1", "gl-vec2": "^1.3.0", "gl-vec3": "^1.1.3", "lodash": "^4.17.21", "ndarray": "^1.0.19", "ndarray-linear-interpolate": "^1.0.0", "fsevents": "2.1.2"}, "optionalDependencies": {"fsevents": "2.1.2"}, "devDependencies": {"@babel/core": "^7.17.9", "@babel/plugin-proposal-class-properties": "^7.16.7", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.16.7", "@babel/plugin-proposal-object-rest-spread": "^7.17.3", "@babel/plugin-proposal-optional-chaining": "^7.16.7", "@babel/plugin-transform-runtime": "^7.17.0", "@babel/preset-env": "^7.16.11", "@babel/preset-typescript": "^7.16.7", "@babel/runtime": "^7.17.9", "@cypress/code-coverage": "^3.9.12", "@cypress/webpack-preprocessor": "5.11.1", "@types/chai": "^4.3.1", "@types/gl-vec2": "^1.3.2", "@types/lodash": "^4.14.182", "@types/mocha": "^5.2.7", "@types/sinon": "^10.0.11", "@types/sinon-chai": "^3.2.8", "@typescript-eslint/eslint-plugin": "^5.21.0", "@typescript-eslint/parser": "^5.21.0", "babel-loader": "^8.2.5", "babel-plugin-add-module-exports": "^1.0.4", "babel-plugin-istanbul": "^6.1.1", "chai": "^4.3.6", "core-js": "^3.22.2", "cross-env": "^7.0.3", "cypress": "^4.12.1", "eslint": "^8.14.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^17.0.0", "eslint-config-airbnb-typescript-base": "^3.1.2", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsx-a11y": "^6.5.1", "eslint-plugin-typescript-sort-keys": "^2.1.0", "esm": "^3.2.25", "mocha": "^5.2.0", "nyc": "^15.1.0", "sinon": "^13.0.2", "sinon-chai": "^3.7.0", "source-map-loader": "^1.1.1", "ts-mocha": "^9.0.2", "ts-node": "^10.7.0", "typescript": "^4.6.3", "webpack": "^4.44.2", "webpack-cli": "^3.3.12"}, "directories": {"doc": "doc"}, "dist": {"integrity": "sha512-4ZZtqAHaX63i5/YLf0doZ/upiHAbUtaYx4k5V2lLLbC3fO+Wdneh8cQbKBG0oztALXwad5LDiOY/6/59wCn9jw==", "shasum": "64551ad1aaef69214a40b090097beb0a4f66fd67", "tarball": "https://registry.npmjs.org/@ericblade/quagga2/-/quagga2-1.7.0.tgz", "fileCount": 8, "unpackedSize": 3310704, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGgI82aa3iLgDmj89Q1yjkxDkUNrez4XszUvN3qZtxWMAiBdvTGv/01tXY/xbROAKbzj3SmElcv5xybUWpJub0SlyQ=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaOlmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr7cg//YnRReJwj2uFz1+HK1QWoR2bkJF+zKqRgRz02BtFmfDf0U9/L\r\n7tvGt9t5dFi2uzI+2L+NVpqqKj06wQm5ouox1DK3Tv1NhpDBI5iX6MK2dN0p\r\nDQMSz1OnRENpPtSeDL7ZyBcfhTiQoXMPBLTDLWmj7lGp9vNQsGThzTBG+YDA\r\nPfdSaIcCXJR2yQTKYVDgZExeR+7ZxjPVqTBKecYJv9TryxXY44aBAVjwrZcc\r\n5qj8o65IRarG8071tIfdr5XFFAU631sbgHXYUOwQ86oVm4iuikZgeahBDVGI\r\nGGuiJfVYS3VK7zfcigjN/E4BudIJZX66wQAWNJ7FZyR1LzfkwQOpWsNG2t2m\r\nxXpgJ7diQL8RO808zKK9yZrkYmQnhUCAktsTQ+V7H4v5FZfKXVk5F7O5xlD/\r\n5XHVmDcAUdHZmLzdE4UIZAO2KjaaCGUGtZQ9EwZCBKFEv5IxUGHDQPbqZDK4\r\n0L8cP5CMZD/nxVjxU+PQcl970tLvPvlPrADbQ2/lTbHdaF0AM5SGD68sA0tK\r\nOIgz+HOINe7ZQ6tlQaicrraIc1zOYRSNfU1HS38KClzVWBVRHSkZhF2Vgi9T\r\nUZI0pPzqel05p+9Jhb5fnNVe8cH8/18z9XwfpYe1jAWW7ee213sz/wZq8PLp\r\nV3SvqU4EiWuhZoTRwPCRZyTXmofwEwUVEEw=\r\n=3/dB\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10.0"}}, "1.7.1": {"name": "@ericblade/quagga2", "version": "1.7.1", "dependencies": {"@babel/polyfill": "^7.12.1", "get-pixels": "^3.3.3", "gl-mat2": "^1.0.1", "gl-vec2": "^1.3.0", "gl-vec3": "^1.1.3", "lodash": "^4.17.21", "ndarray": "^1.0.19", "ndarray-linear-interpolate": "^1.0.0", "fsevents": "2.1.2"}, "optionalDependencies": {"fsevents": "2.1.2"}, "devDependencies": {"@babel/core": "^7.17.9", "@babel/plugin-proposal-class-properties": "^7.16.7", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.16.7", "@babel/plugin-proposal-object-rest-spread": "^7.17.3", "@babel/plugin-proposal-optional-chaining": "^7.16.7", "@babel/plugin-transform-runtime": "^7.17.0", "@babel/preset-env": "^7.16.11", "@babel/preset-typescript": "^7.16.7", "@babel/runtime": "^7.17.9", "@cypress/code-coverage": "^3.9.12", "@cypress/webpack-preprocessor": "5.11.1", "@types/chai": "^4.3.1", "@types/gl-vec2": "^1.3.2", "@types/lodash": "^4.14.182", "@types/mocha": "^5.2.7", "@types/sinon": "^10.0.11", "@types/sinon-chai": "^3.2.8", "@typescript-eslint/eslint-plugin": "^5.21.0", "@typescript-eslint/parser": "^5.21.0", "babel-loader": "^8.2.5", "babel-plugin-add-module-exports": "^1.0.4", "babel-plugin-istanbul": "^6.1.1", "chai": "^4.3.6", "core-js": "^3.22.2", "cross-env": "^7.0.3", "cypress": "^4.12.1", "eslint": "^8.14.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^17.0.0", "eslint-config-airbnb-typescript-base": "^3.1.2", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsx-a11y": "^6.5.1", "eslint-plugin-typescript-sort-keys": "^2.1.0", "esm": "^3.2.25", "mocha": "^5.2.0", "nyc": "^15.1.0", "sinon": "^13.0.2", "sinon-chai": "^3.7.0", "source-map-loader": "^1.1.1", "ts-mocha": "^9.0.2", "ts-node": "^10.7.0", "typescript": "^4.6.3", "webpack": "^4.44.2", "webpack-cli": "^3.3.12"}, "directories": {"doc": "doc"}, "dist": {"integrity": "sha512-brx0N6MHTCgsFE/b5EiMVKguUpBhddmKwpgxXDUpVEthw4n6v3OWE+EkI1SgYYUJn/MPqy6B/QwTARcymm9O6w==", "shasum": "5c1fd932ad999c2be31f6fde8c1c304f65024f2f", "tarball": "https://registry.npmjs.org/@ericblade/quagga2/-/quagga2-1.7.1.tgz", "fileCount": 8, "unpackedSize": 3311074, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCPd2n7fFsqZs4fQKdaAEgEXiLWbK3uWLAboUMBy4fXSAIhAI/i4PVYhdorNSHgKkBSmNA7MVWdV2wBXatGq0JJH6FF"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi/+IgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr9CRAAg1iRGXhJr7mY6Pxe8q41wDjTgVcZpQblKYxn94HKTHHoS8WB\r\nMAr0bSYPvc0XIyci0XOUq50VC7X4dFQSr5rFytIbxamKZWH9NVaFnV6Thuog\r\nNw9A1dEAJcrNC9VVBYjfvHW1rnAlaUpJctk2wx6/JfuOIXJMS2OnXQRURwpI\r\ncq+xOV//CFqGIr7yCr4bculRm5wP6G4g86ma3rDmYblbnvVNiEQPVKz4x0SM\r\ng8zbtmW3stJ3vkZ/NglZcCO1MzQxL2HaK+JLWZSDqW84Ld/jkS0yVMeDQzN1\r\nH8HnPCf10kQO1CQQKgh7rPHGtOjZpq66qQTw56el7hwmUO97wF91rbiBD6XC\r\nkQ0QOIwU99bvlp00aemB4BqIDvR+XyTkZrkdXDbvbxxi5TX8KiCKdoc0NMUO\r\nyA8SH9Ex7Ro+aHqhySQMpKqn0Q8qONbkV9F58fvalMV1UsnGgDxUQ1iRJQcH\r\nqd5/nbLRRHaZJerMv5ufDKbDrDBpiQkHx3ANCTqZNWvIKNEVyXMYifUOMzgs\r\nL2pt5upPjkZ7Urj1v8IO2vdUGo2NbP0VDjpY5X2HGOGi6t+pPzFarviuLpwi\r\nmsTiMIFcsLHC1AYh0l5aVsUpTUAjp/HHE2RdLBoTtGcOGp8p5AxQml2n0Wu7\r\n05k1SOAXq5nGjxEtOXr7j0BWXcWRmFueQQg=\r\n=0jQ7\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10.0"}}, "1.7.2": {"name": "@ericblade/quagga2", "version": "1.7.2", "dependencies": {"@babel/polyfill": "^7.12.1", "get-pixels": "^3.3.3", "gl-mat2": "^1.0.1", "gl-vec2": "^1.3.0", "gl-vec3": "^1.1.3", "lodash": "^4.17.21", "ndarray": "^1.0.19", "ndarray-linear-interpolate": "^1.0.0", "fsevents": "2.3.2"}, "optionalDependencies": {"fsevents": "2.3.2"}, "devDependencies": {"@babel/core": "^7.18.13", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-object-rest-spread": "^7.18.9", "@babel/plugin-proposal-optional-chaining": "^7.18.9", "@babel/plugin-transform-runtime": "^7.18.10", "@babel/preset-env": "^7.18.10", "@babel/preset-typescript": "^7.18.6", "@babel/runtime": "^7.18.9", "@cypress/code-coverage": "^3.10.0", "@cypress/webpack-preprocessor": "5.12.2", "@types/chai": "^4.3.3", "@types/gl-vec2": "^1.3.2", "@types/lodash": "^4.14.184", "@types/mocha": "^5.2.7", "@types/sinon": "^10.0.13", "@types/sinon-chai": "^3.2.8", "@typescript-eslint/eslint-plugin": "^5.34.0", "@typescript-eslint/parser": "^5.34.0", "babel-loader": "^8.2.5", "babel-plugin-add-module-exports": "^1.0.4", "babel-plugin-istanbul": "^6.1.1", "chai": "^4.3.6", "core-js": "^3.24.1", "cross-env": "^7.0.3", "cypress": "^10.0.0", "eslint": "^8.22.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^17.0.0", "eslint-config-airbnb-typescript-base": "^4.0.2", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsx-a11y": "^6.6.1", "eslint-plugin-typescript-sort-keys": "^2.1.0", "esm": "^3.2.25", "mocha": "^5.2.0", "nyc": "^15.1.0", "sinon": "^14.0.0", "sinon-chai": "^3.7.0", "source-map-loader": "^1.1.1", "ts-mocha": "^10.0.0", "ts-node": "^10.9.1", "typescript": "^4.7.4", "webpack": "^4.44.2", "webpack-cli": "^3.3.12"}, "directories": {"doc": "doc"}, "dist": {"integrity": "sha512-Uae90msCxpNRGlRVNQ4VpmuIUUyPntI4nlYR5qY3Gl3fFjIk+0UhK88C2icLEOk6rZ1yuJRl7iJ2RLXMXKwkuQ==", "shasum": "15a6a749f7d9b4f0cebd84f1cafbeaed350b6d8c", "tarball": "https://registry.npmjs.org/@ericblade/quagga2/-/quagga2-1.7.2.tgz", "fileCount": 9, "unpackedSize": 3262470, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCcPf8WnF0IdRTKVD4JaAr76eKLej8LZ0D2VctI6wDJlAIhAIO+zASSUaDpXoe8suCCJRVvGd+jzngponLXxIA+ix88"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjHE5gACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq96g//VhvQWLgpbm8VVGoExQzUi5m366BrD4xh/FZb6rwL8EiP8F+a\r\n8wIzy511geHlmm9prVY8ydLFJ4RtUeItSFj7xlN7a/F4FaHpCpgBL5zC7p9K\r\n5P4Jwpz4FG707v6glRBpKQ5AAzIMUjesP5bM3bvfp+Bz3DCz+N/FjWjn9KDG\r\nmq5ybf7H0B1KpHCEubr/2pvS9OGoJrRI/+/7uY4RWE77L7j5GSOtewcIDlyK\r\nS4STNW9wKJU9Z2pSs4plIwmSqDtI+EVXPC6O+RZlOf3YIeF4VNFJQkTg/z+J\r\nTnRGNJILAM1DLLYmqU83RZAMAzytUw6P9jA3vHd0OlbCVd/yBk0dQ8IFO1/h\r\nkku35pOdD0JWAkFjkY6ViQlF4YocgzU/ELpfuR4JdGmRF/I7GBIS2m0gN8zA\r\n64gLLWV2ssVFR5UJiN1GD36tplze//fF6DBBW8ciwpi+c5bicDb7BRCG7qgD\r\naR5DkyFbsoX6U/qOSQcow8ILWocV/otSH+CeahSlnFl8Flm9mUVtSIRV8CZE****************************************************************************************DMeG4gEJ8GYIan9VgJ3f4jRw+9TH0xMApuGs+JJV\r\nBvLYW6hvS7noTNs+pfzTbuRflfGN/M3VlRg=\r\n=JKem\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10.0"}}, "1.7.3": {"name": "@ericblade/quagga2", "version": "1.7.3", "dependencies": {"@babel/polyfill": "^7.12.1", "get-pixels": "^3.3.3", "gl-mat2": "^1.0.1", "gl-vec2": "^1.3.0", "gl-vec3": "^1.1.3", "lodash": "^4.17.21", "ndarray": "^1.0.19", "ndarray-linear-interpolate": "^1.0.0", "fsevents": "2.3.2"}, "optionalDependencies": {"fsevents": "2.3.2"}, "devDependencies": {"@babel/core": "^7.18.13", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-object-rest-spread": "^7.18.9", "@babel/plugin-proposal-optional-chaining": "^7.18.9", "@babel/plugin-transform-runtime": "^7.18.10", "@babel/preset-env": "^7.18.10", "@babel/preset-typescript": "^7.18.6", "@babel/runtime": "^7.18.9", "@cypress/code-coverage": "^3.10.0", "@cypress/webpack-preprocessor": "5.12.2", "@types/chai": "^4.3.3", "@types/gl-vec2": "^1.3.2", "@types/lodash": "^4.14.184", "@types/mocha": "^5.2.7", "@types/sinon": "^10.0.13", "@types/sinon-chai": "^3.2.8", "@typescript-eslint/eslint-plugin": "^5.34.0", "@typescript-eslint/parser": "^5.34.0", "babel-loader": "^8.2.5", "babel-plugin-add-module-exports": "^1.0.4", "babel-plugin-istanbul": "^6.1.1", "chai": "^4.3.6", "core-js": "^3.24.1", "cross-env": "^7.0.3", "cypress": "^10.0.0", "eslint": "^8.22.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^17.0.0", "eslint-config-airbnb-typescript-base": "^4.0.2", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsx-a11y": "^6.6.1", "eslint-plugin-typescript-sort-keys": "^2.1.0", "esm": "^3.2.25", "mocha": "^5.2.0", "nyc": "^15.1.0", "sinon": "^14.0.0", "sinon-chai": "^3.7.0", "source-map-loader": "^1.1.1", "ts-mocha": "^10.0.0", "ts-node": "^10.9.1", "typescript": "^4.7.4", "webpack": "^4.44.2", "webpack-cli": "^3.3.12"}, "directories": {"doc": "doc"}, "dist": {"integrity": "sha512-c6OEkGXj903K72SKbUviQuvC/i4IHo6TySXVq0w8ywDARrp+K+uaTgOv2QXTgnLS5lTvmpFCCiKTvEF9sBvhKA==", "shasum": "b9cdbf57749f840d42e8fadd6e2cab53f2657f48", "tarball": "https://registry.npmjs.org/@ericblade/quagga2/-/quagga2-1.7.3.tgz", "fileCount": 9, "unpackedSize": 3262296, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD8QhzdLiaH+Mjxt+ZXhrWySgp3Xrmz2y1ZWc1wwNoUlgIhAKvXaH4vMU2+G+hOBaQOqA2ljBpAN94uA/47o+IJbl2u"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjJ6DuACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpCnQ//aXHzuW7aMyJt4QI+mFuC7IJBxDwDtKPp0dJeQcjEAG5TpsEO\r\neXz7Of9cF0ZBbSPoXON2gkSUk988duUN81fOqDfAkP8+Nj+ns8Z2Qq6J767A\r\nqYCYFpBtqrsJEK3w8LdcM+LlwL0nVS0XnQWpnHZTZr4PxTqOw79JzLKPWXu3\r\n9nP0HWw1CK30I24KJfYYXdEd9prVU0TfDa7rEzH3aUEoTwW0ATWd2k0vbQ8E\r\nKD4G69ELQlgsFOGVu81QPFSyRC82MXeFk25bWyCC+b5WLvGrAGW+mM4oaXnE\r\nsyaSikTkNLZC3KSfCDhK8QVUwjG0R1mgfwY0pdPZDFqbqHbIk01mrbI5RBvr\r\nj7YorZRI/C8GY6dUTzEO8W/7izrAPK1Y32b1749zL/s6La/zwg+j6q9JwE8x\r\nqyB3o9B62i7NjpVMZP/vRgLMK6KwRThitBJz/RhK6FLzEKKc6jFAd0y3rWqF\r\nV4x5NJb0EY3cHTOQ7pkIovo+n5OkS7T9NER440aZPn88GJmTyeGTVu93eLrt\r\npb7UpqlCGXHKeIfAz9EhW4N5fORDeU0wvs7pFyahXlefgkOaMhRYDLnb7g/l\r\nCC1un+cjCymrlJaAkUxmbmOqw3LK+VCjGU1ykPqHanAbC/VcxZT80/+gB2nA\r\nnMkcgIL+7/1PAuWSOllvQMS1cLjVV+ovsxM=\r\n=uKUI\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10.0"}}, "1.7.4": {"name": "@ericblade/quagga2", "version": "1.7.4", "dependencies": {"@babel/polyfill": "^7.12.1", "get-pixels": "^3.3.3", "gl-mat2": "^1.0.1", "gl-vec2": "^1.3.0", "gl-vec3": "^1.1.3", "lodash": "^4.17.21", "ndarray": "^1.0.19", "ndarray-linear-interpolate": "^1.0.0", "fsevents": "2.3.2"}, "optionalDependencies": {"fsevents": "2.3.2"}, "devDependencies": {"@babel/core": "^7.18.13", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-object-rest-spread": "^7.18.9", "@babel/plugin-proposal-optional-chaining": "^7.18.9", "@babel/plugin-transform-runtime": "^7.18.10", "@babel/preset-env": "^7.18.10", "@babel/preset-typescript": "^7.18.6", "@babel/runtime": "^7.18.9", "@cypress/code-coverage": "^3.10.0", "@cypress/webpack-preprocessor": "5.12.2", "@types/chai": "^4.3.3", "@types/gl-vec2": "^1.3.2", "@types/lodash": "^4.14.184", "@types/mocha": "^5.2.7", "@types/sinon": "^10.0.13", "@types/sinon-chai": "^3.2.8", "@typescript-eslint/eslint-plugin": "^5.34.0", "@typescript-eslint/parser": "^5.34.0", "babel-loader": "^8.2.5", "babel-plugin-add-module-exports": "^1.0.4", "babel-plugin-istanbul": "^6.1.1", "chai": "^4.3.6", "core-js": "^3.24.1", "cross-env": "^7.0.3", "cypress": "^10.0.0", "eslint": "^8.22.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^17.0.0", "eslint-config-airbnb-typescript-base": "^4.0.2", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsx-a11y": "^6.6.1", "eslint-plugin-typescript-sort-keys": "^2.1.0", "esm": "^3.2.25", "mocha": "^5.2.0", "nyc": "^15.1.0", "sinon": "^14.0.0", "sinon-chai": "^3.7.0", "source-map-loader": "^1.1.1", "ts-mocha": "^10.0.0", "ts-node": "^10.9.1", "typescript": "^4.7.4", "webpack": "^4.44.2", "webpack-cli": "^3.3.12"}, "directories": {"doc": "doc"}, "dist": {"integrity": "sha512-7rBY0mPgV0iAIyrXUrijbitzglculdOizpj2IljvbhRoABvybZCJGXCxmnO7Vwuju7LclwRNZLPu+E0xmOFmSQ==", "shasum": "4d067af30ff2c82d692a381e73a3293fc9b02f37", "tarball": "https://registry.npmjs.org/@ericblade/quagga2/-/quagga2-1.7.4.tgz", "fileCount": 9, "unpackedSize": 2197552, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDib+kQE374dX57zp+Tj2UiMGc23w55PVXpuG1hH2L40gIgA8jQktDPyMkoEidlgfCuaMYNkSj9ygv1jTcskXgAYes="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjLWbgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr3Lw//UAd+HbXgeiO+LdsGTC470AcEgnOmNZsjZ69WcshCa4IM/aQW\r\npTFyLC3sXHafXgTcVY6d5QLQY8g7CibyR7wN4JUCpzJBLr/P90cPk5WD6zG6\r\nHWOgH/TNXq/RQR1a3kYgArgFZWgtX9hklBHZ3UG3KRV7uU7vGHtG+dzM5fkT\r\nIpzFQAVSBOafRF61OCVwlvGtQ8iWvCScfZSkhnNrufg6hUIjEw2/N0WNlI9l\r\noYo4oEcI51Cf2BdE/pH2qYCFchpJ84WSahYTykrmqEI2KOBBMabveQbTYi7H\r\nj2sHxup8OM8KNyBo4ClyGdd7/u4TRdyZD8YO8WPaPX7kJ/ZJO7WTUZ20lztS\r\npi10ERzL5e8VFflFHuxPpWFgmxOOmqYqdVDqjNm/+t9g0LoHGMmSj/UEBcGq\r\nogIuUXFaAZNLaesfTWEepuIup/sgCDHM+LMTgdfq23rLnzEafdwkxdA4sjTN\r\nu08zUvkT3zYFjF9/9FwTdZdIfvJ6yK91E3KrxsjWSVepYdRq9/pF/kDZ4239\r\n6VYlR3JIkDoQBhkY07pt5QYoT55ZPfoABWj4PRKhWxMidFRa0zRXyOOzLRVP\r\nG/AecgG8qdED4+tdC97pDzoHkQvuUZwtKakM46mXRT3FB3IMT3468H086i8v\r\nqzmDhiJsWxc1ORBWg8deyNOGQBlZsD5ZbSk=\r\n=fS8x\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10.0"}}, "1.7.5": {"name": "@ericblade/quagga2", "version": "1.7.5", "dependencies": {"@babel/polyfill": "^7.12.1", "get-pixels": "^3.3.3", "gl-mat2": "^1.0.1", "gl-vec2": "^1.3.0", "gl-vec3": "^1.1.3", "lodash": "^4.17.21", "ndarray": "^1.0.19", "ndarray-linear-interpolate": "^1.0.0", "fsevents": "2.3.2"}, "optionalDependencies": {"fsevents": "2.3.2"}, "devDependencies": {"@babel/core": "^7.18.13", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-object-rest-spread": "^7.18.9", "@babel/plugin-proposal-optional-chaining": "^7.18.9", "@babel/plugin-transform-runtime": "^7.18.10", "@babel/preset-env": "^7.18.10", "@babel/preset-typescript": "^7.18.6", "@babel/runtime": "^7.18.9", "@cypress/code-coverage": "^3.10.0", "@cypress/webpack-preprocessor": "5.12.2", "@types/chai": "^4.3.3", "@types/gl-vec2": "^1.3.2", "@types/lodash": "^4.14.184", "@types/mocha": "^5.2.7", "@types/sinon": "^10.0.13", "@types/sinon-chai": "^3.2.8", "@typescript-eslint/eslint-plugin": "^5.34.0", "@typescript-eslint/parser": "^5.34.0", "babel-loader": "^8.2.5", "babel-plugin-add-module-exports": "^1.0.4", "babel-plugin-istanbul": "^6.1.1", "chai": "^4.3.6", "core-js": "^3.24.1", "cross-env": "^7.0.3", "cypress": "^10.0.0", "eslint": "^8.22.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^17.0.0", "eslint-config-airbnb-typescript-base": "^4.0.2", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsx-a11y": "^6.6.1", "eslint-plugin-typescript-sort-keys": "^2.1.0", "esm": "^3.2.25", "mocha": "^5.2.0", "nyc": "^15.1.0", "sinon": "^14.0.0", "sinon-chai": "^3.7.0", "source-map-loader": "^1.1.1", "ts-mocha": "^10.0.0", "ts-node": "^10.9.1", "typescript": "^4.7.4", "webpack": "^4.44.2", "webpack-cli": "^3.3.12"}, "directories": {"doc": "doc"}, "dist": {"integrity": "sha512-thdz46VgJah07u3VZ6RLgw/gs5xAkkcxj74+ir1Kb96B7B9IBuU0Ey/7geI20q4woSxcm+1XR24dJbUA7iq8lw==", "shasum": "76435ef40cc46d72f628492ad30d31a0adc40638", "tarball": "https://registry.npmjs.org/@ericblade/quagga2/-/quagga2-1.7.5.tgz", "fileCount": 9, "unpackedSize": 2198207, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD4l0iKvt4hP0+MsjLFZCtmUI87RO5tTuTHsCFtUd7ZwwIhAJ0Ej47fBDBW2Sy/YJoSXFEGIm3kM0Jrcc6o2hJ1J/ja"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjQ+fYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpQXw//e45NVG26eN0xpRepMPpPyFhAjBsWzf83V5JJkWYmBkfRVITu\r\nJ11mOvswXjyRVGeeUFmj928o8t7HVoFX4ftoUDwAcToZ2OdNVpTVNi5VgN4p\r\n/1B866eEa3vBrlcu87xU2miNLxa256mMRGCvglPuSgbRCfzxMMs5lxuyvD8o\r\n/2GZd6Lh4SSfHnVOP4Fb2SocNSYXx4m3rH3QHAkY15wgSKA9wTXX6p2VbymY\r\nO4Jc5U+tbug6btf6KW2d459j12VRAWUEzeR+zeZXKdJwXrcLaVA2Yr6KlcTq\r\n27F409tJexagXPm3r+5OcplgG1rHZZTFzz1rD6EAKjvzJB1npyLk1B1hrkzh\r\n1fj+Is9DXQPN0IweTX0gfRiGUbBS1tuX46C2Nl1hvSyfj5Y7uWeDcsIs6Z6h\r\nmMLTSlZ32IcwXsyEgXlqVnbJ4i82flzTxSnBDtg7PzLP9GlrNbUJ/YMO7A5g\r\ngQLRyCyDFRH1FTJWQNgXNybCZNrCgsVqoQhnS6LGvN91yALJAi8KW1oVBa2L\r\nVmcTCFHFT+DW6SZsOh24ewZ0NhmLI0cBr5agVMNYTHJ3KTA29uLaYkzVLxVQ\r\nRGdUykb41kduVijqBQJgeYJKLWFBU5KaGWtAPncR1oAbv500/MmdzVOye4Su\r\nqcNhdqS+kxvp4wzEhSSuqm994pMSxJwb2bU=\r\n=Q47X\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10.0"}}, "1.7.6": {"name": "@ericblade/quagga2", "version": "1.7.6", "dependencies": {"@babel/polyfill": "^7.12.1", "get-pixels": "^3.3.3", "gl-matrix": "^3.4.3", "lodash": "^4.17.21", "ndarray": "^1.0.19", "ndarray-linear-interpolate": "^1.0.0", "fsevents": "2.3.2"}, "optionalDependencies": {"fsevents": "2.3.2"}, "devDependencies": {"@babel/core": "^7.18.13", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-object-rest-spread": "^7.18.9", "@babel/plugin-proposal-optional-chaining": "^7.18.9", "@babel/plugin-transform-runtime": "^7.18.10", "@babel/preset-env": "^7.18.10", "@babel/preset-typescript": "^7.18.6", "@babel/runtime": "^7.18.9", "@cypress/code-coverage": "^3.10.0", "@cypress/webpack-preprocessor": "5.12.2", "@types/chai": "^4.3.3", "@types/gl-vec2": "^1.3.2", "@types/lodash": "^4.14.184", "@types/mocha": "^5.2.7", "@types/sinon": "^10.0.13", "@types/sinon-chai": "^3.2.8", "@typescript-eslint/eslint-plugin": "^5.34.0", "@typescript-eslint/parser": "^5.34.0", "babel-loader": "^8.2.5", "babel-plugin-add-module-exports": "^1.0.4", "babel-plugin-istanbul": "^6.1.1", "chai": "^4.3.6", "core-js": "^3.24.1", "cross-env": "^7.0.3", "cypress": "^10.0.0", "eslint": "^8.22.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^17.0.0", "eslint-config-airbnb-typescript-base": "^4.0.2", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsx-a11y": "^6.6.1", "eslint-plugin-typescript-sort-keys": "^2.1.0", "esm": "^3.2.25", "mocha": "^5.2.0", "nyc": "^15.1.0", "sinon": "^14.0.0", "sinon-chai": "^3.7.0", "source-map-loader": "^1.1.1", "ts-mocha": "^10.0.0", "ts-node": "^10.9.1", "typescript": "^4.7.4", "webpack": "^4.44.2", "webpack-cli": "^3.3.12"}, "directories": {"doc": "doc"}, "dist": {"integrity": "sha512-4+A1AonUwkoy9qr5S+N0z9owCN3lAxjy15Bh6I0Vl/mW8+hhYxhGieHgUq7bGplrwtZEnVyM4vp4b3ZxcCB69g==", "shasum": "53c19cf2218de97cc9e5f1ac9946f6b99fd2216e", "tarball": "https://registry.npmjs.org/@ericblade/quagga2/-/quagga2-1.7.6.tgz", "fileCount": 9, "unpackedSize": 2176600, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEy8NWcnGzfTdsrDZSrlOzNnXtPnHu85F6X4CPFkpV2bAiA/XJ9GBi9L8aszpPHFeIA1EY38Osbdc7iL9B0p0AetJQ=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjT+XMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr8dQ//VCMYUBR/ZpBVDekOG2ZMTDhJU40VHgYGcayVy7Jv9xaKMgaA\r\nTekqlLgCExjKoLpfN42mOKrY2NR6/KgUJklJHJnH3FVs0ofaFRIogHPvyFsO\r\nikGUPCU5XsdhPNv8IjVEG6fyM45EKZmeeDjioUY0Z+pb5DkesTsKDqWvAeGp\r\nMFEWb91aYynfu4o0FVOLY6vgjzPnSKBtjQQlPzbzxG5XtWSclKEcpaf/n4rf\r\nrmXax8ZeGUyrBUgXlIcu8K5DE6dGMmbDRnjB7jf5my0hTgRzBi3o/1r5o+iz\r\nt3XpLh1dak7Hl2plB7jta2XKuS1AuaTvSArki72WoVqsRQac9OVsip/pXdKm\r\nTipGEuhZ3y453PK8KgfaLIiCddXNxqzjeW7wZKUQJGhhwb1TjM/J225Atv2U\r\nZizUah8XhkQAe7yNXWqdZ0XJw+kYECAM7iQGOFTjjadVijKSKJltnInVj0RR\r\n2h27cnJQEQH7T8ye3o2TrmMuPHqnM/bm7jFmA1ulZRTr7z/OlANdWhzkz+MU\r\naTaNrdYnXQ7iDi4dd90HXB83jzxx8TZ+ML/DHWGgQBgS3hoa5JKPzL5tX/cF\r\n/M6Acs+8chk8ThjptZAcEpa4q4MsHywjGI9j1hwII0vmOo73hopps1BJ6TbS\r\nMG6lrhZY+qdneBF15CADbkOoRbo+rKW3LEA=\r\n=r/rR\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10.0"}}, "1.7.7": {"name": "@ericblade/quagga2", "version": "1.7.7", "dependencies": {"@babel/polyfill": "^7.12.1", "get-pixels": "^3.3.3", "gl-matrix": "^3.4.3", "lodash": "^4.17.21", "ndarray": "^1.0.19", "ndarray-linear-interpolate": "^1.0.0", "fsevents": "2.3.2"}, "optionalDependencies": {"fsevents": "2.3.2"}, "devDependencies": {"@babel/core": "^7.20.2", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-object-rest-spread": "^7.20.2", "@babel/plugin-proposal-optional-chaining": "^7.18.9", "@babel/plugin-transform-runtime": "^7.19.6", "@babel/preset-env": "^7.20.2", "@babel/preset-typescript": "^7.18.6", "@babel/runtime": "^7.20.1", "@cypress/code-coverage": "^3.10.0", "@cypress/webpack-preprocessor": "5.15.3", "@types/chai": "^4.3.3", "@types/gl-vec2": "^1.3.2", "@types/lodash": "^4.14.188", "@types/mocha": "^5.2.7", "@types/node": "^18.11.9", "@types/sinon": "^10.0.13", "@types/sinon-chai": "^3.2.8", "@typescript-eslint/eslint-plugin": "^5.42.0", "@typescript-eslint/parser": "^5.42.0", "babel-loader": "^8.2.5", "babel-plugin-add-module-exports": "^1.0.4", "babel-plugin-istanbul": "^6.1.1", "chai": "^4.3.6", "core-js": "^3.26.0", "cross-env": "^7.0.3", "cypress": "^10.0.0", "eslint": "^8.27.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^17.0.0", "eslint-config-airbnb-typescript-base": "^4.0.2", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsx-a11y": "^6.6.1", "eslint-plugin-typescript-sort-keys": "^2.1.0", "esm": "^3.2.25", "mocha": "^5.2.0", "nyc": "^15.1.0", "sinon": "^14.0.1", "sinon-chai": "^3.7.0", "source-map-loader": "^1.1.1", "ts-mocha": "^10.0.0", "ts-node": "^10.9.1", "typescript": "^4.8.4", "webpack": "^4.44.2", "webpack-cli": "^3.3.12"}, "directories": {"doc": "doc"}, "dist": {"integrity": "sha512-cuTgtEgr8O86kFEAjCg5BYOiKdXVk71DYQ1SwWlyA09+JWySL5GDgSSi3Pg7jc3m8SMaKvVoVxye4mgRo9nEPw==", "shasum": "2df96953b2795c2b7c5df19aac002f12cc8d02d3", "tarball": "https://registry.npmjs.org/@ericblade/quagga2/-/quagga2-1.7.7.tgz", "fileCount": 9, "unpackedSize": 2169018, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGcBw3r1EU/iAHID4ZsZr7p0DC8K4jtfuhy7N0qHkRcSAiB4OiVdkWFnH8Qttabb0ldtsW4VrP6s7zv6OTZiz1xHzA=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjaBYTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp0Qg/7BbQuFAT9mQkjETp8nJ4LFWbI2qbKuiGMMZ6Gn2IODCuchhmP\r\n4X0zC5WhLmGEyfltDAEgJecRb6kfPwmjtaVipDFyU7Ln+5BZFwucojQz3+mK\r\nss/f8o9B8XhAFh/1sI0AimBKcX8vEIQGePwrOpBJutLGqouaSg/Xy/3PnFRR\r\n7/mfiqWj8TL5+M3F83nVwhPXTuBOecBvTxriuJlTmUoqzKwr0uZLtcsYJGPG\r\nLuLifvejuFiW1lySlifT+RdbENdsRNkTZ8CP2PlgK7wVWmz/ZNbxQrt9GO9t\r\nMeCqt4VXVu3AZ9vyQSmJbWwW7u2VGmvUEL3A5qhZRHI8GNq3T2d8SGgZ/YXV\r\nlRQIn2CHfbmBK/t3ROaT1oahkViBn15U3yUmXw8FjmZOQhUE8nrTbIaq8abs\r\nZ3236HtJNoCd1IM0jrCrpVETHD+qugzgu75wWhZrx68+kGI6gXC3pO0xfdHu\r\nFZVImhuBgpA6jVvSwaRFOnpQBqCZdNCeQNf+beavnhteSJHTjSwKkn/OsW4H\r\nECIshSt+PUyL9WSx/hpEH4ij7wNvDudj3MmAN/5gsYUJF1cO+YwxxI6/YahR\r\ndn0FQRdP2RXvqhVfO8OsQkOiFujGELbyS4/CG0QNUktd6Gfm7UhEAeSO+7ty\r\nN2RN58XIzlovE+sc3WQki4T1v6IhvPGertY=\r\n=Wuej\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10.0"}}, "1.8.0": {"name": "@ericblade/quagga2", "version": "1.8.0", "dependencies": {"@babel/polyfill": "^7.12.1", "get-pixels": "^3.3.3", "gl-matrix": "^3.4.3", "lodash": "^4.17.21", "ndarray": "^1.0.19", "ndarray-linear-interpolate": "^1.0.0", "fsevents": "2.3.2"}, "optionalDependencies": {"fsevents": "2.3.2"}, "devDependencies": {"@babel/core": "^7.20.2", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-object-rest-spread": "^7.20.2", "@babel/plugin-proposal-optional-chaining": "^7.18.9", "@babel/plugin-transform-runtime": "^7.19.6", "@babel/preset-env": "^7.20.2", "@babel/preset-typescript": "^7.18.6", "@babel/runtime": "^7.20.1", "@cypress/code-coverage": "^3.10.0", "@cypress/webpack-preprocessor": "5.15.3", "@types/chai": "^4.3.3", "@types/gl-vec2": "^1.3.2", "@types/lodash": "^4.14.188", "@types/mocha": "^5.2.7", "@types/node": "^18.11.9", "@types/sinon": "^10.0.13", "@types/sinon-chai": "^3.2.8", "@typescript-eslint/eslint-plugin": "^5.42.0", "@typescript-eslint/parser": "^5.42.0", "babel-loader": "^8.2.5", "babel-plugin-add-module-exports": "^1.0.4", "babel-plugin-istanbul": "^6.1.1", "chai": "^4.3.6", "core-js": "^3.26.0", "cross-env": "^7.0.3", "cypress": "^10.0.0", "eslint": "^8.27.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^17.0.0", "eslint-config-airbnb-typescript-base": "^4.0.2", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsx-a11y": "^6.6.1", "eslint-plugin-typescript-sort-keys": "^2.1.0", "esm": "^3.2.25", "mocha": "^5.2.0", "nyc": "^15.1.0", "sinon": "^14.0.1", "sinon-chai": "^3.7.0", "source-map-loader": "^1.1.1", "ts-mocha": "^10.0.0", "ts-node": "^10.9.1", "typescript": "^4.8.4", "webpack": "^4.44.2", "webpack-cli": "^3.3.12"}, "directories": {"doc": "doc"}, "dist": {"integrity": "sha512-Opg54Ik8p+92Rxo80GIefiiSpR1cNqoFDYkeBQsDMHYBfehcAIs/FP3mT7mCmjS9GrjNFDYbvenKIeSxtF4jeA==", "shasum": "61b5aefc8ddd6851cc61e9e173d4ea02ddd1b35f", "tarball": "https://registry.npmjs.org/@ericblade/quagga2/-/quagga2-1.8.0.tgz", "fileCount": 9, "unpackedSize": 2169979, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICv04jKtNqVWsoMjJnmT4swtQAykjHdu1H5hQ4jJcqdUAiA9groogcUT7DUPMM9fEVoeR7bSX2MvoC9w0ddxEkah7Q=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjqgCTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqaFhAAhv/E1guejHshx+vWAb36kZ840aA5JJpXW0zQneAAcpT8DO2e\r\nuapfABciOFdIVuhmPavg4bKtrIPWjUdTeZEPzxtnUWot6yhbKIYPFvHrgJeK\r\n3bRT7xBwBHrZ4GgG5LLWCo6+BP83gipN0+LpGFvYeK5u/1RDZBlzYv+g3XsK\r\nELTDcUANdMiPXL5g2vkGrBbwBox5/QkAEaOhv/sGiKbWSc4erp36LeKXr53G\r\n3u9Yn6OOpmX5MWA5WQAnM+nfQiDpGHEOBf/1kaMhcviiSUuaBO2SHQc9Smbj\r\nV6iMfFR591MDPUqe83mV17n6MWVJGqT6aNktzkcqoeDfy+5d+q2y6gp7IycL\r\nxengeRt3ItkGwOSfRyYZuG+iD73e+fFYgpU264TeB/OfPpHi/TTldtzlP6bv\r\nIy2UB8LFa0SeyvssQAMtwcvL4lS/6dKf5yz9MeuRbLVGh2DIwqdBBXTgwnXq\r\nMmE3WpXgx2l0sltaAS4FlRJkpFga53tvhEe1d6+jAeUYHqki9uYjCHTu1yNq\r\nigWYZbIgTT5XFBICb7JyItBm3nU8cYgMgcouv8WDC8MtxZr+mwq3rBT9Wk+m\r\n8aU0DzHa6IpyCLFuGJVD0eN/WyzWeyHknDaH5bRdNFbDZHt3EfJdwOVyzqGI\r\nYgN11Ygzyik5HvxKFxkRLOpEWzJ7LLDqDcw=\r\n=Mhls\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10.0"}}, "1.8.1": {"name": "@ericblade/quagga2", "version": "1.8.1", "dependencies": {"@babel/polyfill": "^7.12.1", "get-pixels": "^3.3.3", "gl-matrix": "^3.4.3", "lodash": "^4.17.21", "ndarray": "^1.0.19", "ndarray-linear-interpolate": "^1.0.0", "fsevents": "2.3.2"}, "optionalDependencies": {"fsevents": "2.3.2"}, "devDependencies": {"@babel/core": "^7.20.2", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-object-rest-spread": "^7.20.2", "@babel/plugin-proposal-optional-chaining": "^7.18.9", "@babel/plugin-transform-runtime": "^7.19.6", "@babel/preset-env": "^7.20.2", "@babel/preset-typescript": "^7.18.6", "@babel/runtime": "^7.20.1", "@cypress/code-coverage": "^3.10.0", "@cypress/webpack-preprocessor": "5.15.3", "@types/chai": "^4.3.3", "@types/gl-vec2": "^1.3.2", "@types/lodash": "^4.14.188", "@types/mocha": "^5.2.7", "@types/node": "^18.11.9", "@types/sinon": "^10.0.13", "@types/sinon-chai": "^3.2.8", "@typescript-eslint/eslint-plugin": "^5.42.0", "@typescript-eslint/parser": "^5.42.0", "babel-loader": "^8.2.5", "babel-plugin-add-module-exports": "^1.0.4", "babel-plugin-istanbul": "^6.1.1", "chai": "^4.3.6", "core-js": "^3.26.0", "cross-env": "^7.0.3", "cypress": "^10.0.0", "eslint": "^8.27.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^17.0.0", "eslint-config-airbnb-typescript-base": "^4.0.2", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsx-a11y": "^6.6.1", "eslint-plugin-typescript-sort-keys": "^2.1.0", "esm": "^3.2.25", "mocha": "^5.2.0", "nyc": "^15.1.0", "sinon": "^14.0.1", "sinon-chai": "^3.7.0", "source-map-loader": "^1.1.1", "ts-mocha": "^10.0.0", "ts-node": "^10.9.1", "typescript": "^4.8.4", "webpack": "^4.44.2", "webpack-cli": "^3.3.12"}, "directories": {"doc": "doc"}, "dist": {"integrity": "sha512-fsUnZu5iQQ7n9F8T3690I82Yq86Qt/GvZZ6DOIVEqBGQk8M2C+iQVVmW6Aiq7eKFcXPXEZyxbClqPHvinN8BNw==", "shasum": "83380a67338bdf1a10207d59eacb9b52a30b6b09", "tarball": "https://registry.npmjs.org/@ericblade/quagga2/-/quagga2-1.8.1.tgz", "fileCount": 9, "unpackedSize": 2178642, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDTzXe/pZ89UQZjbLjQ9iW+UAaPe6IJ/I4dNOs5oKrd6QIhAOUZZFit4EKvH28R0hvx+2HF2GPfDgKjYZiz2bdK92CF"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjqg7yACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqqZQ/5AdeKbTp/x/7b8lAZ+U0+dFS+qKJLisfgvQ6pdv60VDWY+rMw\r\nAyWZTioe6hUQ90/p8SbBK70eXBYzzw1YRxVToRTD8XNjn4hR5i0N9C0eX6j8\r\ncjY0pRQmMriXuS6Q3+zSqUZOAg8EiWB5LHjke2ysl7psIM99PzeDCpLRYmxN\r\n7JvB9zVncHyDT7hC3m8S2INKQZaVRvWgupHqYwm1+GwgQy055SrSFzmJ1LHR\r\nggTDtl0LROrSPkY7l4st6N4f/IqzMd5SiQJLpghbtCEqNaxXGsSEggCMpXSc\r\nn65aThOa6/qb/Ieo9Qe9c+PCLSKf7uG4H4EGxox2H80TE5x+R7mKq6RhWeiD\r\nYmfm3yOhJKiZnAzJa2tLBoaKqrBMv2EhmL/vZM1mwsv/FWWtB/GpQ1ohV1Ss\r\nnvz+NeoaXwbYs1gUc7Lbe0Plst0jUGne5QZC6X++DmD8Kq+hfSaJ6lK85NTW\r\nrzMfp46NhITxQ5w5bt6EiXrz80bBjFuY1GuABR+2IDS1lHU6k+zSSFPvZbrd\r\nu/NP91qZ/Thq6HQCZR5nwaOZxkJDrXLFxXuLEA5N5Dz3jQuFoO7VOV1eCumw\r\nnzMNd713fQUURIAHybSZdESjXR2DWAB1AY/Vu4bh7wy/k1o2TS7JcLGPxdfO\r\nn5xRUHGci4IRCGUSdApWPmp4g8uPwPZuPpM=\r\n=EyVw\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10.0"}}, "1.8.2": {"name": "@ericblade/quagga2", "version": "1.8.2", "dependencies": {"@babel/polyfill": "^7.12.1", "get-pixels": "^3.3.3", "gl-matrix": "^3.4.3", "lodash": "^4.17.21", "ndarray": "^1.0.19", "ndarray-linear-interpolate": "^1.0.0", "fsevents": "2.3.2"}, "optionalDependencies": {"fsevents": "2.3.2"}, "devDependencies": {"@babel/core": "^7.20.2", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-object-rest-spread": "^7.20.2", "@babel/plugin-proposal-optional-chaining": "^7.18.9", "@babel/plugin-transform-runtime": "^7.19.6", "@babel/preset-env": "^7.20.2", "@babel/preset-typescript": "^7.18.6", "@babel/runtime": "^7.20.1", "@cypress/code-coverage": "^3.10.0", "@cypress/webpack-preprocessor": "5.15.3", "@types/chai": "^4.3.3", "@types/gl-vec2": "^1.3.2", "@types/lodash": "^4.14.188", "@types/mocha": "^5.2.7", "@types/node": "^18.11.9", "@types/sinon": "^10.0.13", "@types/sinon-chai": "^3.2.8", "@typescript-eslint/eslint-plugin": "^5.42.0", "@typescript-eslint/parser": "^5.42.0", "babel-loader": "^8.2.5", "babel-plugin-add-module-exports": "^1.0.4", "babel-plugin-istanbul": "^6.1.1", "chai": "^4.3.6", "core-js": "^3.26.0", "cross-env": "^7.0.3", "cypress": "^10.0.0", "eslint": "^8.27.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^17.0.0", "eslint-config-airbnb-typescript-base": "^4.0.2", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsx-a11y": "^6.6.1", "eslint-plugin-typescript-sort-keys": "^2.1.0", "esm": "^3.2.25", "mocha": "^5.2.0", "nyc": "^15.1.0", "sinon": "^14.0.1", "sinon-chai": "^3.7.0", "source-map-loader": "^1.1.1", "ts-mocha": "^10.0.0", "ts-node": "^10.9.1", "typescript": "^4.8.4", "webpack": "^4.44.2", "webpack-cli": "^3.3.12"}, "directories": {"doc": "doc"}, "dist": {"integrity": "sha512-UCVC6nnE0z8sjdpJqtIHJDqRdVMdK3oJdDx0O1LhBK+eKqm01upTGEEFo2lglsAaW1cVrnxWEcwHmUnNUP0ukg==", "shasum": "6216dd170d07711fb2c8317ef4f0b029f0581654", "tarball": "https://registry.npmjs.org/@ericblade/quagga2/-/quagga2-1.8.2.tgz", "fileCount": 9, "unpackedSize": 2179053, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD3fVfy3FFrKOROtNAr5lG1SP1jVDpOkZ2O9JX3wIf2TwIgA5Nbc+HcI0uebV4bkLzq9eyRD7S2F3TOpooMxPjRkPM="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjslQ1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpchw/9EZKvi5m7gQGybp72xfM6TNGpJVNoSDmT8uz57WNdeUHBZNN2\r\nQsaTcnq4C6LSwestZExV/V8oi2NkquDqu8TWnNO0MxF7bp4iOlPvXhVapFad\r\nXY0KyxOOF0cqOnPKGyXAHs5FmI42qDzH7DzJrFtbl+Mm33u0Q+9535VYKThI\r\nZbgY/opKdfgMVrm6WRN+ylJ6GDybvvjptFfuRgnWbzFauboc5Bq9OO7AmA3i\r\n8xAPAAR/WLljUNkZznelAlVZU+WHSTShSPMwAF3gmRmggc+BI4lAvgjYHmVR\r\ngwW6eH1MpEbPkS7b8Ssw5yO6vJV3rSOhxTcV+AlySATiughw/vBhJIuxvR+S\r\nAPLDj+3EvGREp5Dgbl+lVrI98pbTttdYqE3Vhojl2jjokt6kXROaEkMcUde/\r\nIoVoGXzm98P2HeOPR0NxKQQ+3F7G3i32Sm59c2C/+JvJCfSs9GyqG4G07gil\r\nC6P9lhIOwimFMzsNorClTqygA6Z/m48wgU7Cp/khX6gRC/C26zkoShmrATyU\r\n9o2AG35r2+R9OFiTjbdfs6cTaPfEqmos4/rn0J4D9O9Dk6KZVKWE+Uy1wbzK\r\nvGlOoDTNRXuKn3qpJNQjFrq7iRk8CrBzuqqNErdUNIIoqYaZ6qKaIOeWVKw8\r\niHgt7DCeYwcDPRqCu4iXduqq13S+/gpsyhA=\r\n=xsYf\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10.0"}}, "1.8.3": {"name": "@ericblade/quagga2", "version": "1.8.3", "dependencies": {"@babel/polyfill": "^7.12.1", "get-pixels": "^3.3.3", "gl-matrix": "^3.4.3", "lodash": "^4.17.21", "ndarray": "^1.0.19", "ndarray-linear-interpolate": "^1.0.0"}, "optionalDependencies": {"fsevents": "2.3.3"}, "devDependencies": {"@babel/core": "^7.23.2", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-object-rest-spread": "^7.20.2", "@babel/plugin-proposal-optional-chaining": "^7.18.9", "@babel/plugin-transform-runtime": "^7.23.2", "@babel/preset-env": "^7.23.2", "@babel/preset-typescript": "^7.23.2", "@babel/runtime": "^7.23.2", "@cypress/code-coverage": "^3.12.4", "@cypress/webpack-preprocessor": "6.0.0", "@types/chai": "^4.3.9", "@types/gl-vec2": "^1.3.4", "@types/lodash": "^4.14.200", "@types/mocha": "^5.2.7", "@types/node": "^20.8.7", "@types/sinon": "^10.0.20", "@types/sinon-chai": "^3.2.11", "@typescript-eslint/eslint-plugin": "^5.42.0", "@typescript-eslint/parser": "^5.42.0", "babel-loader": "^8.2.5", "babel-plugin-add-module-exports": "^1.0.4", "babel-plugin-istanbul": "^6.1.1", "chai": "^4.3.10", "core-js": "^3.33.1", "cross-env": "^7.0.3", "cypress": "^13.1.0", "eslint": "^8.52.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^17.1.0", "eslint-config-airbnb-typescript-base": "^4.0.2", "eslint-plugin-import": "^2.28.1", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-typescript-sort-keys": "^2.1.0", "esm": "^3.2.25", "mocha": "^5.2.0", "nyc": "^15.1.0", "sinon": "^17.0.0", "sinon-chai": "^3.7.0", "source-map-loader": "^1.1.1", "ts-mocha": "^10.0.0", "ts-node": "^10.9.1", "typescript": "^4.8.4", "webpack": "^4.44.2", "webpack-cli": "^3.3.12"}, "directories": {"doc": "doc"}, "dist": {"integrity": "sha512-4+UPpUKt9hwdUYohCE0cEsdn7P+CnvG14HvpO5m0KuoZvfJeHHXjokDUQTq4jBeiU2NZbFIns49h/LzOsN6p2w==", "shasum": "c32ffd3e3dfbaa800178e1b03a860991811915d7", "tarball": "https://registry.npmjs.org/@ericblade/quagga2/-/quagga2-1.8.3.tgz", "fileCount": 9, "unpackedSize": 2203212, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHXHvYpyQM9XLEzQ602WWi5A5GLGb0QKpxWwBY+zfZfuAiEAlq8KgMvy1tHBD6OfiJ6dvgrTSHCRzrGwewl68KIW4hI="}]}, "engines": {"node": ">= 10.0"}}, "1.8.4": {"name": "@ericblade/quagga2", "version": "1.8.4", "dependencies": {"@babel/polyfill": "^7.12.1", "get-pixels": "^3.3.3", "gl-matrix": "^3.4.3", "lodash": "^4.17.21", "ndarray": "^1.0.19", "ndarray-linear-interpolate": "^1.0.0"}, "optionalDependencies": {"fsevents": "2.3.3"}, "devDependencies": {"@babel/core": "^7.23.2", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-object-rest-spread": "^7.20.2", "@babel/plugin-proposal-optional-chaining": "^7.18.9", "@babel/plugin-transform-runtime": "^7.23.2", "@babel/preset-env": "^7.23.2", "@babel/preset-typescript": "^7.23.2", "@babel/runtime": "^7.23.2", "@cypress/code-coverage": "^3.12.4", "@cypress/webpack-preprocessor": "6.0.0", "@types/chai": "^4.3.9", "@types/gl-vec2": "^1.3.4", "@types/lodash": "^4.14.200", "@types/mocha": "^5.2.7", "@types/node": "^20.8.7", "@types/sinon": "^10.0.20", "@types/sinon-chai": "^3.2.11", "@typescript-eslint/eslint-plugin": "^5.42.0", "@typescript-eslint/parser": "^5.42.0", "babel-loader": "^8.2.5", "babel-plugin-add-module-exports": "^1.0.4", "babel-plugin-istanbul": "^6.1.1", "chai": "^4.3.10", "core-js": "^3.33.1", "cross-env": "^7.0.3", "cypress": "^13.1.0", "eslint": "^8.52.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^17.1.0", "eslint-config-airbnb-typescript-base": "^4.0.2", "eslint-plugin-import": "^2.28.1", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-typescript-sort-keys": "^2.1.0", "esm": "^3.2.25", "mocha": "^5.2.0", "nyc": "^15.1.0", "sinon": "^17.0.0", "sinon-chai": "^3.7.0", "source-map-loader": "^1.1.1", "ts-mocha": "^10.0.0", "ts-node": "^10.9.1", "typescript": "^4.8.4", "webpack": "^4.44.2", "webpack-cli": "^3.3.12"}, "directories": {"doc": "doc"}, "dist": {"integrity": "sha512-Wy0G29x3fxvmv+k11IbPL6eY+5xwdU+lj48Buhxiw8f1uyNuvrd5SomjWIi9ohuIJbCmsKuaOa5nqlvvSLVErg==", "shasum": "b5e547117bdc856762460d19d4087482c1c35ae3", "tarball": "https://registry.npmjs.org/@ericblade/quagga2/-/quagga2-1.8.4.tgz", "fileCount": 9, "unpackedSize": 2205448, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDQqCG4voYvp1a7A2L9iE8weIEH97fYykqs0mfRTmo7pAIhANGhVnL6mRQLgbQS9moYFI2RPa0kkU+p+Wlm8Eknp+Nu"}]}, "engines": {"node": ">= 10.0"}}}, "modified": "2023-10-25T23:51:43.599Z"}