{"name": "request", "dist-tags": {"latest": "2.88.2"}, "versions": {"0.9.0": {"name": "request", "version": "0.9.0", "directories": {"lib": "lib"}, "dist": {"shasum": "1049f59a6f46588e6d030921fbb84ca2f0c2714e", "tarball": "https://registry.npmjs.org/request/-/request-0.9.0.tgz", "integrity": "sha512-Dd5KRv1//FksoXzCLLaUqwTQ4VJt6CqG0pm+N/MuAj3tuxpr5m90GzbyA467Z7F6wiyPbtQAj6bTuVlpVyeY+g==", "signatures": [{"sig": "MEQCIFhriiezeTRk+oum/YFlhvhjQ+5Zjy7OFvPIEe6Hab8AAiAQC3eI3HZaU5jVE6iSjdYCGb599Mn4KsYRKyLc4HmQOA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >=0.1.90"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "0.8.3": {"name": "request", "version": "0.8.3", "directories": {"lib": "lib"}, "dist": {"shasum": "03e3a382fb677a42eac0e7b0fa8a46b504944c08", "tarball": "https://registry.npmjs.org/request/-/request-0.8.3.tgz", "integrity": "sha512-O3BR9zo5Fp28jxCwSSjSitG+/I7hCHyNS0g7kDARk6Tlqpgk2iWdb0GU/Hhzs7fXiaMQxXIbdhWV1EOB8Cvxig==", "signatures": [{"sig": "MEUCIADfc17Rxif+pHQe4daY0ovxwrag7VcH1ZQdgrL+mKf6AiEA5mN+2czlWtc4z9QdYQ4wqnTjd00wMR+k8Latfh5KfxA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >=0.1.90"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "0.10.0": {"name": "request", "version": "0.10.0", "directories": {"lib": "lib"}, "dist": {"shasum": "0910540d9b86c4f90b4c4b44c84025220a1f9d2e", "tarball": "https://registry.npmjs.org/request/-/request-0.10.0.tgz", "integrity": "sha512-Qn6cLFKm3532h7EOmaUHuONxPtefzpB5StKzR3HE+fkIu+SLHlVWHU8peg4wI9cV9BcX8AZZ7mdss4pcWtFMyA==", "signatures": [{"sig": "MEUCIQDtN8dPaaA+tJ+vcIm15O3UCLluoU6Eyx7yaKx2Xl9mwwIgY+J0qIHMdFRNn0GI+YNqfRuQaXgEsMrAzcbJYH1M27M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >=0.1.90"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "0.9.1": {"name": "request", "version": "0.9.1", "directories": {"lib": "lib"}, "dist": {"shasum": "a08a9a253f5b45447112e1ceb4d3087a1578682d", "tarball": "https://registry.npmjs.org/request/-/request-0.9.1.tgz", "integrity": "sha512-gwHvNdFs01N2kMz6GAlRlVGnMUIJpxYZI562nzjcAZwHwKugZc9hvY37N477zIvPTCHebwp7LlH4/H+bMfGwhQ==", "signatures": [{"sig": "MEYCIQC41wEAICmVMjLer2zgsMZJdPwdaLespbTsHsXgrvdBAAIhAMjFBIC/cxM+OH2UZ12MIRK1P5DKSjZlqrVkxqEuBN3P", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >=0.1.90"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "0.9.5": {"name": "request", "version": "0.9.5", "directories": {"lib": "lib"}, "dist": {"shasum": "66269143f6f5023f967159d5760369a606abfc0a", "tarball": "https://registry.npmjs.org/request/-/request-0.9.5.tgz", "integrity": "sha512-auJLUcOjMeeNN/Oaz8uaOFPD9kFNJV6sDuN8fZRZhxEYc6BIdhvtVU7s/zLs088jLkw+sX5Ea51v1J1vU7b1Ig==", "signatures": [{"sig": "MEUCIFqJz7xZtqUUh55EKVPIbR5v1mFN0fv6UtH6DsbS1sUXAiEA40zwFvx8S0xHQT17cUPf+pHHoNOYisHxJjApyUcPlIE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >=0.1.90"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "1.0.0": {"name": "request", "version": "1.0.0", "directories": {"lib": "lib"}, "dist": {"shasum": "6e482329b10657461b51db9157f64a2046f635c0", "tarball": "https://registry.npmjs.org/request/-/request-1.0.0.tgz", "integrity": "sha512-9wvnlUf9ymOQdWtAyjezi3NpktX18kyHwxquDg9wgo+wsInxtvg/Y8tPXJaCNOYK9mp9Psxb8rrymwbaWkq6cw==", "signatures": [{"sig": "MEUCIQCYTDjB0z1mG8eYISgPDOoYl8i8T5Pj53Pkk8sW5XqHtAIgDAv6YgMsL/Ty7lA+m8P/kH6xREKjS4tig9JR3dbz/6Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >=0.1.90"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "1.1.0": {"name": "request", "version": "1.1.0", "dist": {"shasum": "327162dd590a9b4bcb71d70cff1267ef10ff5101", "tarball": "https://registry.npmjs.org/request/-/request-1.1.0.tgz", "integrity": "sha512-vUugKnlvrgNiPeaJGXggWWXUPeU8z5v9uNFEAhpFbJTOZ73wM6ArUUNIYG2AfKk421LIfj6vM8ejJvKZwoVrnw==", "signatures": [{"sig": "MEUCIAagtmuuAoonE781AvmkoRCuh7yGnFb6QfOYc0beTD94AiEApmGYfDestSBF18XjgDoftjBBBn7qNeCrd5WPrSmV9Hw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node < 0.3.6"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "1.1.1": {"name": "request", "version": "1.1.1", "dist": {"shasum": "6d6581049397c51e1b0e98eca7bfd29c0c8a4f6f", "tarball": "https://registry.npmjs.org/request/-/request-1.1.1.tgz", "integrity": "sha512-rvsF+AZnEId1/3D0b8lGniBAgCYPBzxAeh8owo6dxA2Sw7FCJL08nPMUMWkpFg0V1rEQ2pOcNWBVnsFwRMEO2A==", "signatures": [{"sig": "MEYCIQDdsJK7v5a2pZV17bBrfo+OBFuVFIlUBhmcw+nX2laaiQIhAMVtSQGtLcl4PLtGAq3SF6mlIbicpSnHiODeS79QrULg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node < 0.3.6"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "1.2.0": {"name": "request", "version": "1.2.0", "dist": {"shasum": "f4b74ad2edd68c777bd640ffc74737aa0ffe5bba", "tarball": "https://registry.npmjs.org/request/-/request-1.2.0.tgz", "integrity": "sha512-/NpGjjOoV1dj0dDlaDglP3LI+TudCMP4P54gXM7Qf/EV0rwykFTIWy415SJQlvCPmpszOebUXsYb2y1GUYpvaw==", "signatures": [{"sig": "MEYCIQCFqBw+vmHSImVVutsEM0MT6Lq3IB29McMJt/p/86GqFAIhALoAcTbW0ncc8c9WfucgpWhgSq5lclRlLGqPK/xgVqhr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node < 0.3.6"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "1.9.0": {"name": "request", "version": "1.9.0", "dist": {"shasum": "67758b48da41f4240c6ed50d2bd8ced8157b4bb1", "tarball": "https://registry.npmjs.org/request/-/request-1.9.0.tgz", "integrity": "sha512-ILXT4Yedbdomi4NTtP02jixuybeo6t9xDeqidtqd3iklcLCyDaDWFAj/5rBA6PFUGSbp3bdQw1qdsqFzN/zsmg==", "signatures": [{"sig": "MEUCICMpVPC+MOa2YZefFNpixe9pKuhfYkJtBS8d9OhsL6aDAiEAxbTzxvtQjEnCNiEXUCQfaBoG8qbmGA4wAjnwZDiZCh8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.3.6"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "1.9.1": {"name": "request", "version": "1.9.1", "dist": {"shasum": "428e8283e87db620cd3303673e62ead2bd7451b8", "tarball": "https://registry.npmjs.org/request/-/request-1.9.1.tgz", "integrity": "sha512-HvL5e4Z4dzL/DhyBc+gGvsC13x/TblF0uB/N/dbB5k40DMSUII/NCCFICjyaxCyjjiNAlEmXfDwgIK6makviAA==", "signatures": [{"sig": "MEQCIBFfXBHUjiJJwg+ooTf7pkwB0ve+7CYE+L0PyuFaaxfsAiBLB7IP3OU7GfMvFsa2IW9y/VX9paRso+aZNEPkLs68sg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.3.6"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "1.9.2": {"name": "request", "version": "1.9.2", "dist": {"shasum": "b1b66f63c5250fc1fc7caeeec53fdfa0b2528c29", "tarball": "https://registry.npmjs.org/request/-/request-1.9.2.tgz", "integrity": "sha512-8wnHe30YNWM0IQ6NxAi1A1xCwZYb/Q3By1kSQl2NyIz7bAtnvNY5sOcCJ60UPlfHhIv0YjFwVt0ca5f6rPNZfA==", "signatures": [{"sig": "MEUCIQDJWXQ1/ABuvp6s7ia8nha5AKSav2HhjrCnz4aSDQ0bQQIgBJFzFQlxh1V1+uPlHswtB9+EXB7eGOVn5nViuaWxUA0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.3.6"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "1.9.3": {"name": "request", "version": "1.9.3", "dist": {"shasum": "7c4b88c1e36ef8f265f1487958c5a2042f13a624", "tarball": "https://registry.npmjs.org/request/-/request-1.9.3.tgz", "integrity": "sha512-cfTjGyvNOfOrrmXmXfmBSu/i3VsBtB536KnqlaleAylkc9/yOJLh5lc0l5ccGcX7PflwLsJfvzJyxqeiG0bfwA==", "signatures": [{"sig": "MEUCIQCf8v8g3NkkIXNpKP1aJiOHut+RmeD+XsCUqkEz9tbTYgIgYjPQyT2NDx5qq80OJhhR9QVLrIEjufOR9lKaOxtN134=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.3.6"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "1.9.5": {"name": "request", "version": "1.9.5", "dist": {"shasum": "24084f6c377fc2f3368b53be0922bebe5d40afd6", "tarball": "https://registry.npmjs.org/request/-/request-1.9.5.tgz", "integrity": "sha512-4BAHj7n3+ddE1PQiIQKVCljNRzz6ayuoRBuzUBduURnlroz59yMtaAzbxdvB0ybTHznLPQxL7QqjgOqQaUl9UA==", "signatures": [{"sig": "MEUCICp3Kk+/tb2WsWI0tBFk2rQjldNca4aBxaTYeuedNjlxAiEArm1+/CvVF3kmB1rl0Wx0ArlOgUKZkE1GauCc7imzCPs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.3.6"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "1.9.7": {"name": "request", "version": "1.9.7", "dist": {"shasum": "efc79859e5386aeb3f211f63b0d0e549026c7133", "tarball": "https://registry.npmjs.org/request/-/request-1.9.7.tgz", "integrity": "sha512-75fUnRqxfHT0h81t5hsvquHv+FU/6LaFkXZN/w0BaXp9gDx0QhKPo6+/6tuqhJU5eoP5yNWqJVFEoZEnzlI4mw==", "signatures": [{"sig": "MEYCIQCzaPtrSTz2aKLsyMAbNkwmgRL9q0yoj9gpKAQjMBrd9QIhAMOrqzoUMyvsK1a1v4jlkwU0x4QDwcpPHevUc7DMLzf8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.3.6"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "1.9.8": {"name": "request", "version": "1.9.8", "dist": {"shasum": "2da5138da2851fd5bdb50500aa7eb9e2df2a9c2b", "tarball": "https://registry.npmjs.org/request/-/request-1.9.8.tgz", "integrity": "sha512-55OXT+zfcXPr2IgPINYbBDnBLwUzxYu3K4SIE+BCWDXUB67lmTfc9iXSC6tuGnCCTfAKmdJQoCEQ80u7IunV6Q==", "signatures": [{"sig": "MEQCIFXitrXI3paW27MqvHas35V5rP0tbsQGyOiirlgc+BzJAiBzwTvIqXqnBq0FigcGaHLkuk0lba1yzmc8sih/WN66Mw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.3.6"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "1.9.9": {"name": "request", "version": "1.9.9", "dist": {"shasum": "af231fe87e3725a5db46ff74833f3aa0ebfd9151", "tarball": "https://registry.npmjs.org/request/-/request-1.9.9.tgz", "integrity": "sha512-YVWSKYdBolxLllo8GVK24jnf0ck+P4XeBEkFPz5jdVZzZ2L8jXzDJPaZ+DrIrc89S1c4fDBgu3hsluxe4X9nqQ==", "signatures": [{"sig": "MEYCIQDAiWLvMAY+vRsLTRBS69QN5QK3YooLg18YabMhfLLlcwIhAM2/Tr+X6fxOvoFnt5Eq8lsUB6iN+Xgyh5eWHI+Yj3O3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.3.6"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.0.0": {"name": "request", "version": "2.0.0", "dist": {"shasum": "e8ecdcb12ded03b795aa1de0f44afddbab98f3db", "tarball": "https://registry.npmjs.org/request/-/request-2.0.0.tgz", "integrity": "sha512-/YiTJ2FZtvOWFhRE+HHiq3GxpX4e1RMEnqYYQM/0xhQRzQ/dgdHrfK+WB4uRW3OWrcTjWA6uE8GSMhwbm7dLLw==", "signatures": [{"sig": "MEYCIQDb+YQ49NJ/z/lGlhI6s9ha3fZweZR2pCAcGDOsFGbqPgIhAI51QKQNS7DCAb0dSkI1jNelUhlLAulgStdNDZGzPxou", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.3.6"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.0.1": {"name": "request", "version": "2.0.1", "dist": {"shasum": "2188893b17b9b0c761d5e53ee0cb76a50e720d89", "tarball": "https://registry.npmjs.org/request/-/request-2.0.1.tgz", "integrity": "sha512-V0rD2kZTDmntx2LoimfFj9ba1o/jyCpk3JDiPPxM0aO06V59Tg3Dl9ivohXUTYN+wAiGxWVBsrJd5Ft3qIYiPA==", "signatures": [{"sig": "MEQCIAMvMkomcdbYEpJ6RLzcmbCE4l4DtbAplV1Bmk7Jsw3ZAiBAYoxl4C1MtJsXbPkz8JIO6HuMdf03EDE3QXMNUOKVkA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.3.6"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.0.2": {"name": "request", "version": "2.0.2", "dist": {"shasum": "8fb7985ed54581bb152e8c1c5649ec0f5a10d9b1", "tarball": "https://registry.npmjs.org/request/-/request-2.0.2.tgz", "integrity": "sha512-AhMSUZ6HVXYjlzOVzZ39ZGq4ZDrWRccVZ5EefKVgLV1FAnxujoac3Y6lunsjJ+n7OhyF7RQSVFWTa/MQsywtVA==", "signatures": [{"sig": "MEYCIQDbMKrrk1dakFBlU8cNBQmP4AXAj6sdM+Y26TwL9DUe7gIhAIXXLUp5G1NinH+AAqnFMk8JHlT96h3NqciBXuP9sJC+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.3.6"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.0.3": {"name": "request", "version": "2.0.3", "dist": {"shasum": "ec8d338be3de528ca16a5307d94bbd06298a80cc", "tarball": "https://registry.npmjs.org/request/-/request-2.0.3.tgz", "integrity": "sha512-J/qwIsTyygco2DKCy0oLVr4MbdC6qr8J8T1hmg9U6dM5sbHZEC0s9zd7HPp4FXG1d+4q2Z1jMaxI3FJd9DfgjQ==", "signatures": [{"sig": "MEQCICbVM5RdAHiCQxtITMM7wL6jh3XqQ8DBbBsFPIudWDEBAiAM+Aw2LB0dSCe6lKQcxUpATM2fcWjq1iulgvC3qVRYPw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.3.6"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.0.4": {"name": "request", "version": "2.0.4", "dist": {"shasum": "7baa221cef8923c65ba74479f7963f1214b02161", "tarball": "https://registry.npmjs.org/request/-/request-2.0.4.tgz", "integrity": "sha512-X3fiob5cD/E6gwqkuz8vPaIA7RDF6eMFIYjhWhi2qw+9bxrKzoRpY4BCohfCmlR8JTq1UH8Wv1Y46DbQt6tqeg==", "signatures": [{"sig": "MEQCIBi62QKY7QfgH0c2CH68+qQSoPwzDBf7ifJ4iKKdF91kAiBRau6n/LXL9Nyu9GcTTZkBm1kEhRA3KMLX+lohUDGi/g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.3.6"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.0.5": {"name": "request", "version": "2.0.5", "dist": {"shasum": "a3173897f02aa2f64a7cb5f06b6b931162edaa38", "tarball": "https://registry.npmjs.org/request/-/request-2.0.5.tgz", "integrity": "sha512-Gev9ZlNZTB0sDZsDKNyomovQ39u8Uq7jRN5XtGT+fFv/K1TD72hnauTLMELV+PYOvv418SmAa2Rk8JXiWfb2aQ==", "signatures": [{"sig": "MEQCIH8z1uRdTSm91hBuelavqrd1oMpRK7mP668A9tMJBeY0AiAWMXNFRtYQk3yUZPeH7brZDuhd+2Rm3g7xs9aiBlHzOw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.3.6"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.1.0": {"name": "request", "version": "2.1.0", "dist": {"shasum": "8280cbeaadeee1190e4587b0ad9dea48d8a9e1a3", "tarball": "https://registry.npmjs.org/request/-/request-2.1.0.tgz", "integrity": "sha512-0H0CNaLPz95ncl5+Q5Q6ngflcqnC8J4Gmvnirl/V3/cC1CqBL4BgK/PjsFCU7ul3CvoCQAHtw7j1vB3Y95QX4Q==", "signatures": [{"sig": "MEYCIQD7zQ+somsKE/3Zgeu9Com4OY1Ez65uSeUb2lp+DHRC8AIhAOh1l4iKJEo4+AYlsrGRIcWkghBFpQrhZRnxibKA17Xo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.3.6"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.1.1": {"name": "request", "version": "2.1.1", "dist": {"shasum": "5570e0c737d656ebd6d1d3c80aa510d3cf86c6fa", "tarball": "https://registry.npmjs.org/request/-/request-2.1.1.tgz", "integrity": "sha512-FMF+25XbxxgyPn+/twIEJeupQuu5LqifPjeNigL3hf0UQUqZHKMbgbRuUKNudxxEHNqJhSXIekZSEiikc/ApVw==", "signatures": [{"sig": "MEUCIQD3bqpIG0shXdrByhxod+tN5AjciLyLwjqPYx3tttFFeAIgU4z+vK+PQOmOTLzw5FBbZ4vkr3wc38RcAyePxiYfuIo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.3.6"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.2.0": {"name": "request", "version": "2.2.0", "dist": {"shasum": "cfa3210cd64b76a4d26b8f0cd1ec2d958d2cddc0", "tarball": "https://registry.npmjs.org/request/-/request-2.2.0.tgz", "integrity": "sha512-TgMm9MKxiseS2busbpx4aGN4WTS3y9b3rmg48v4ER5IxcjcsiDE6vUvIKMbWkdJVosczKQk+IBTx5hyByJ8Z6g==", "signatures": [{"sig": "MEUCIDuCEZQXir68YXo4MDZSBI4+wP5IhnuU2N7wNKJ9MkbUAiEA28CZexmW53jGS59kG4jHyAdwHSffGxbF1aAiVYfrtbE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.3.6"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.2.5": {"name": "request", "version": "2.2.5", "dist": {"shasum": "da3edd6a5b903563aea1531a6cb61cfe381820b4", "tarball": "https://registry.npmjs.org/request/-/request-2.2.5.tgz", "integrity": "sha512-lXR+YV2XUATijcnoR9OgJ5fkP+aQNyhmjxKRONMbof7VuGeY9ngUXYNuI9+1Y6GirrgpVYrajeYz6PJml6CBBA==", "signatures": [{"sig": "MEYCIQCwreT0jeD0VdYmSvEHp4ZpClA7Owu+627W+PEHp6eLZgIhAJsKk11wB6mThsalkKJthpNs7R6o7O4qqn/kj0Gra8Iq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.3.6"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.2.6": {"name": "request", "version": "2.2.6", "dist": {"shasum": "87dbd87959d1b85ce07253f9255878f20473d99e", "tarball": "https://registry.npmjs.org/request/-/request-2.2.6.tgz", "integrity": "sha512-kL3UIfgvH1t3GKkOcepYtcgztDE44GF1VH9cQ5d4CfoBI2WDtHf4OznkRYyFeucyRunrapP7XwXqwdv115w4DA==", "signatures": [{"sig": "MEYCIQCI33LO019wiTyojrl+6J8aknBQQBUAXPvDI5k6wfO19wIhAJHYTvhwfFA7MMTwRNdAX7x621eOh59fDflQNV1Gk6VE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.3.6"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.2.9": {"name": "request", "version": "2.2.9", "dist": {"shasum": "efbf8afbfe7f1e200d483b99752b6c42b404a0f1", "tarball": "https://registry.npmjs.org/request/-/request-2.2.9.tgz", "integrity": "sha512-ghe0epdhsogU/laqPTcWw+bS/fJ1cW6IDFXhDCr3VV42GDyMJdW8c2l+taYxLu+vuAHGh043IpypZIq/3jqEyQ==", "signatures": [{"sig": "MEUCIQDlk3SKXXcH3HiqyqO6BEnyyBQv6rGPsdzh6KBVTwB1sgIgbvF37+m416Q7MZac/soG9HgJEt3U4eBZqsvWV0+3NhM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.3.6"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.9.0": {"name": "request", "version": "2.9.0", "dist": {"shasum": "f0a672cd33f4534f9ce811ede17e3fbc18ccaafd", "tarball": "https://registry.npmjs.org/request/-/request-2.9.0.tgz", "integrity": "sha512-RLf0TD5ipXfarVOwCDgNGDbYmidMU1F8gtO69TbS0gMb4xigB/lA5Yl73xt/Cx7UTNOF7RZtaml1KqIIcX3wOg==", "signatures": [{"sig": "MEQCIFp4ygq96H5eGrmLuC69+9RNRZRZmtU/Gp1Nm8sizY+EAiBuVeZMs8fbHqaCCvZSmZpfmDrt0L6xW6JTDhtZr8nPuA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.3.6"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.9.1": {"name": "request", "version": "2.9.1", "dist": {"shasum": "54c62ebbe608fd7202c22345c45c044ba4115b1c", "tarball": "https://registry.npmjs.org/request/-/request-2.9.1.tgz", "integrity": "sha512-lwgpRo/oVvj9iykBqgL8LDbndb/87THxn6RdB61ZEiNjPshoIw5ns7TCrgotGvgemLkU4cdgkEGutUPaiIIxRg==", "signatures": [{"sig": "MEUCIFFedaIuoKW8RJdarh0bkGIrM81usSl9XSOAox/DxW9MAiEAsLQCa5M1RsMm5A/rNjqOTDjMTyauLOdAcTP862emNSg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.3.6"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.9.2": {"name": "request", "version": "2.9.2", "dist": {"shasum": "5f1c4e6c5ad085a713f83110c3d43b29a1603c23", "tarball": "https://registry.npmjs.org/request/-/request-2.9.2.tgz", "integrity": "sha512-T+fGfW3qsWj7Os6KKiTjUkxL3x1UafK+ClsPfb8NoYnKUlvNKY5nux0bVnu7gttXYI9ak4FZTIznkM2NDjRj9g==", "signatures": [{"sig": "MEYCIQDQOHaN490u4ybkX0yxgMxbR2yMblBhMHuJpzxk8ghhDgIhAJiNiCjJcvChgGXIECD3pK7tWMN70O6UNTyUQfXpNFyX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.3.6"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.9.3": {"name": "request", "version": "2.9.3", "dist": {"shasum": "ac0ac26169568837d2886a92b22c0bd1e15e7ad7", "tarball": "https://registry.npmjs.org/request/-/request-2.9.3.tgz", "integrity": "sha512-SyyCqkxodKeOkoQqbLfLnwSGmVP/HXI3/CedADYEzH0Txml8P1p2XNyhfCJR9ejk8yqPMftdeYXH9+Oi0liX6Q==", "signatures": [{"sig": "MEYCIQDXJVnBIWVzaAytikFi25EQAnIH2RTknHX1dXo9MAQ0mgIhAOKxovtFRwXomwQP211Ql1vKfEHHAWW7TJKHtBaTfPRx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.3.6"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.9.100": {"name": "request", "version": "2.9.100", "dist": {"shasum": "c9900a6cc8576b8400a82161a6268962f3d09b78", "tarball": "https://registry.npmjs.org/request/-/request-2.9.100.tgz", "integrity": "sha512-HnJn5hieQn1hoeVErtCS/Uh+CIv5KHKUEe1AYtNemWlgGk4LQKKwpZaB8drSE/edvkI9Zig89GNf7ftqL9x/NQ==", "signatures": [{"sig": "MEUCICaAGOc5qNsnZG1ZnTPxsBrZO2Ka+OoMCNpXqtMPUqxGAiEAogmarr+/uIUHjsPDzzS3POeDw/du/N80w9A8pBQCwA4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.3.6"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.9.150": {"name": "request", "version": "2.9.150", "dist": {"shasum": "a19e4ab54ee27274fe6c55b10fdc86ff413123b7", "tarball": "https://registry.npmjs.org/request/-/request-2.9.150.tgz", "integrity": "sha512-sRYFEgv3SUjqEI4altehjwv5OkS9T+BTgX2T9gshnKur6FVBZJtXPwS5mPHBHv5rxcwNpBsTRSBtRnIf/8x8rg==", "signatures": [{"sig": "MEYCIQCsINbMY8QLffG9jOFcP0TU3ElwG2VjCyDQindHaH0EmQIhAO2ETkYX4E7z9if9/n5EzHhSoLJuU8zl7LcyhKTOfEQP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.3.6"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.9.151": {"name": "request", "version": "2.9.151", "dist": {"shasum": "caa84501f05e8b9a32c22b8caead5bce3b41ea87", "tarball": "https://registry.npmjs.org/request/-/request-2.9.151.tgz", "integrity": "sha512-zq4v14V9mzQpkQT/Q7yXytgmvOovYTxT55UUgSuQJVYxNJJb4rqS771r3hXIdIWd28Ic/BTZpeb4F6Uv50Vniw==", "signatures": [{"sig": "MEQCIDFdeupFqZHxu/mqk3WwFfejVgl+m4h3xmbIuCcB3ljjAiBUOC8m+08VXySSUa3RuQPMKg+1Mxo4+EpGCVs12a7Dcg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.3.6"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.9.152": {"name": "request", "version": "2.9.152", "dist": {"shasum": "100cffcd9a012556fedcc916e647a9cd1e2bbf43", "tarball": "https://registry.npmjs.org/request/-/request-2.9.152.tgz", "integrity": "sha512-2ot/OsmzpgPoEV99RemyM0Nd4Ghuwy975ws7gwrSDHlhaQRpz6FM+W3xBDywx0174Yc7l44bP7H1i7jRfHWd4g==", "signatures": [{"sig": "MEUCIFwnGsD2SulZ/q53DE7tRv1coFspBxij0oaTh6CyBc7XAiEAoxb0mBiq4lErLtOy6ey2IPpsulA47xkpJhUxF67GzAE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.3.6"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.9.153": {"name": "request", "version": "2.9.153", "dist": {"shasum": "a93c4f7a02d733b56882a166c1e68a9688e75742", "tarball": "https://registry.npmjs.org/request/-/request-2.9.153.tgz", "integrity": "sha512-VJilcCb99AhBauo6fVH3yNfimSqEw80JcZvRkzCKtsPbBzuPtbqMsPsKqJbD+wDUvGsZPYvgkFbOBbPv3ZclFA==", "signatures": [{"sig": "MEUCIQDN9JHO9rk3lVFRDNLP78Yen7ByTEkwYnHKJ/nkL5FnagIgRBeg5HHb2I2YCFOAMFfr9qi0WJ338S3dG23iBfldwHE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.3.6"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.9.200": {"name": "request", "version": "2.9.200", "dist": {"shasum": "ccb8b3407c34fe545522aca40b77f3194978b0c6", "tarball": "https://registry.npmjs.org/request/-/request-2.9.200.tgz", "integrity": "sha512-M90+ctNWZSNr1V5x8FqGFYmjTI0+NICVxOVS1y7pqi4OuXZzryD5agPlMG8fob5tDn+k3lUQW/Aoe/leNxSeeQ==", "signatures": [{"sig": "MEQCIFad5RBE3/IPKUxg/qhEdKfhIHRiYpDPGmhXbI1wV0IxAiAldqlgQrJbCwJKPO/BPVwwAAPEKi3XZHmnrTsrmktndw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.3.6"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.9.201": {"name": "request", "version": "2.9.201", "dist": {"shasum": "65277189f7b2348aa90a1f8fe639436b6ad339fc", "tarball": "https://registry.npmjs.org/request/-/request-2.9.201.tgz", "integrity": "sha512-EH62WoOo6NHAtMDz5ntUYszD4+C+s4HmPhGJ23gQW3JBa6D7ZY5zgi+qPCqe4Zh+IeajcELA70bpTSKwPaLWbA==", "signatures": [{"sig": "MEUCIQCianLf+ifHh2xu9CAt+dgmHKD55e6ha3OFLZNXaPNA0gIgCpe8J0CeLigSJwxFaOpT+ElcuugedCKkUL2Gjp1OV+w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.3.6"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.9.202": {"name": "request", "version": "2.9.202", "dist": {"shasum": "c61399cdfbbefda5dc48653b0e9bea517d9f8497", "tarball": "https://registry.npmjs.org/request/-/request-2.9.202.tgz", "integrity": "sha512-3tc+z/Mcu5Ux4WCZ2dh3F/h7EghvlTQNr0OeplFt09gOCbSZ8MZpHwWIjX/s6I8CsineNvUBGG4/QUNLSC9nKg==", "signatures": [{"sig": "MEQCIBSj9NqOxkxVTFpMBKK0K6G39lqMRCtd4q4rJ6Z/jzu0AiB6Amt4Y62dM5Ypdl79dLUqjkZmmpR/hbljKg7ZUgMNyQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.3.6"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.9.203": {"name": "request", "version": "2.9.203", "dist": {"shasum": "6c1711a5407fb94a114219563e44145bcbf4723a", "tarball": "https://registry.npmjs.org/request/-/request-2.9.203.tgz", "integrity": "sha512-OWtna9w7yRI/gcfu3VaURgIwE1FHgbz5+fHGQ9GJTHcJ4+uvHnDjXd+N7mVDOv5+1fp1CRPzUSY2wcM345Z2Fw==", "signatures": [{"sig": "MEQCID0jsr4XHE+cRN40u1T3x/71iFSFiB65FhmcwVOelW15AiBjVDSuWElS4oRael7Se3z+Y8qTwRK5lQKt7j2Us8w9bQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.3.6"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.10.0": {"name": "request", "version": "2.10.0", "dist": {"shasum": "9911b5ef669b6500da2ae0b133fa1cfc92b1c48a", "tarball": "https://registry.npmjs.org/request/-/request-2.10.0.tgz", "integrity": "sha512-m/AqwBGi9jC0WLSobeVZmh6kxs31zRUVSvmOCUeLUJ129Lx+yfh7jnidiaJzuvtilMsHz5YeJ7W4+v2kq4Ud3g==", "signatures": [{"sig": "MEUCIB9pgTqm5Ou5TF4bLKtdTLfSJHkAmRWpMwNa9PBATtUjAiEAtoTNV6bkwVY932T9KCCiH9wtkAhMpXaBYFi50vA3yK4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.3.6"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.11.0": {"name": "request", "version": "2.11.0", "bundleDependencies": ["form-data", "mime"], "dist": {"shasum": "7d0fd1b5a9e19781c63b2eb0c3ac119dc6a97130", "tarball": "https://registry.npmjs.org/request/-/request-2.11.0.tgz", "integrity": "sha512-OQbcnNfUlm2O4Zvi0s/DEMOqBPYTbwCzrBSC8rRExQjX4YdBetqCIrUE95QSAVKw+QpJzjZ8ecnvQ1WVrqyzfw==", "signatures": [{"sig": "MEUCIQC06cA2XrCGHRWAnQmMFRSedOGXQ1FElzZ/7qluWBJlxwIgVb3bozOaO/j4WCH0U+KDvUq9PBELxb5wIp9ZEXaX9aI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.3.6"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.11.1": {"name": "request", "version": "2.11.1", "dependencies": {"mime": "~1.2.7", "form-data": "~0.0.3"}, "bundleDependencies": ["form-data", "mime"], "dist": {"shasum": "257f46bcaf4d54e62c0b61ba28fac0268f658e2c", "tarball": "https://registry.npmjs.org/request/-/request-2.11.1.tgz", "integrity": "sha512-usK6DfshUDNc0DQZms016mQ3UFwxMQKr2hMrlfwLnHmscv96gz2DIRXj7eCgNUAMXQmAzmm7g84o3YM0+xEgFw==", "signatures": [{"sig": "MEUCIF8cqr/YxFJPrO3BycEfmffNP+l09jd6LsZULO6lgDTqAiEA7k9sTN/LeQGmAV5ESkfZjDvV7Fhxv3t/+GsqgzccWhY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.3.6"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.11.2": {"name": "request", "version": "2.11.2", "dependencies": {"mime": "~1.2.7", "form-data": "~0.0.3"}, "bundleDependencies": ["form-data", "mime"], "dist": {"shasum": "070340fc7628b2a8c23b8c11395854f2f546ab34", "tarball": "https://registry.npmjs.org/request/-/request-2.11.2.tgz", "integrity": "sha512-HQgcZY21Z9dMGwjVvOE1WwVzLa8WTNZOij2iM8SQQ/F/cpj3IjTWlxP9e+oemzK5dXTePDj0Mg40VEiDsFU+sQ==", "signatures": [{"sig": "MEYCIQD2F/MlVuT+4id54+MWRcMcZabpK4h6FiSoIA4ygkRr/QIhAIZ9PX8i7zcutofNsmJA7v+PeXzQ9xSg2Nv6gvzR+Fym", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.3.6"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.11.3": {"name": "request", "version": "2.11.3", "dependencies": {"mime": "~1.2.7", "form-data": "~0.0.3"}, "bundleDependencies": ["form-data", "mime"], "dist": {"shasum": "b44425b540d65a0b2563014a28cea0dcb1e8d659", "tarball": "https://registry.npmjs.org/request/-/request-2.11.3.tgz", "integrity": "sha512-pfWT+DRPzJzhe6C/NLODNAadJp3NuIyz/MkUorafJ/OasRhKglpfg5EXV4mSRn+GIblMHyNqhWQi8ZhlcwLV4A==", "signatures": [{"sig": "MEUCIQCum8TxUYiFbbfP5GEMYFEoT0SYW0uzweGchQvBZ4NKPAIgPwm8QVTYHgxkBVG1d4cdKCkWy5wmgvR88ZqZjvj/81I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.3.6"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.11.4": {"name": "request", "version": "2.11.4", "dependencies": {"mime": "~1.2.7", "form-data": "~0.0.3"}, "bundleDependencies": ["form-data", "mime"], "dist": {"shasum": "6347d7d44e52dc588108cc1ce5cee975fc8926de", "tarball": "https://registry.npmjs.org/request/-/request-2.11.4.tgz", "integrity": "sha512-lL+DgCpVn4b8scEhyGGel3kAipjMyHHfIEcP1SCbdJq9jCuaJLSBELJ7pel5RhK3oXl/xTI9HecR4NnHSV+Hrg==", "signatures": [{"sig": "MEYCIQCTGn2vZjfIiKaysu7DdLFh0WPqpvnpJQumzg1lNsS9pwIhAKYuPYSc27o5fuEi50IfFZlikNZn8HoXn45vxCodF2CE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.3.6"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.12.0": {"name": "request", "version": "2.12.0", "dependencies": {"mime": "~1.2.7", "form-data": "~0.0.3"}, "bundleDependencies": ["form-data", "mime"], "dist": {"shasum": "11f46f20b3d0f4848c6383991c80790af16c8e48", "tarball": "https://registry.npmjs.org/request/-/request-2.12.0.tgz", "integrity": "sha512-GcOI9NyUw2lRke4kdbrjQFxu+cLugShENHvcb9QCSLLc/yH3lP7I/7RESxVbYS+AxC/r9rwzks52MRHVoZdb5w==", "signatures": [{"sig": "MEYCIQDrY1QGl7k6ekTHX05QSVn3IV1MCBxBkRqAHIJ9Kb0x2wIhAPLn8rqJcilFmBoXaYCv8LhfB9kpZDP9TX43binB2JjA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.3.6"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.14.0": {"name": "request", "version": "2.14.0", "dependencies": {"mime": "~1.2.7", "form-data": "~0.0.3"}, "bundleDependencies": ["form-data", "mime"], "dist": {"shasum": "0d8acbb0b14c1ab82e000b7d381fa8c80d1a7d88", "tarball": "https://registry.npmjs.org/request/-/request-2.14.0.tgz", "integrity": "sha512-L8hGGkSLQf2e0V5BJDX0tpKBQU1dchI2cXZj8IgahCnUwZeLoUB/z5/CcBD0jPB84rV3EC4DHTYfPsEU94JZ1g==", "signatures": [{"sig": "MEUCIE3k3bExGlJ6VDfW69T8F1/v2V3jkso2esYtOh6IsiNcAiEAlXEZJ28BWuQQd9/f2KBHM+KvLuP++U2mamNk7b60KlI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.3.6"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.16.0": {"name": "request", "version": "2.16.0", "dependencies": {"qs": "~0.5.0", "hawk": "~0.10.0", "mime": "~1.2.7", "aws-sign": "~0.2.0", "form-data": "~0.0.3", "node-uuid": "~1.4.0", "cookie-jar": "~0.2.0", "oauth-sign": "~0.2.0", "tunnel-agent": "~0.2.0", "forever-agent": "~0.2.0", "json-stringify-safe": "~3.0.0"}, "dist": {"shasum": "4e428a90da323c2a23deaa10aa729efb71da60eb", "tarball": "https://registry.npmjs.org/request/-/request-2.16.0.tgz", "integrity": "sha512-GewfOBIN4N0UJ0+tgX7ODjl5LHKlLHSkZMozzUkQrrwy0wd175R7fLWMhBlXA62L41gg0osf1/Wb9Rbde5pIsQ==", "signatures": [{"sig": "MEUCIQCnr4Bs9f1CUNtQmi2GQXIw5TgNlbCpZFBv9tfwq99EygIgSi/hfLEkniOxap0bJZW1r5eSIo337hJKEw2JWVgKLNo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.3.6"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.16.2": {"name": "request", "version": "2.16.2", "dependencies": {"qs": "~0.5.0", "hawk": "~0.10.0", "mime": "~1.2.7", "aws-sign": "~0.2.0", "form-data": "~0.0.3", "node-uuid": "~1.4.0", "cookie-jar": "~0.2.0", "oauth-sign": "~0.2.0", "tunnel-agent": "~0.2.0", "forever-agent": "~0.2.0", "json-stringify-safe": "~3.0.0"}, "dist": {"shasum": "83a028be61be4a05163e7e2e7a4b40e35df1bcb9", "tarball": "https://registry.npmjs.org/request/-/request-2.16.2.tgz", "integrity": "sha512-vL1C/7tZxSk7uWdUkjGgkPs37Z/0BrKx2kXfvHATlNrUZdAwz29gvhblnnTH7MUzsyduLENWNfmwJ6H/TlbQig==", "signatures": [{"sig": "MEUCIG29w6IYxGu/jWO23fq1kiHWgtyq58BfHF+8AkWheNJ/AiEAqXq2eNq54QUhQLliphcXYJe0uvR4znjB73kCEu7gy8Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.3.6"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.16.4": {"name": "request", "version": "2.16.4", "dependencies": {"qs": "~0.5.4", "hawk": "~0.10.2", "mime": "~1.2.7", "aws-sign": "~0.2.0", "form-data": "~0.0.3", "node-uuid": "~1.4.0", "cookie-jar": "~0.2.0", "oauth-sign": "~0.2.0", "tunnel-agent": "~0.2.0", "forever-agent": "~0.2.0", "json-stringify-safe": "~3.0.0"}, "dist": {"shasum": "9bbb264fad62cf12bd3e03ef83d7c728c4f78ff3", "tarball": "https://registry.npmjs.org/request/-/request-2.16.4.tgz", "integrity": "sha512-9SQLWcwSr/ttI+pNmUrWGATkB26HToB2+H5LGsbBKnm6BeBpRXrirfh0GaG1yWW8sQzOkjQMxuyyVu3Aa9ZCIg==", "signatures": [{"sig": "MEUCIQDe0TMfCgI40ET4YNsZNVTA14oC0aZDIrxXIuO5js40hQIgN29eb8sUFZGJrC/TBWqWoyV66cnQlb4C0mQITyhIuks=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.8.0"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.16.6": {"name": "request", "version": "2.16.6", "dependencies": {"qs": "~0.5.4", "hawk": "~0.10.2", "mime": "~1.2.7", "aws-sign": "~0.2.0", "form-data": "~0.0.3", "node-uuid": "~1.4.0", "cookie-jar": "~0.2.0", "oauth-sign": "~0.2.0", "tunnel-agent": "~0.2.0", "forever-agent": "~0.2.0", "json-stringify-safe": "~3.0.0"}, "dist": {"shasum": "872fe445ae72de266b37879d6ad7dc948fa01cad", "tarball": "https://registry.npmjs.org/request/-/request-2.16.6.tgz", "integrity": "sha512-TfD4kMo40kwuOpO7GYfAZpb2wYdw7yvTIglPNgPPSmp2Fz6MKNvPLla40FQ/ypdhy6B2jRNz3VlCjPD6mnzsmA==", "signatures": [{"sig": "MEQCIBraxEQt4tsT3/Pp20Y/tRhVsWp79G5sudGqxJCvPtgPAiBtvpsi33UVDQRYC0lNe83awIwADhv0f0M2SCKppRXl7g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.8.0"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.18.0": {"name": "request", "version": "2.18.0", "dependencies": {"qs": "~0.5.4", "hawk": "~0.10.2", "mime": "~1.2.7", "aws-sign": "~0.3.0", "form-data": "~0.0.3", "node-uuid": "~1.4.0", "cookie-jar": "~0.3.0", "oauth-sign": "~0.3.0", "tunnel-agent": "~0.5.0", "forever-agent": "~0.3.0", "http-signature": "~0.9.1", "json-stringify-safe": "~3.0.0"}, "dist": {"shasum": "8abf1ae86ba12553832984dab7d2a46880025fc5", "tarball": "https://registry.npmjs.org/request/-/request-2.18.0.tgz", "integrity": "sha512-oCWztPHBEBx1oTA8N5sFSjF6ABOPDtL/eUpoKu7J2fNGO4OlvTyoJK7LRQe5JJztt8pOz7W1KMKK5ntDZgH9Ig==", "signatures": [{"sig": "MEYCIQD/Jk//16E5VDvqaQazO3CmLs9apz8bOnHvvnLOVRzL+AIhAIOsyxRmkspg2LoRn2WlUaGSIU7AwVM9u9ufMi+79WGR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.8.0"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.19.0": {"name": "request", "version": "2.19.0", "dependencies": {"qs": "~0.5.4", "hawk": "~0.10.2", "mime": "~1.2.7", "aws-sign": "~0.3.0", "form-data": "~0.0.3", "node-uuid": "~1.4.0", "cookie-jar": "~0.3.0", "oauth-sign": "~0.3.0", "tunnel-agent": "~0.3.0", "forever-agent": "~0.3.0", "http-signature": "~0.9.1", "json-stringify-safe": "~3.0.0"}, "dist": {"shasum": "d7b614fd6248b4a0365a7fb7b8e1cdd081d08025", "tarball": "https://registry.npmjs.org/request/-/request-2.19.0.tgz", "integrity": "sha512-AXvYdkxQsyicPEAO0U3pZR654aSmCCNaCI0z4F4NEBwPQXSs4A8w74eJdORusik9rzlzgceMPs3QHCIrsgk+wQ==", "signatures": [{"sig": "MEQCIATwNNOZXX7dBfFfATZTuLPkXCGw10vEdH+thY3Ri76mAiBPTc5pYjS+IxAbiMq/Z7rcflqFToPQsn4CwLEefxJo/Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.8.0"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.20.0": {"name": "request", "version": "2.20.0", "dependencies": {"qs": "~0.5.4", "hawk": "~0.10.2", "mime": "~1.2.7", "aws-sign": "~0.3.0", "form-data": "~0.0.3", "node-uuid": "~1.4.0", "cookie-jar": "~0.3.0", "oauth-sign": "~0.3.0", "tunnel-agent": "~0.3.0", "forever-agent": "~0.3.0", "http-signature": "~0.9.1", "json-stringify-safe": "~3.0.0"}, "dist": {"shasum": "497c9bc869f03c34f6cabaac962224d2d835d47d", "tarball": "https://registry.npmjs.org/request/-/request-2.20.0.tgz", "integrity": "sha512-xw4akmDNVV4TiYiz3zPKhWyos+7WtCNi/v+8dHsjJ2flMx6aM0rjWPgA0m2UuqXNS3aa4wvFtyubMLX5JRVKag==", "signatures": [{"sig": "MEYCIQCbXZ8UmsKWucM5hfxamwMmDVCBMRl/5J8xNK2H8ABQXQIhAK6YN9D/8Oo7+NKDv0/Ct+cLudoiSQjJ8vaN2VB8xb0K", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.8.0"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.21.0": {"name": "request", "version": "2.21.0", "dependencies": {"qs": "~0.6.0", "hawk": "~0.13.0", "mime": "~1.2.9", "aws-sign": "~0.3.0", "form-data": "0.0.8", "node-uuid": "~1.4.0", "cookie-jar": "~0.3.0", "oauth-sign": "~0.3.0", "tunnel-agent": "~0.3.0", "forever-agent": "~0.5.0", "http-signature": "~0.9.11", "json-stringify-safe": "~4.0.0"}, "dist": {"shasum": "5728ab9c45e5a87c99daccd530298b6673a868d7", "tarball": "https://registry.npmjs.org/request/-/request-2.21.0.tgz", "integrity": "sha512-jvDa6FC46ystc0cn+EqtJ4B32SSz/cMX7fEIv0UHX4wsYAKJYXjA5EyAMWpRQ+hWCnX9jPD1b4o7xZ/r1Tyx/Q==", "signatures": [{"sig": "MEQCIFxQJqyMfHBSgH7PBZzLFy5CUNJJ6MCFdIJhRMn3TPWmAiBXW2qd4RpYl7pnsn17UwxWqVavlSCvxvAh3VyP4RideQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.8.0"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.22.0": {"name": "request", "version": "2.22.0", "dependencies": {"qs": "~0.6.0", "hawk": "~0.13.0", "mime": "~1.2.9", "aws-sign": "~0.3.0", "form-data": "0.0.8", "node-uuid": "~1.4.0", "cookie-jar": "~0.3.0", "oauth-sign": "~0.3.0", "tunnel-agent": "~0.3.0", "forever-agent": "~0.5.0", "http-signature": "~0.10.0", "json-stringify-safe": "~4.0.0"}, "dist": {"shasum": "b883a769cc4a909571eb5004b344c43cf7e51592", "tarball": "https://registry.npmjs.org/request/-/request-2.22.0.tgz", "integrity": "sha512-s05oCBjWuzNi/UbZtvwOnSJ85/lHUdYPriJyFUwdxHKr8VcZHB0wx0eTX8y5hCH3p7OTDi9iQUqMFyDkW6K7EQ==", "signatures": [{"sig": "MEYCIQDRMjGtTiO84KvhsMz0t8BURDlwH7dbUJ0FWFOUjNixJwIhAIQZh/UfVCXrjM52II4kN27bd2gQ/DOUWdg35Jg2xSdL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.8.0"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.23.0": {"name": "request", "version": "2.23.0", "dependencies": {"qs": "~0.6.0", "hawk": "~0.13.0", "mime": "~1.2.9", "aws-sign": "~0.3.0", "form-data": "0.0.8", "node-uuid": "~1.4.0", "cookie-jar": "~0.3.0", "oauth-sign": "~0.3.0", "tunnel-agent": "~0.3.0", "forever-agent": "~0.5.0", "http-signature": "~0.10.0", "json-stringify-safe": "~4.0.0"}, "dist": {"shasum": "121742874bf40b3c149fe113b7a35847597fc885", "tarball": "https://registry.npmjs.org/request/-/request-2.23.0.tgz", "integrity": "sha512-OzY/QLUcGhIkdSyuoKBoVZKkijjJsUp0nCXavCxAG6A5nkDEvnhOi1sFcn78somoGwpV9a3RT1RAnhLtUHdGCQ==", "signatures": [{"sig": "MEUCIDPmS36DhHINtxXYFwtMYd79IC/ihdW2ItERpkqTti/ZAiEAokwR0Op6XA02iCwOamcReOHNFdS4JP9Ej618wwFEhvk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.8.0"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.24.0": {"name": "request", "version": "2.24.0", "dependencies": {"qs": "~0.6.0", "hawk": "~1.0.0", "mime": "~1.2.9", "aws-sign": "~0.3.0", "form-data": "~0.1.0", "node-uuid": "~1.4.0", "cookie-jar": "~0.3.0", "oauth-sign": "~0.3.0", "tunnel-agent": "~0.3.0", "forever-agent": "~0.5.0", "http-signature": "~0.10.0", "json-stringify-safe": "~5.0.0"}, "dist": {"shasum": "1d9f104118a0389aac5dc873c713869efceed8ef", "tarball": "https://registry.npmjs.org/request/-/request-2.24.0.tgz", "integrity": "sha512-lRJnw74Nlkq6KhfY8BtIGEmIIszdSJGj4R/SqRuNdB7IQtVofY01XllLD6wbbWFwHidvEa8DqC1R276GO8zGgQ==", "signatures": [{"sig": "MEQCIQD0lItplTxRfzs5UAxB0u9yps8UbuNf+1Q7sK1BUmrRAAIfaiJR5mczFeyv4Q36mydTzgY8oW54PyWV25x2ebMKMg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.8.0"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.25.0": {"name": "request", "version": "2.25.0", "dependencies": {"qs": "~0.6.0", "hawk": "~1.0.0", "mime": "~1.2.9", "aws-sign": "~0.3.0", "form-data": "~0.1.0", "node-uuid": "~1.4.0", "cookie-jar": "~0.3.0", "oauth-sign": "~0.3.0", "tunnel-agent": "~0.3.0", "forever-agent": "~0.5.0", "http-signature": "~0.10.0", "json-stringify-safe": "~5.0.0"}, "dist": {"shasum": "dac1673181887fe0b2ce6bd7e12f46d554a02ce9", "tarball": "https://registry.npmjs.org/request/-/request-2.25.0.tgz", "integrity": "sha512-HgRVnAwpnaDMWux0KoVJot83Ixz82QllrnkOgw7EzpOAdIrkp7NlkIL3kGUWNG0s3SP1E1vqYJd/dGgTrifgVw==", "signatures": [{"sig": "MEUCIB9nu/ZgjuIBMChACbI5bPF2pY9qsKkfu7FfbbT3Q5kNAiEAryDXTNKKLCrn1IcbNqcKbE2+kzTiknL3uwRX+PF6lEw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.8.0"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.26.0": {"name": "request", "version": "2.26.0", "dependencies": {"qs": "~0.6.0", "hawk": "~1.0.0", "mime": "~1.2.9", "aws-sign": "~0.3.0", "form-data": "~0.1.0", "node-uuid": "~1.4.0", "cookie-jar": "~0.3.0", "oauth-sign": "~0.3.0", "tunnel-agent": "~0.3.0", "forever-agent": "~0.5.0", "http-signature": "~0.10.0", "json-stringify-safe": "~5.0.0"}, "dist": {"shasum": "79b03075cbac2e22ebe41aa7fca884e869c1c212", "tarball": "https://registry.npmjs.org/request/-/request-2.26.0.tgz", "integrity": "sha512-4+OESo492n2ZbGv7z265lj+U1/fXK5nYaJhKVNHqXNpxwZtrovDk8j459IKU48SlGeAEy0o9zjlPura5+xY4ow==", "signatures": [{"sig": "MEUCICdwO8bx0uE1y/2J5bS85wMmllanr1BQdJ841cFkM4sgAiEA49rnAdY5Khharicnqcp3flHtrR/ezgetcuCIOL+IdJM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.8.0"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.27.0": {"name": "request", "version": "2.27.0", "dependencies": {"qs": "~0.6.0", "hawk": "~1.0.0", "mime": "~1.2.9", "aws-sign": "~0.3.0", "form-data": "~0.1.0", "node-uuid": "~1.4.0", "cookie-jar": "~0.3.0", "oauth-sign": "~0.3.0", "tunnel-agent": "~0.3.0", "forever-agent": "~0.5.0", "http-signature": "~0.10.0", "json-stringify-safe": "~5.0.0"}, "dist": {"shasum": "dfb1a224dd3a5a9bade4337012503d710e538668", "tarball": "https://registry.npmjs.org/request/-/request-2.27.0.tgz", "integrity": "sha512-V4AYOEmdUrf0X+CVF2hndyMzIeQ8G7LB45HER/rXZYEwNVI3QFGgLPLafQLHjqtG/ggzHEMb66Ng5IemksixsQ==", "signatures": [{"sig": "MEQCIA7YLpmZDZeMpK6kue6vI6cNTRG1OEJs1LChD77V9+rXAiBwRfMxpfHGyRG7NXM4Z+9XmEP32fsqw/Ms1pKppDltOg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.8.0"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.28.0": {"name": "request", "version": "2.28.0", "dependencies": {"qs": "~0.6.0", "hawk": "~1.0.0", "mime": "~1.2.9", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "node-uuid": "~1.4.0", "oauth-sign": "~0.3.0", "tough-cookie": "~0.9.15", "tunnel-agent": "~0.3.0", "forever-agent": "~0.5.0", "http-signature": "~0.10.0", "json-stringify-safe": "~5.0.0"}, "optionalDependencies": {"hawk": "~1.0.0", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "oauth-sign": "~0.3.0", "tough-cookie": "~0.9.15", "tunnel-agent": "~0.3.0", "http-signature": "~0.10.0"}, "dist": {"shasum": "f20c4045de01eaf6976e127cbebff113827ab33a", "tarball": "https://registry.npmjs.org/request/-/request-2.28.0.tgz", "integrity": "sha512-6Z/lLMHDdBk4Gq7bVGfpkfa3vprifgKqwvwVYjf5tt7UZJjIyz1AC4fmnTJikLkW74bgdE3FR0XF4r1mf7cmlg==", "signatures": [{"sig": "MEQCIEkWYlET73VLLW9kuY03yBsEmF2QWyGCBNEmc/Nr+x/RAiBnWeJwk/8ooSomr41wQUg21tjxHNZnYGcPQaKG4+jsqg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.8.0"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.29.0": {"name": "request", "version": "2.29.0", "dependencies": {"qs": "~0.6.0", "hawk": "~1.0.0", "mime": "~1.2.9", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "node-uuid": "~1.4.0", "oauth-sign": "~0.3.0", "tough-cookie": "~0.9.15", "tunnel-agent": "~0.3.0", "forever-agent": "~0.5.0", "http-signature": "~0.10.0", "json-stringify-safe": "~5.0.0"}, "optionalDependencies": {"hawk": "~1.0.0", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "oauth-sign": "~0.3.0", "tough-cookie": "~0.9.15", "tunnel-agent": "~0.3.0", "http-signature": "~0.10.0"}, "dist": {"shasum": "0d4b8de70d26a9911a8344af9a0e8edab81ff1c3", "tarball": "https://registry.npmjs.org/request/-/request-2.29.0.tgz", "integrity": "sha512-iAFUuqfaFTUTixvLiP0AgydECINAMJA287va2xCvmwobxPD50jNL8ScT4D5HiyiZMG+wCbvXcr49OzQdJBn19Q==", "signatures": [{"sig": "MEUCIHb4pnR0RVl74KaROTLIg9ruoNKAQh997f7TFTcsRWo6AiEArtuXUQHdJ+cl77Ipp3ErNkTPA4ZdkjIKUhpLCChwt7g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.8.0"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.30.0": {"name": "request", "version": "2.30.0", "dependencies": {"qs": "~0.6.0", "hawk": "~1.0.0", "mime": "~1.2.9", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "node-uuid": "~1.4.0", "oauth-sign": "~0.3.0", "tough-cookie": "~0.9.15", "tunnel-agent": "~0.3.0", "forever-agent": "~0.5.0", "http-signature": "~0.10.0", "json-stringify-safe": "~5.0.0"}, "optionalDependencies": {"hawk": "~1.0.0", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "oauth-sign": "~0.3.0", "tough-cookie": "~0.9.15", "tunnel-agent": "~0.3.0", "http-signature": "~0.10.0"}, "dist": {"shasum": "8e0d36f0806e8911524b072b64c5ee535a09d861", "tarball": "https://registry.npmjs.org/request/-/request-2.30.0.tgz", "integrity": "sha512-YjobnprRMBsJzyZOkIFf+GYKmuF+PsQWesN2s0yXedTCDBGpTKE2pm4viygPtGTRAtg9uF3+wmmb/+2a9Elc/A==", "signatures": [{"sig": "MEUCIQC9xR+ECNplObghM1zxAWN5+IiFoIvC4HZW05drSbyXFgIgbyj4mjc7glNOjPseHdAyoZjpbTP93T8RP4zih/g3rtc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.8.0"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.31.0": {"name": "request", "version": "2.31.0", "dependencies": {"qs": "~0.6.0", "hawk": "~1.0.0", "mime": "~1.2.9", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "node-uuid": "~1.4.0", "oauth-sign": "~0.3.0", "tough-cookie": "~0.9.15", "tunnel-agent": "~0.3.0", "forever-agent": "~0.5.0", "http-signature": "~0.10.0", "json-stringify-safe": "~5.0.0"}, "optionalDependencies": {"hawk": "~1.0.0", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "oauth-sign": "~0.3.0", "tough-cookie": "~0.9.15", "tunnel-agent": "~0.3.0", "http-signature": "~0.10.0"}, "dist": {"shasum": "4c8ac967c9a4b9410cb4ba1a61fdb644267eeeff", "tarball": "https://registry.npmjs.org/request/-/request-2.31.0.tgz", "integrity": "sha512-mR68gQIhSwipuNPRcQx1Z5SsiGEd/PiZBARCFduBnzsaoBhsTvR47tAABDdqzlh+S301QnecgkWjUkib9+mC8A==", "signatures": [{"sig": "MEUCIDT3SS14l9kQ69QZuwcVjUhhetag2ahjGcvPH/S5GxlrAiEA5eMLKr/KTeQMZ36LTJ2uuvZyShexXBhVWd0ZpDfbWkI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.8.0"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.32.0": {"name": "request", "version": "2.32.0", "dependencies": {"qs": "~0.6.0", "hawk": "~1.0.0", "mime": "~1.2.9", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "node-uuid": "~1.4.0", "oauth-sign": "~0.3.0", "tough-cookie": "~0.9.15", "tunnel-agent": "~0.3.0", "forever-agent": "~0.5.0", "http-signature": "~0.10.0", "json-stringify-safe": "~5.0.0"}, "optionalDependencies": {"hawk": "~1.0.0", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "oauth-sign": "~0.3.0", "tough-cookie": "~0.9.15", "tunnel-agent": "~0.3.0", "http-signature": "~0.10.0"}, "dist": {"shasum": "c546e8ab8ae6d431047158c74cb85bbda57586c5", "tarball": "https://registry.npmjs.org/request/-/request-2.32.0.tgz", "integrity": "sha512-I8Qkf/P6Xwc29AHN1+vri7Y1blPtf6IANSc6HsqMsm7UD98NXrqcm6eBDuHlbkWtX0pJOMPYSPxl8pX2Vlibgg==", "signatures": [{"sig": "MEUCICFJZAkP+5HLOftqF9/E3+SkHKm0BJ+pNqNFZE0oe5X0AiEA/25KHeWbjygfoc2q7ygsZHYg4rpLdpxj+3Iicw56JWc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.8.0"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.33.0": {"name": "request", "version": "2.33.0", "dependencies": {"qs": "~0.6.0", "hawk": "~1.0.0", "mime": "~1.2.9", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "node-uuid": "~1.4.0", "oauth-sign": "~0.3.0", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.3.0", "forever-agent": "~0.5.0", "http-signature": "~0.10.0", "json-stringify-safe": "~5.0.0"}, "optionalDependencies": {"hawk": "~1.0.0", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "oauth-sign": "~0.3.0", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.3.0", "http-signature": "~0.10.0"}, "dist": {"shasum": "5167878131726070ec633752ea230a2379dc65ff", "tarball": "https://registry.npmjs.org/request/-/request-2.33.0.tgz", "integrity": "sha512-FqM/Jy/kECM/UjanL+3fyQbeEBMEutBXRgltnneYIpb7R+u/3kWgzrAoj+55DjCyMYQkzXVeW4/JkwDJ1H8HxA==", "signatures": [{"sig": "MEUCICQDnhueG8aFyDCjwff177lIrlmBPkSbXCBcBEUntr67AiEAtAFkb89COiZ7v32A9sfAVXtC34rBwoYydKGARWPyzYQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.8.0"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.34.0": {"name": "request", "version": "2.34.0", "dependencies": {"qs": "~0.6.0", "hawk": "~1.0.0", "mime": "~1.2.9", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "node-uuid": "~1.4.0", "oauth-sign": "~0.3.0", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.3.0", "forever-agent": "~0.5.0", "http-signature": "~0.10.0", "json-stringify-safe": "~5.0.0"}, "optionalDependencies": {"hawk": "~1.0.0", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "oauth-sign": "~0.3.0", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.3.0", "http-signature": "~0.10.0"}, "dist": {"shasum": "b5d8b9526add4a2d4629f4d417124573996445ae", "tarball": "https://registry.npmjs.org/request/-/request-2.34.0.tgz", "integrity": "sha512-mD5mNhfkeaKMg5ZY/hZFbW4lyC/NTn34/ILGQr/XLSuxYOE6vJfL0MTPPXZcZrdt+Nh1Kce+f4B4KbGThIETxQ==", "signatures": [{"sig": "MEUCIQCGFNpBKrG6Odz4EoSJ4kjSe5FwnQW/pwqShfgzbzxKuQIgOltVM2ppXOQ/2RN8rmDleCdP3svkGjy8gvDUBUy7V9w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.8.0"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.35.0": {"name": "request", "version": "2.35.0", "dependencies": {"qs": "~0.6.0", "hawk": "~1.0.0", "mime": "~1.2.9", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "node-uuid": "~1.4.0", "oauth-sign": "~0.3.0", "lodash.merge": "~2.4.1", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "forever-agent": "~0.5.0", "http-signature": "~0.10.0", "json-stringify-safe": "~5.0.0"}, "optionalDependencies": {"hawk": "~1.0.0", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "oauth-sign": "~0.3.0", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "http-signature": "~0.10.0"}, "dist": {"shasum": "0d5c0f293479a080cba508f94342cd2415a0d297", "tarball": "https://registry.npmjs.org/request/-/request-2.35.0.tgz", "integrity": "sha512-l6TZWFCEwywj3eLWikFkIcrTWJBkX5db6Yz/NGC1RzwAujF86jzhvxv5EQKREf+jSduKNIUhh4+2QH3o3pUULQ==", "signatures": [{"sig": "MEQCIBP9/U53hnKeZY9g1ag1huCsOFCy7yXfSt/WuxBpESwbAiBVyT8LGu+btApJurKWtKzgPJM/qSNCoOQpUWp3BBQreg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.8.0"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.36.0": {"name": "request", "version": "2.36.0", "dependencies": {"qs": "~0.6.0", "hawk": "~1.0.0", "mime": "~1.2.9", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "node-uuid": "~1.4.0", "oauth-sign": "~0.3.0", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "forever-agent": "~0.5.0", "http-signature": "~0.10.0", "json-stringify-safe": "~5.0.0"}, "optionalDependencies": {"hawk": "~1.0.0", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "oauth-sign": "~0.3.0", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "http-signature": "~0.10.0"}, "dist": {"shasum": "28c6c04262c7b9ffdd21b9255374517ee6d943f5", "tarball": "https://registry.npmjs.org/request/-/request-2.36.0.tgz", "integrity": "sha512-iVii/ruMH9i8k++HYYPqi+nb1Pbgz7UOTGbFEiyhl7uDN8PhyFV2lGJa8XLIUS5tyt5scERcLkwqvCNF84Vv2Q==", "signatures": [{"sig": "MEYCIQC8BDlmIvSMEVZXWrUA2VinLuMoPRr1gcuW6biCyKuIUwIhAJrusWcWL0h2GueppEQwSKwox+8EnOLb932HXGbZinlz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.8.0"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.37.0": {"name": "request", "version": "2.37.0", "dependencies": {"qs": "~0.6.0", "hawk": "1.1.1", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "node-uuid": "~1.4.0", "mime-types": "~1.0.1", "oauth-sign": "~0.3.0", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "forever-agent": "~0.5.0", "http-signature": "~0.10.0", "json-stringify-safe": "~5.0.0"}, "optionalDependencies": {"hawk": "1.1.1", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "oauth-sign": "~0.3.0", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "http-signature": "~0.10.0"}, "dist": {"shasum": "6c04c1f0f34af0c8b7408f1c1e30d4d6bd852d46", "tarball": "https://registry.npmjs.org/request/-/request-2.37.0.tgz", "integrity": "sha512-WFkZiwB84gb8jtO7DJKkYflmFikmqIVyraK69u5hFjxHGzEFhgCwVs8+4rwqQA+DRjmVCEJqyJKCEqMTa18+bg==", "signatures": [{"sig": "MEYCIQDEx1LZmRVxItMWh2DSijsPgxkTUSf2t7q4/ven7oocCAIhALUSsrYlpWObl0TW5zcBPURLzm5aGU77LWK7glUMw9fY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.8.0"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.38.0": {"name": "request", "version": "2.38.0", "dependencies": {"qs": "~0.6.0", "hawk": "1.1.1", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "node-uuid": "~1.4.0", "mime-types": "~1.0.1", "oauth-sign": "~0.3.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "forever-agent": "~0.5.0", "http-signature": "~0.10.0", "json-stringify-safe": "~5.0.0"}, "optionalDependencies": {"hawk": "1.1.1", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "oauth-sign": "~0.3.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "http-signature": "~0.10.0"}, "dist": {"shasum": "b7e36dcbf74072feaa7156e05ef1ba2829816886", "tarball": "https://registry.npmjs.org/request/-/request-2.38.0.tgz", "integrity": "sha512-Kw1usPQZat9XsZoGs0o5MHgLCa7MhTxhJTBEEVPFjDLl99XHui6cdoGTvsaEe87xdv+/adFK+6276icWWyd/RQ==", "signatures": [{"sig": "MEUCIQDt43EcTssp1+5vuoHAnNhJsJsA7BEhcbjzQlxlYLWebwIgR0XDAt6r831fKNgFfCK5iD+AoVYu0+0iYkv4aWKlQi8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.8.0"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.39.0": {"name": "request", "version": "2.39.0", "dependencies": {"qs": "~0.6.0", "hawk": "1.1.1", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "node-uuid": "~1.4.0", "mime-types": "~1.0.1", "oauth-sign": "~0.3.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "forever-agent": "~0.5.0", "http-signature": "~0.10.0", "json-stringify-safe": "~5.0.0"}, "optionalDependencies": {"hawk": "1.1.1", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "oauth-sign": "~0.3.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "http-signature": "~0.10.0"}, "dist": {"shasum": "e01bdbec68c37a3b154681867daa2833bb38cbc4", "tarball": "https://registry.npmjs.org/request/-/request-2.39.0.tgz", "integrity": "sha512-cCWh1oPe7aVkBe46QaR4DV9nKf/TLP8ypiVtBxFkpdiJm6vDI9DFEsE9jCKcExoDbJWqncpPEh4j+YdbmNcONA==", "signatures": [{"sig": "MEUCIDw9W2YWrH4IpjHSolh5hw36CErRNe5i5MD5S9Hsm4S0AiEA/BSubC3YRSVLH0yP2/8X13SXXDM/VPe8dsnXBzVGAXQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.8.0"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.40.0": {"name": "request", "version": "2.40.0", "dependencies": {"qs": "~1.0.0", "hawk": "1.1.1", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "node-uuid": "~1.4.0", "mime-types": "~1.0.1", "oauth-sign": "~0.3.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "forever-agent": "~0.5.0", "http-signature": "~0.10.0", "json-stringify-safe": "~5.0.0"}, "optionalDependencies": {"hawk": "1.1.1", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "oauth-sign": "~0.3.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "http-signature": "~0.10.0"}, "dist": {"shasum": "4dd670f696f1e6e842e66b4b5e839301ab9beb67", "tarball": "https://registry.npmjs.org/request/-/request-2.40.0.tgz", "integrity": "sha512-waNoGB4Z7bPn+lgqPk7l7hhze4Vd68jKccnwLeS7vr9GMxz0iWQbYTbBNWzfIk87Urx7V44pu29qjF/omej+Fw==", "signatures": [{"sig": "MEUCICT8DpL7kQ+7g54k1vJEcIEIs8xxGZBvMKGNj8KgXZY4AiEAjk+KQGXwXgh5BMwp3UbXIcJMDFfD+zYGCGNPwkMLMfM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.8.0"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.41.0": {"name": "request", "version": "2.41.0", "dependencies": {"bl": "~0.9.0", "qs": "~1.2.0", "hawk": "1.1.1", "caseless": "~0.6.0", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "node-uuid": "~1.4.0", "mime-types": "~1.0.1", "oauth-sign": "~0.4.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "forever-agent": "~0.5.0", "http-signature": "~0.10.0", "json-stringify-safe": "~5.0.0"}, "optionalDependencies": {"hawk": "1.1.1", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "oauth-sign": "~0.4.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "http-signature": "~0.10.0"}, "devDependencies": {"rimraf": "~2.2.8"}, "dist": {"shasum": "752520b1abf5cb78c6321e1d3ed692d11fbd1058", "tarball": "https://registry.npmjs.org/request/-/request-2.41.0.tgz", "integrity": "sha512-ZeNsqFKXkyJ0cqPd+a00o6DaQPfmt5Bn7VFqucm2yPm+H6VSNIg6JWhKKeFxcpvrI04vUmzCUPN5fjsvu8Zvxw==", "signatures": [{"sig": "MEQCIDMbLXU8pYXhitdtzkP6uGoPYME4oxC4AWZ5J5zEkcyKAiBsk+CXhkxXtFfSED9bk63cGpoDMJLOHvN0feosm8Fw/w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.8.0"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.42.0": {"name": "request", "version": "2.42.0", "dependencies": {"bl": "~0.9.0", "qs": "~1.2.0", "hawk": "1.1.1", "caseless": "~0.6.0", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "node-uuid": "~1.4.0", "mime-types": "~1.0.1", "oauth-sign": "~0.4.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "forever-agent": "~0.5.0", "http-signature": "~0.10.0", "json-stringify-safe": "~5.0.0"}, "optionalDependencies": {"hawk": "1.1.1", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "oauth-sign": "~0.4.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "http-signature": "~0.10.0"}, "devDependencies": {"rimraf": "~2.2.8"}, "dist": {"shasum": "572bd0148938564040ac7ab148b96423a063304a", "tarball": "https://registry.npmjs.org/request/-/request-2.42.0.tgz", "integrity": "sha512-ZpqQyQWQ7AdVurjxpmP/fgpN3wAZBruO2GeD3zDijWmnqg3SYz9YY6uZC8tJF++IhZ/P2VZkZug/fFEshAkD6g==", "signatures": [{"sig": "MEUCIEWiFto4V0FH+P9ly9usWQei85uSSSPy4O0cf1nH9Xm2AiEAhJrXLFw2DsbuerZwQWojuN3ZlElYb3k2ed4qdwNfXvg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.8.0"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.43.0": {"name": "request", "version": "2.43.0", "dependencies": {"bl": "~0.9.0", "qs": "~1.2.0", "hawk": "1.1.1", "caseless": "~0.6.0", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "node-uuid": "~1.4.0", "mime-types": "~1.0.1", "oauth-sign": "~0.4.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "forever-agent": "~0.5.0", "http-signature": "~0.10.0", "json-stringify-safe": "~5.0.0"}, "optionalDependencies": {"hawk": "1.1.1", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "oauth-sign": "~0.4.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "http-signature": "~0.10.0"}, "devDependencies": {"rimraf": "~2.2.8"}, "dist": {"shasum": "6d1549482c494e13bfd4e206a3311f951be9ea63", "tarball": "https://registry.npmjs.org/request/-/request-2.43.0.tgz", "integrity": "sha512-1gdHqAy7FzGD4gReZRak7ggkOKcaEqOZ44VDm46MWWQuGqM1Gzcyc67d5xG3yxvBDo7Q7/hpVr+ypTxKYQU9Yw==", "signatures": [{"sig": "MEUCIQCDNysVq+wQcoPOHBIOjfhlC1LilTpgO3IuQk09MBf+AwIgR2FjxWftepo5yZtrecYjOziegTgKpj03pvAPxn80mIY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.8.0"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.44.0": {"name": "request", "version": "2.44.0", "dependencies": {"bl": "~0.9.0", "qs": "~1.2.0", "hawk": "1.1.1", "caseless": "~0.6.0", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "node-uuid": "~1.4.0", "mime-types": "~1.0.1", "oauth-sign": "~0.4.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "forever-agent": "~0.5.0", "http-signature": "~0.10.0", "json-stringify-safe": "~5.0.0"}, "optionalDependencies": {"hawk": "1.1.1", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "oauth-sign": "~0.4.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "http-signature": "~0.10.0"}, "devDependencies": {"rimraf": "~2.2.8"}, "dist": {"shasum": "78d62454d68853cadfb07ad31f58b9ec98072ea8", "tarball": "https://registry.npmjs.org/request/-/request-2.44.0.tgz", "integrity": "sha512-gF6ZBvOhdOAANnP9yy9rPJ34PoUA1oqkrABVN2WZWoEBOQHvQuM/h9lYEAg/DQZEMmATx7E1x9Yiq3P3EFKz3Q==", "signatures": [{"sig": "MEYCIQCEKOjm0pOk3oPZrxYXByOX1A+7OMNubpuoB1XO/edh0QIhAJmaPEWMEm1f8Xz8ZyPaWyc4S3mhCKfxTP1k004Hxom2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": ["node >= 0.8.0"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.45.0": {"name": "request", "version": "2.45.0", "dependencies": {"bl": "~0.9.0", "qs": "~1.2.0", "hawk": "1.1.1", "caseless": "~0.6.0", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "node-uuid": "~1.4.0", "mime-types": "~1.0.1", "oauth-sign": "~0.4.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "forever-agent": "~0.5.0", "http-signature": "~0.10.0", "json-stringify-safe": "~5.0.0"}, "optionalDependencies": {"hawk": "1.1.1", "aws-sign2": "~0.5.0", "oauth-sign": "~0.4.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "http-signature": "~0.10.0"}, "devDependencies": {"eslint": "0.5.1", "rimraf": "~2.2.8"}, "dist": {"shasum": "29d713a0a07f17fb2e7b61815d2010681718e93c", "tarball": "https://registry.npmjs.org/request/-/request-2.45.0.tgz", "integrity": "sha512-gEPucp8vYKQBoPhO45nAdWFvpiYklhA3TjDzseGRgDK4lYIOoWEwFff22a0Cy7uXMDw/wNpGHd2eDRvIpKDE0g==", "signatures": [{"sig": "MEUCIQCvJ8ciAUSincB3PBVNxHoSRJW90lS9Uh0DVPnpQvpjvgIgIJaVi1lAlsGyU7yGXEcvwZZdGWhp9EjJM3wPnfuTTOc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.46.0": {"name": "request", "version": "2.46.0", "dependencies": {"bl": "~0.9.0", "qs": "~1.2.0", "hawk": "1.1.1", "caseless": "~0.6.0", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "node-uuid": "~1.4.0", "mime-types": "~1.0.1", "oauth-sign": "~0.4.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "forever-agent": "~0.5.0", "http-signature": "~0.10.0", "json-stringify-safe": "~5.0.0"}, "devDependencies": {"tape": "~3.0.0", "taper": "~0.3.0", "eslint": "0.5.1", "rimraf": "~2.2.8"}, "dist": {"shasum": "359195d52eaf720bc69742579d04ad6d265a8274", "tarball": "https://registry.npmjs.org/request/-/request-2.46.0.tgz", "integrity": "sha512-cI6BttkBbiIPKBmDrzo0Ovuo9dlVGd+hrou7O/xEpY4a6EhvPYPyiR7qQb0a+Q34q5uaxfq6qJdOhmY0fupblA==", "signatures": [{"sig": "MEYCIQDfa51fqWokmcr29T5J1YtQZMDtmaCMQ3b+j4i80kioRAIhAO18W0wYRgrmXjTGK1ES1fIwXXQ089SqF5y5TEHlLJmW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.47.0": {"name": "request", "version": "2.47.0", "dependencies": {"bl": "~0.9.0", "qs": "~2.3.1", "hawk": "1.1.1", "caseless": "~0.6.0", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "node-uuid": "~1.4.0", "mime-types": "~1.0.1", "oauth-sign": "~0.4.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "forever-agent": "~0.5.0", "http-signature": "~0.10.0", "combined-stream": "~0.0.5", "json-stringify-safe": "~5.0.0"}, "devDependencies": {"tape": "~3.0.0", "taper": "~0.3.0", "eslint": "0.5.1", "rimraf": "~2.2.8"}, "dist": {"shasum": "09e9fd1a4fed6593a805ef8202b20f0c5ecb485f", "tarball": "https://registry.npmjs.org/request/-/request-2.47.0.tgz", "integrity": "sha512-7HDodfmCGAgxZWJddewFP3t3dKGFyMfb/tz9uWkyA3VbR79Wb/ydZ+OihNgOIj1IliYYbqohqox5evZgBCv5aw==", "signatures": [{"sig": "MEUCIQC7kwfKaBWxS3+P6uCCFbOoUOMk1JNd3f7IiKZSI6DcOAIgQiLpOeTgBpBFUjG/9YVCML9ZEcsT+K+IwrekZlQQkT8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.48.0": {"name": "request", "version": "2.48.0", "dependencies": {"bl": "~0.9.0", "qs": "~2.3.1", "hawk": "1.1.1", "caseless": "~0.7.0", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "node-uuid": "~1.4.0", "mime-types": "~1.0.1", "oauth-sign": "~0.5.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "forever-agent": "~0.5.0", "http-signature": "~0.10.0", "combined-stream": "~0.0.5", "json-stringify-safe": "~5.0.0"}, "devDependencies": {"tape": "~3.0.0", "taper": "~0.3.0", "eslint": "0.5.1", "rimraf": "~2.2.8"}, "dist": {"shasum": "3ae2e091c9698282d58a0e6989ece2638f0f1f28", "tarball": "https://registry.npmjs.org/request/-/request-2.48.0.tgz", "integrity": "sha512-FJdqbvTjo4HBlnTUAkjnvjvJS4cCSORYLI+eAIibhU2ripMirSpxMqJ4xu2uER85L/xb0XAKnrCmpao4M1/SPw==", "signatures": [{"sig": "MEYCIQCQtyd3SAmEGTl0pSOyF2P4ce9T6yyDzH8rJEAX/M2XCQIhAKdY+7hTkAvzrdEmmr42crNtJQu9ESZUWknaK7x+A+ac", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.49.0": {"name": "request", "version": "2.49.0", "dependencies": {"bl": "~0.9.0", "qs": "~2.3.1", "hawk": "1.1.1", "caseless": "~0.8.0", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "node-uuid": "~1.4.0", "mime-types": "~1.0.1", "oauth-sign": "~0.5.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "forever-agent": "~0.5.0", "http-signature": "~0.10.0", "combined-stream": "~0.0.5", "json-stringify-safe": "~5.0.0"}, "devDependencies": {"tape": "~3.0.0", "taper": "~0.3.0", "eslint": "0.5.1", "rimraf": "~2.2.8"}, "dist": {"shasum": "0d4f6348dc3348059b553e4db60fd2478de662a7", "tarball": "https://registry.npmjs.org/request/-/request-2.49.0.tgz", "integrity": "sha512-AV8Rypi7OHPxmkTu1W30zw8UEZW55Hx11JAgXBhucNylP34qNBjJDHoMNRdkivJYbtenSFsxGnkZeAqGXt4Q8A==", "signatures": [{"sig": "MEYCIQCLNxwRJVufovTUR+QVkEnIgHEKaM5G9WnGTc4eo4sIcwIhALzKKsgPEvKme7oNXAiPxyqmBdjcNbBTWg8F0O6eNszV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.50.0": {"name": "request", "version": "2.50.0", "dependencies": {"bl": "~0.9.0", "qs": "~2.3.1", "hawk": "1.1.1", "caseless": "~0.8.0", "aws-sign2": "~0.5.0", "form-data": "~0.2.0", "node-uuid": "~1.4.0", "mime-types": "~1.0.1", "oauth-sign": "~0.5.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "forever-agent": "~0.5.0", "http-signature": "~0.10.0", "combined-stream": "~0.0.5", "json-stringify-safe": "~5.0.0"}, "devDependencies": {"tape": "~3.0.0", "karma": "~0.12.21", "taper": "~0.3.0", "eslint": "0.5.1", "rimraf": "~2.2.8", "istanbul": "~0.3.2", "coveralls": "~2.11.2", "karma-cli": "0.0.4", "karma-tap": "~1.0.1", "browserify": "~5.9.1", "function-bind": "~1.0.0", "karma-phantomjs-launcher": "~0.1.4"}, "dist": {"shasum": "d23881a57c76b5cc8b9ca969e3acbfc3c8fea367", "tarball": "https://registry.npmjs.org/request/-/request-2.50.0.tgz", "integrity": "sha512-K3eqRa9HxSoWwDkQLQqM3Xwfmibkok/47hnZrNSIQ+aFLoST9UtkXREh6rAsJseLyf273VmhME1Xx4Cd9wNtPg==", "signatures": [{"sig": "MEYCIQC8CN6MCCgzyEhzFHhj4KHxiaOan0FKqq6FKYVfmRbX8AIhAPySz8UJ3elMWJQ2QI9gCvox5f3vDG0K4Ha3RMNTvCHS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.51.0": {"name": "request", "version": "2.51.0", "dependencies": {"bl": "~0.9.0", "qs": "~2.3.1", "hawk": "1.1.1", "caseless": "~0.8.0", "aws-sign2": "~0.5.0", "form-data": "~0.2.0", "node-uuid": "~1.4.0", "mime-types": "~1.0.1", "oauth-sign": "~0.5.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "forever-agent": "~0.5.0", "http-signature": "~0.10.0", "combined-stream": "~0.0.5", "json-stringify-safe": "~5.0.0"}, "devDependencies": {"tape": "~3.0.0", "karma": "~0.12.21", "taper": "~0.3.0", "eslint": "0.5.1", "rimraf": "~2.2.8", "istanbul": "~0.3.2", "coveralls": "~2.11.2", "karma-cli": "0.0.4", "karma-tap": "~1.0.1", "browserify": "~5.9.1", "function-bind": "~1.0.0", "karma-phantomjs-launcher": "~0.1.4"}, "dist": {"shasum": "35d00bbecc012e55f907b1bd9e0dbd577bfef26e", "tarball": "https://registry.npmjs.org/request/-/request-2.51.0.tgz", "integrity": "sha512-6pfShjLfn6ThOlPHyQo7nBxEwTa2PzvqHruxQS51TrADjWj3qetRZ2Ae5gRzMF7N2fKG5Ww7su+Z6jA3sFv0Gw==", "signatures": [{"sig": "MEUCIFBWDUdysSyliFHK97gWnGV8tDlh8dPW4fhJBFL4UMX+AiEA9KHJ1cpBfnHFk3Z2ZXDM07haaV2Ge5M7FfTvG5Wavgs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.52.0": {"name": "request", "version": "2.52.0", "dependencies": {"bl": "~0.9.0", "qs": "~2.3.1", "hawk": "~2.3.0", "caseless": "~0.9.0", "isstream": "~0.1.1", "aws-sign2": "~0.5.0", "form-data": "~0.2.0", "node-uuid": "~1.4.0", "mime-types": "~2.0.1", "oauth-sign": "~0.6.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "forever-agent": "~0.5.0", "http-signature": "~0.10.0", "combined-stream": "~0.0.5", "json-stringify-safe": "~5.0.0"}, "devDependencies": {"tape": "~3.0.0", "karma": "~0.12.21", "taper": "~0.4.0", "eslint": "0.5.1", "rimraf": "~2.2.8", "istanbul": "~0.3.2", "coveralls": "~2.11.2", "karma-cli": "0.0.4", "karma-tap": "~1.0.1", "browserify": "~5.9.1", "function-bind": "~1.0.0", "karma-coverage": "0.2.6", "server-destroy": "~1.0.0", "karma-browserify": "~3.0.1", "browserify-istanbul": "~0.1.3", "karma-phantomjs-launcher": "~0.1.4"}, "dist": {"shasum": "02d82a8adc04dc94a3a79f09fc850ade9aa21e74", "tarball": "https://registry.npmjs.org/request/-/request-2.52.0.tgz", "integrity": "sha512-U5lwtiMgtCOQB2TrfW/UVFZibdrDpheO2VF7rKrV8yqokNG0lqGqovRHBB+Z4aYdYk9TzZOxYlCU/Iwing0Fyw==", "signatures": [{"sig": "MEYCIQD7ZK2jvH81nAnUxJmJJCUX3OaK3obLMp0VjSaitdhKOgIhAJ6K6MPqyTca5YpdtDgt4TcTvuf9b3Rv64wAItcuoBM/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.53.0": {"name": "request", "version": "2.53.0", "dependencies": {"bl": "~0.9.0", "qs": "~2.3.1", "hawk": "~2.3.0", "caseless": "~0.9.0", "isstream": "~0.1.1", "aws-sign2": "~0.5.0", "form-data": "~0.2.0", "node-uuid": "~1.4.0", "mime-types": "~2.0.1", "oauth-sign": "~0.6.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "forever-agent": "~0.5.0", "http-signature": "~0.10.0", "combined-stream": "~0.0.5", "json-stringify-safe": "~5.0.0"}, "devDependencies": {"tape": "~3.0.0", "karma": "~0.12.21", "taper": "~0.4.0", "eslint": "0.5.1", "rimraf": "~2.2.8", "istanbul": "~0.3.2", "coveralls": "~2.11.2", "karma-cli": "0.0.4", "karma-tap": "~1.0.1", "browserify": "~5.9.1", "function-bind": "~1.0.0", "karma-coverage": "0.2.6", "server-destroy": "~1.0.0", "karma-browserify": "~3.0.1", "browserify-istanbul": "~0.1.3", "karma-phantomjs-launcher": "~0.1.4"}, "dist": {"shasum": "180a3ae92b7b639802e4f9545dd8fcdeb71d760c", "tarball": "https://registry.npmjs.org/request/-/request-2.53.0.tgz", "integrity": "sha512-E/kWR29ujsKySEMwTRod7i8fzxIV0v58itPRcvG3FyE0Uv/l8wujgPeXlXstBybNF0EdSVounY+vcnkBn03woQ==", "signatures": [{"sig": "MEYCIQCEYidengovhADH2N9hluiSdTuUNJRBNtoiEMM6YduS2wIhANPZtz/PK/wDEripIZVvcVy2NDrYjlBb+YNdvOOej/VB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.54.0": {"name": "request", "version": "2.54.0", "dependencies": {"bl": "~0.9.0", "qs": "~2.4.0", "hawk": "~2.3.0", "caseless": "~0.9.0", "isstream": "~0.1.1", "aws-sign2": "~0.5.0", "form-data": "~0.2.0", "node-uuid": "~1.4.0", "mime-types": "~2.0.1", "oauth-sign": "~0.6.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "forever-agent": "~0.6.0", "har-validator": "^1.4.0", "http-signature": "~0.10.0", "combined-stream": "~0.0.5", "json-stringify-safe": "~5.0.0"}, "devDependencies": {"tape": "~3.0.0", "karma": "~0.12.21", "taper": "~0.4.0", "eslint": "0.17.1", "rimraf": "~2.2.8", "istanbul": "~0.3.2", "coveralls": "~2.11.2", "karma-cli": "0.0.4", "karma-tap": "~1.0.1", "browserify": "~5.9.1", "function-bind": "~1.0.0", "karma-coverage": "0.2.6", "server-destroy": "~1.0.0", "karma-browserify": "~3.0.1", "browserify-istanbul": "~0.1.3", "karma-phantomjs-launcher": "~0.1.4"}, "dist": {"shasum": "a13917cd8e8fa73332da0bf2f84a30181def1953", "tarball": "https://registry.npmjs.org/request/-/request-2.54.0.tgz", "integrity": "sha512-1ND8gGyQEkrwGr0NVNrdBt22PZ2vjDb8Etq458qRbn96Q958C2jsPNQUwEsOSKGNmsBuIsObK3qfJL/ZENNpmw==", "signatures": [{"sig": "MEUCIQCx0dd46p0UIZNKUd9fpvGGPNByoJc+JEUh1P8IqtawWAIgEp9nod7xp2uWQN1q7mafP9t0Yk98CwZvYcE1zcIeVFo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.55.0": {"name": "request", "version": "2.55.0", "dependencies": {"bl": "~0.9.0", "qs": "~2.4.0", "hawk": "~2.3.0", "caseless": "~0.9.0", "isstream": "~0.1.1", "aws-sign2": "~0.5.0", "form-data": "~0.2.0", "node-uuid": "~1.4.0", "mime-types": "~2.0.1", "oauth-sign": "~0.6.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "forever-agent": "~0.6.0", "har-validator": "^1.4.0", "http-signature": "~0.10.0", "combined-stream": "~0.0.5", "json-stringify-safe": "~5.0.0"}, "devDependencies": {"tape": "~3.0.0", "karma": "~0.12.21", "taper": "~0.4.0", "eslint": "0.17.1", "rimraf": "~2.2.8", "bluebird": "~2.9.21", "istanbul": "~0.3.2", "coveralls": "~2.11.2", "karma-cli": "0.0.4", "karma-tap": "~1.0.1", "browserify": "~5.9.1", "function-bind": "~1.0.0", "karma-coverage": "0.2.6", "server-destroy": "~1.0.0", "karma-browserify": "~3.0.1", "browserify-istanbul": "~0.1.3", "karma-phantomjs-launcher": "~0.1.4"}, "dist": {"shasum": "d75c1cdf679d76bb100f9bffe1fe551b5c24e93d", "tarball": "https://registry.npmjs.org/request/-/request-2.55.0.tgz", "integrity": "sha512-tmHyusPYdblyvhGzDxPtDGOHWnP2h3dR9M5yO0UC5ndGGx0nRpOU+4c8bcv7utMxB7AakrE9p4B0CsqUBxYyyA==", "signatures": [{"sig": "MEYCIQDhKeoqAlAzsoAoi3ELto4SOf4Z0A6jzln6pZYC9Z/0bgIhALlJN4mf/hjmvK918EcFMAUPt0TAb8qP4yEaogOH6U9W", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.56.0": {"name": "request", "version": "2.56.0", "dependencies": {"bl": "~0.9.0", "qs": "~3.1.0", "hawk": "~2.3.0", "caseless": "~0.10.0", "isstream": "~0.1.1", "aws-sign2": "~0.5.0", "form-data": "~0.2.0", "node-uuid": "~1.4.0", "mime-types": "~2.0.1", "oauth-sign": "~0.8.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "forever-agent": "~0.6.0", "har-validator": "^1.6.1", "http-signature": "~0.11.0", "combined-stream": "~1.0.1", "json-stringify-safe": "~5.0.0"}, "devDependencies": {"tape": "~3.0.0", "karma": "~0.12.21", "taper": "~0.4.0", "eslint": "0.18.0", "rimraf": "~2.2.8", "bluebird": "~2.9.21", "istanbul": "~0.3.2", "coveralls": "~2.11.2", "karma-cli": "0.0.4", "karma-tap": "~1.0.1", "browserify": "~5.9.1", "buffer-equal": "0.0.1", "function-bind": "~1.0.0", "karma-coverage": "0.2.6", "server-destroy": "~1.0.0", "karma-browserify": "~3.0.1", "browserify-istanbul": "~0.1.3", "karma-phantomjs-launcher": "~0.1.4"}, "dist": {"shasum": "21a6bd9cfd6aff33a7749971ffac29e833fa28fe", "tarball": "https://registry.npmjs.org/request/-/request-2.56.0.tgz", "integrity": "sha512-s7vRMnqbXdbh1JE5N28Z4b1SHz37as1kNV0TAPbnCGqab9NHC2VFgBi5Ll/ftfTl8HyvT7CZJezGFVmqT0at0A==", "signatures": [{"sig": "MEUCIQDnejmLvmaBtq1eikh3Irzbc7ESLqDxt1gTp35kgAYlCwIgDGDYZbdA/NlEajldclDsCzijjea1JHleIb8yJQ33MtU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.57.0": {"name": "request", "version": "2.57.0", "dependencies": {"bl": "~0.9.0", "qs": "~3.1.0", "hawk": "~2.3.0", "caseless": "~0.10.0", "isstream": "~0.1.1", "aws-sign2": "~0.5.0", "form-data": "~0.2.0", "node-uuid": "~1.4.0", "mime-types": "~2.0.1", "oauth-sign": "~0.8.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "forever-agent": "~0.6.0", "har-validator": "^1.6.1", "http-signature": "~0.11.0", "combined-stream": "~1.0.1", "json-stringify-safe": "~5.0.0"}, "devDependencies": {"tape": "~3.0.0", "karma": "~0.12.21", "taper": "~0.4.0", "eslint": "0.18.0", "rimraf": "~2.2.8", "bluebird": "~2.9.21", "istanbul": "~0.3.2", "coveralls": "~2.11.2", "karma-cli": "0.0.4", "karma-tap": "~1.0.1", "browserify": "~5.9.1", "buffer-equal": "0.0.1", "function-bind": "~1.0.0", "karma-coverage": "0.2.6", "server-destroy": "~1.0.0", "karma-browserify": "~3.0.1", "browserify-istanbul": "~0.1.3", "karma-phantomjs-launcher": "~0.1.4"}, "dist": {"shasum": "d445105a42d009b9d724289633b449a6d723d989", "tarball": "https://registry.npmjs.org/request/-/request-2.57.0.tgz", "integrity": "sha512-MWUXeVetZBn2usb2PWmqVsAAzG7Bg7HOFQoarVxsIjO3CNYFfXCC7rwWUAtrRBxa9b3WhsLdDXx1VL8RHY/rjA==", "signatures": [{"sig": "MEUCIQCDRy6lClhyo0JmR2kca8tpVYd3Y/HZ2yl8yDRCum8zyQIgGBg0IzhRpopGfkBihDDDEgsQ+BeJgoXrsbkzGdzPhGw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.58.0": {"name": "request", "version": "2.58.0", "dependencies": {"bl": "~0.9.0", "qs": "~3.1.0", "hawk": "~2.3.0", "extend": "~2.0.1", "caseless": "~0.10.0", "isstream": "~0.1.1", "aws-sign2": "~0.5.0", "form-data": "~1.0.0-rc1", "node-uuid": "~1.4.0", "mime-types": "~2.0.1", "oauth-sign": "~0.8.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "forever-agent": "~0.6.0", "har-validator": "^1.6.1", "http-signature": "~0.11.0", "combined-stream": "~1.0.1", "json-stringify-safe": "~5.0.0"}, "devDependencies": {"tape": "~3.0.0", "karma": "~0.12.21", "taper": "~0.4.0", "eslint": "0.18.0", "rimraf": "~2.2.8", "bluebird": "~2.9.21", "istanbul": "~0.3.2", "coveralls": "~2.11.2", "karma-cli": "0.0.4", "karma-tap": "~1.0.1", "browserify": "~5.9.1", "codecov.io": "~0.1.2", "buffer-equal": "0.0.1", "function-bind": "~1.0.0", "karma-coverage": "0.2.6", "server-destroy": "~1.0.0", "karma-browserify": "~3.0.1", "browserify-istanbul": "~0.1.3", "karma-phantomjs-launcher": "~0.1.4"}, "dist": {"shasum": "b5f49c0b94aab7fad388612a1fb6ad03b6cc1580", "tarball": "https://registry.npmjs.org/request/-/request-2.58.0.tgz", "integrity": "sha512-okf2uk5kTQKSr+xClLCKA6jGH+ekhV1TfzS7eGiR0u2+ozO2uOSwFUlBZ8xx7C8Oula36UfTaYGxqs4VRpaPRw==", "signatures": [{"sig": "MEUCIQCIdXMTOuIah4rI7rnZtZISn6pkylL7Bf2N3+2awRTE2QIgOLuUb+NB2mP+DPMwHLIe43MMdEm7LyhJDf4uwmH40hw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.59.0": {"name": "request", "version": "2.59.0", "dependencies": {"bl": "~1.0.0", "qs": "~4.0.0", "hawk": "~3.1.0", "extend": "~3.0.0", "caseless": "~0.11.0", "isstream": "~0.1.1", "aws-sign2": "~0.5.0", "form-data": "~1.0.0-rc1", "node-uuid": "~1.4.0", "mime-types": "~2.1.2", "oauth-sign": "~0.8.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "forever-agent": "~0.6.0", "har-validator": "^1.6.1", "http-signature": "~0.11.0", "combined-stream": "~1.0.1", "json-stringify-safe": "~5.0.0"}, "devDependencies": {"tape": "~3.0.0", "karma": "~0.12.21", "taper": "~0.4.0", "eslint": "0.18.0", "rimraf": "~2.2.8", "bluebird": "~2.9.21", "istanbul": "~0.3.2", "coveralls": "~2.11.2", "karma-cli": "0.0.4", "karma-tap": "~1.0.1", "browserify": "~5.9.1", "codecov.io": "~0.1.2", "buffer-equal": "0.0.1", "function-bind": "~1.0.0", "karma-coverage": "0.2.6", "server-destroy": "~1.0.0", "karma-browserify": "~3.0.1", "browserify-istanbul": "~0.1.3", "karma-phantomjs-launcher": "~0.1.4"}, "dist": {"shasum": "984c7d66a8779af9300161ac83203d4b49ed9c65", "tarball": "https://registry.npmjs.org/request/-/request-2.59.0.tgz", "integrity": "sha512-KR3P4KRw8WKb4Du6lYz3gQx3wWe22p1KbTIRVG8zOXI5lsOGdCZLmMdMioZiT+cWinKkLYvHta0hYailDlQKxA==", "signatures": [{"sig": "MEQCIGSo5rDFcY4FqrFHdww/d5HDt0QBajutEIOZ8CjCWN6RAiBi5/67s38+Laffpi5/+55Y+1QnorFyERAzYYvm0a0MlA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.60.0": {"name": "request", "version": "2.60.0", "dependencies": {"bl": "~1.0.0", "qs": "~4.0.0", "hawk": "~3.1.0", "extend": "~3.0.0", "caseless": "~0.11.0", "isstream": "~0.1.1", "aws-sign2": "~0.5.0", "form-data": "~1.0.0-rc1", "node-uuid": "~1.4.0", "mime-types": "~2.1.2", "oauth-sign": "~0.8.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "forever-agent": "~0.6.0", "har-validator": "^1.6.1", "http-signature": "~0.11.0", "combined-stream": "~1.0.1", "json-stringify-safe": "~5.0.0"}, "devDependencies": {"tape": "~3.0.0", "karma": "~0.12.21", "taper": "~0.4.0", "eslint": "0.18.0", "rimraf": "~2.2.8", "bluebird": "~2.9.21", "istanbul": "~0.3.2", "coveralls": "~2.11.2", "karma-cli": "0.0.4", "karma-tap": "~1.0.1", "browserify": "~5.9.1", "codecov.io": "~0.1.2", "buffer-equal": "0.0.1", "function-bind": "~1.0.0", "karma-coverage": "0.2.6", "server-destroy": "~1.0.0", "karma-browserify": "~3.0.1", "browserify-istanbul": "~0.1.3", "karma-phantomjs-launcher": "~0.1.4"}, "dist": {"shasum": "498820957fcdded1d37749069610c85f61a29f2d", "tarball": "https://registry.npmjs.org/request/-/request-2.60.0.tgz", "integrity": "sha512-X6joBWf52/4vPVEYBuEWk142MY/QCMQLBQzXcSLxEKgU36RT0D0JicZ8nsuoiohDUhn4RmBWOGenmKoiFnTCmg==", "signatures": [{"sig": "MEQCIEv6V+iIX/on/vhV5SeLKXPGcp+1Iy+3o6VJdFuRQirFAiAu1IGwcGsa94NaDhbydU5jv4n9HE+jE7t/I4BJ8r0YUw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.61.0": {"name": "request", "version": "2.61.0", "dependencies": {"bl": "~1.0.0", "qs": "~4.0.0", "hawk": "~3.1.0", "extend": "~3.0.0", "caseless": "~0.11.0", "isstream": "~0.1.1", "aws-sign2": "~0.5.0", "form-data": "~1.0.0-rc1", "node-uuid": "~1.4.0", "mime-types": "~2.1.2", "oauth-sign": "~0.8.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "forever-agent": "~0.6.0", "har-validator": "^1.6.1", "http-signature": "~0.11.0", "combined-stream": "~1.0.1", "json-stringify-safe": "~5.0.0"}, "devDependencies": {"tape": "~3.0.0", "karma": "~0.12.21", "taper": "~0.4.0", "eslint": "0.18.0", "rimraf": "~2.2.8", "bluebird": "~2.9.21", "istanbul": "~0.3.2", "coveralls": "~2.11.2", "karma-cli": "0.0.4", "karma-tap": "~1.0.1", "browserify": "~5.9.1", "codecov.io": "~0.1.2", "buffer-equal": "0.0.1", "function-bind": "~1.0.0", "karma-coverage": "0.2.6", "server-destroy": "~1.0.0", "karma-browserify": "~3.0.1", "browserify-istanbul": "~0.1.3", "karma-phantomjs-launcher": "~0.1.4"}, "dist": {"shasum": "6973cb2ac94885f02693f554eec64481d6013f9f", "tarball": "https://registry.npmjs.org/request/-/request-2.61.0.tgz", "integrity": "sha512-VzuqfRVF3Fy53F+dgzN3yf1S2P+Jf5CygTa5CSCn5UYfZgeRK7GgshyKEKjCs1fOAFLFVaBkVJSSb+yh9vTsKw==", "signatures": [{"sig": "MEYCIQCHcbBlbMhJwU7xR6Wigwt2kRdC1rX2TubsLpD5LuYxZgIhAKKRS0iKqlseQ1/72+bhrEo/hqvGLqBQ2xgAE/E1kZ/f", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.62.0": {"name": "request", "version": "2.62.0", "dependencies": {"bl": "~1.0.0", "qs": "~5.1.0", "hawk": "~3.1.0", "extend": "~3.0.0", "caseless": "~0.11.0", "isstream": "~0.1.1", "aws-sign2": "~0.5.0", "form-data": "~1.0.0-rc1", "node-uuid": "~1.4.0", "mime-types": "~2.1.2", "oauth-sign": "~0.8.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "forever-agent": "~0.6.0", "har-validator": "^1.6.1", "http-signature": "~0.11.0", "combined-stream": "~1.0.1", "json-stringify-safe": "~5.0.0"}, "devDependencies": {"tape": "~3.0.0", "karma": "~0.12.21", "taper": "~0.4.0", "eslint": "0.18.0", "rimraf": "~2.2.8", "bluebird": "~2.9.21", "istanbul": "~0.3.2", "coveralls": "~2.11.2", "karma-cli": "0.0.4", "karma-tap": "~1.0.1", "browserify": "~5.9.1", "codecov.io": "~0.1.2", "buffer-equal": "0.0.1", "function-bind": "~1.0.0", "karma-coverage": "0.2.6", "server-destroy": "~1.0.0", "karma-browserify": "~3.0.1", "browserify-istanbul": "~0.1.3", "karma-phantomjs-launcher": "~0.1.4"}, "dist": {"shasum": "55c165f702a146f1e21e0725c0b75e1136487b0f", "tarball": "https://registry.npmjs.org/request/-/request-2.62.0.tgz", "integrity": "sha512-WhEj/HXtKaFlzD/dE08poXcEZa01pf0w79sr2265xIfhHAUvt00Hx3QvVZgZm6Z3f1aF2Nycg7hJNXbPA5ZSfg==", "signatures": [{"sig": "MEUCIFOwIt4/6MdRSVEEt/gy1Rc1PBFwsly9RBZzXhhTHwNCAiEA+itpiiKJxQNLKUckud/HeF5CQJSJ6oB0UWgtXwPV/N8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.63.0": {"name": "request", "version": "2.63.0", "dependencies": {"bl": "~1.0.0", "qs": "~5.1.0", "hawk": "~3.1.0", "extend": "~3.0.0", "caseless": "~0.11.0", "isstream": "~0.1.1", "aws-sign2": "~0.5.0", "form-data": "~1.0.0-rc1", "node-uuid": "~1.4.0", "mime-types": "~2.1.2", "oauth-sign": "~0.8.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "forever-agent": "~0.6.0", "har-validator": "^1.6.1", "http-signature": "~0.11.0", "combined-stream": "~1.0.1", "json-stringify-safe": "~5.0.0"}, "devDependencies": {"tape": "~3.0.0", "karma": "~0.12.21", "taper": "~0.4.0", "eslint": "0.18.0", "rimraf": "~2.2.8", "bluebird": "~2.9.21", "istanbul": "~0.3.2", "coveralls": "~2.11.2", "karma-cli": "0.0.4", "karma-tap": "~1.0.1", "browserify": "~5.9.1", "codecov.io": "~0.1.2", "buffer-equal": "0.0.1", "function-bind": "~1.0.0", "karma-coverage": "0.2.6", "server-destroy": "~1.0.0", "karma-browserify": "~3.0.1", "browserify-istanbul": "~0.1.3", "karma-phantomjs-launcher": "~0.1.4"}, "dist": {"shasum": "c83e7c3485e5d9bf9b146318429bc48f1253d8be", "tarball": "https://registry.npmjs.org/request/-/request-2.63.0.tgz", "integrity": "sha512-aKJ1taGX6UDm3JjQMEmEaZH3EDL7It6slBBo/YoqgowiBYewoLHqaoiqDfqmQkJ6s6RN33ZmEslUGocEtZL9CA==", "signatures": [{"sig": "MEUCIQDOPbXR/Ax6DANNpUaL7AgUUHDtGRJLQGMmIPiewL6W6gIgQFDzCtlAH9wg4F0fy6REDXLkP3mX8fkJNsEJ780MfjU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.64.0": {"name": "request", "version": "2.64.0", "dependencies": {"bl": "~1.0.0", "qs": "~5.1.0", "hawk": "~3.1.0", "extend": "~3.0.0", "caseless": "~0.11.0", "isstream": "~0.1.1", "aws-sign2": "~0.5.0", "form-data": "~1.0.0-rc1", "node-uuid": "~1.4.0", "mime-types": "~2.1.2", "oauth-sign": "~0.8.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "forever-agent": "~0.6.0", "har-validator": "^1.6.1", "http-signature": "~0.11.0", "combined-stream": "~1.0.1", "json-stringify-safe": "~5.0.0"}, "devDependencies": {"tape": "~3.0.0", "karma": "~0.12.21", "taper": "~0.4.0", "eslint": "0.18.0", "rimraf": "~2.2.8", "bluebird": "~2.9.21", "istanbul": "~0.3.2", "coveralls": "~2.11.2", "karma-cli": "0.0.4", "karma-tap": "~1.0.1", "browserify": "~5.9.1", "codecov.io": "~0.1.2", "buffer-equal": "0.0.1", "function-bind": "~1.0.0", "karma-coverage": "0.2.6", "server-destroy": "~1.0.0", "karma-browserify": "~3.0.1", "browserify-istanbul": "~0.1.3", "karma-phantomjs-launcher": "~0.1.4"}, "dist": {"shasum": "96a582423ce9b4b5c34e9b232e480173f14ba608", "tarball": "https://registry.npmjs.org/request/-/request-2.64.0.tgz", "integrity": "sha512-LsVk5VhLw6tPl7qSPHxhJBktlnJWDNLjoHiCqJxVP9CU0xDwninOi7jWHKo2Wjzb1xTg/itdHYOPXOteVBo03g==", "signatures": [{"sig": "MEUCIEtZM/8xYaAvVM+/RxYQVx7Fbtl58BReyXBopXhl+b7iAiEA5sz3XKBDiD7EqGDhnVFIzgVknu8JhOxUAhWCys/icjk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.65.0": {"name": "request", "version": "2.65.0", "dependencies": {"bl": "~1.0.0", "qs": "~5.2.0", "hawk": "~3.1.0", "extend": "~3.0.0", "caseless": "~0.11.0", "isstream": "~0.1.2", "aws-sign2": "~0.6.0", "form-data": "~1.0.0-rc3", "node-uuid": "~1.4.3", "mime-types": "~2.1.7", "oauth-sign": "~0.8.0", "stringstream": "~0.0.4", "tough-cookie": "~2.2.0", "tunnel-agent": "~0.4.1", "forever-agent": "~0.6.1", "har-validator": "~2.0.2", "http-signature": "~0.11.0", "combined-stream": "~1.0.5", "json-stringify-safe": "~5.0.1"}, "devDependencies": {"tape": "^4.2.0", "karma": "^0.13.10", "taper": "^0.4.0", "eslint": "0.18.0", "rimraf": "^2.2.8", "bluebird": "^2.10.1", "istanbul": "^0.3.21", "coveralls": "^2.11.4", "karma-cli": "^0.1.1", "karma-tap": "^1.0.3", "browserify": "^11.2.0", "codecov.io": "^0.1.6", "buffer-equal": "^0.0.1", "function-bind": "^1.0.2", "karma-coverage": "^0.2.6", "server-destroy": "^1.0.1", "karma-browserify": "^4.4.0", "browserify-istanbul": "^0.1.5", "karma-phantomjs-launcher": "^0.1.4"}, "dist": {"shasum": "cc1a3bc72b96254734fc34296da322f9486ddeba", "tarball": "https://registry.npmjs.org/request/-/request-2.65.0.tgz", "integrity": "sha512-576hyNF2kC13I++6Y1KwVE9GWirk1TBYwF2lLvqV2JI2m4Q1ULG0u+eCs4oPGvvXogsvahoixveokP0X/IBiTA==", "signatures": [{"sig": "MEUCIQDKKIwYc04GF11BXizO14nW2UaK3r6TPH+z0zuxzP1yRwIgDwUFeQUC1tsUusG5ckrx5byseBC1NsUOIn3V7umvrL8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.66.0": {"name": "request", "version": "2.66.0", "dependencies": {"bl": "~1.0.0", "qs": "~5.2.0", "hawk": "~3.1.0", "extend": "~3.0.0", "caseless": "~0.11.0", "isstream": "~0.1.2", "aws-sign2": "~0.6.0", "form-data": "~1.0.0-rc3", "node-uuid": "~1.4.7", "mime-types": "~2.1.7", "oauth-sign": "~0.8.0", "stringstream": "~0.0.4", "tough-cookie": "~2.2.0", "tunnel-agent": "~0.4.1", "forever-agent": "~0.6.1", "har-validator": "~2.0.2", "is-typedarray": "~1.0.0", "http-signature": "~1.0.2", "combined-stream": "~1.0.5", "json-stringify-safe": "~5.0.1"}, "devDependencies": {"tape": "^4.2.0", "karma": "^0.13.10", "taper": "^0.4.0", "eslint": "1.9.0", "rimraf": "^2.2.8", "bluebird": "^3.0.2", "istanbul": "^0.4.0", "coveralls": "^2.11.4", "karma-cli": "^0.1.1", "karma-tap": "^1.0.3", "browserify": "^12.0.1", "codecov.io": "^0.1.6", "buffer-equal": "^0.0.1", "function-bind": "^1.0.2", "karma-coverage": "^0.5.3", "server-destroy": "^1.0.1", "karma-browserify": "^4.4.0", "browserify-istanbul": "^0.1.5", "karma-phantomjs-launcher": "^0.1.4"}, "dist": {"shasum": "d4c7c9b2ecea2b5fca45ad2d7cfb3b6afeeaa1d8", "tarball": "https://registry.npmjs.org/request/-/request-2.66.0.tgz", "integrity": "sha512-7NSJW4jh3iU7rcd8gU0dPJ90WmsququSqJphUd0Xw37P5Dn61J5HMX0LBb23DbaC3dRKBdFS9R5RiBJeZ58rPg==", "signatures": [{"sig": "MEUCIFBOrBBZ85MPk5x9DWnAI5kRgRUXDzbhuVySfK2btrb/AiEA1dgOfdC+peSm0zbQwBF/n//hdSx90h4GsFt09n6IGcY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.67.0": {"name": "request", "version": "2.67.0", "dependencies": {"bl": "~1.0.0", "qs": "~5.2.0", "hawk": "~3.1.0", "extend": "~3.0.0", "caseless": "~0.11.0", "isstream": "~0.1.2", "aws-sign2": "~0.6.0", "form-data": "~1.0.0-rc3", "node-uuid": "~1.4.7", "mime-types": "~2.1.7", "oauth-sign": "~0.8.0", "stringstream": "~0.0.4", "tough-cookie": "~2.2.0", "tunnel-agent": "~0.4.1", "forever-agent": "~0.6.1", "har-validator": "~2.0.2", "is-typedarray": "~1.0.0", "http-signature": "~1.1.0", "combined-stream": "~1.0.5", "json-stringify-safe": "~5.0.1"}, "devDependencies": {"tape": "^4.2.0", "karma": "^0.13.10", "taper": "^0.4.0", "eslint": "1.9.0", "rimraf": "^2.2.8", "bluebird": "^3.0.2", "istanbul": "^0.4.0", "coveralls": "^2.11.4", "karma-cli": "^0.1.1", "karma-tap": "^1.0.3", "browserify": "^12.0.1", "codecov.io": "^0.1.6", "buffer-equal": "^0.0.1", "function-bind": "^1.0.2", "karma-coverage": "^0.5.3", "server-destroy": "^1.0.1", "karma-browserify": "^4.4.0", "browserify-istanbul": "^0.1.5", "karma-phantomjs-launcher": "^0.1.4"}, "dist": {"shasum": "8af74780e2bf11ea0ae9aa965c11f11afd272742", "tarball": "https://registry.npmjs.org/request/-/request-2.67.0.tgz", "integrity": "sha512-fzMRDWVEdMktE3foqvL4CBmC+AR8WvcP8pIPx6JSqqhWuPr+BxX9tKx4XiijfyeKtqqRMNpHDWqFMw4JlRPIJg==", "signatures": [{"sig": "MEYCIQCtmPBPObiMgQZucc2DRRgLwT0n3ji1UM7dgLGH1WRSHAIhAOo3otAXwaNL+NDids6iLVeArG/HBAgyCuEwPZRZ7uKU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.68.0": {"name": "request", "version": "2.68.0", "dependencies": {"bl": "~1.0.0", "qs": "~6.0.2", "hawk": "~3.1.0", "extend": "~3.0.0", "caseless": "~0.11.0", "isstream": "~0.1.2", "aws-sign2": "~0.6.0", "form-data": "~1.0.0-rc3", "node-uuid": "~1.4.7", "mime-types": "~2.1.7", "oauth-sign": "~0.8.0", "stringstream": "~0.0.4", "tough-cookie": "~2.2.0", "tunnel-agent": "~0.4.1", "forever-agent": "~0.6.1", "har-validator": "~2.0.6", "is-typedarray": "~1.0.0", "http-signature": "~1.1.0", "combined-stream": "~1.0.5", "json-stringify-safe": "~5.0.1"}, "devDependencies": {"aws4": "^1.2.1", "tape": "^4.2.0", "karma": "^0.13.10", "taper": "^0.4.0", "eslint": "1.10.3", "rimraf": "^2.2.8", "bluebird": "^3.0.2", "istanbul": "^0.4.0", "coveralls": "^2.11.4", "karma-cli": "^0.1.1", "karma-tap": "^1.0.3", "browserify": "^13.0.0", "codecov.io": "^0.1.6", "buffer-equal": "^1.0.0", "function-bind": "^1.0.2", "karma-coverage": "^0.5.3", "server-destroy": "^1.0.1", "karma-browserify": "^4.4.0", "browserify-istanbul": "^0.1.5", "karma-phantomjs-launcher": "^0.1.4"}, "dist": {"shasum": "35fe6a5cd184393a477c95b0725604d822fb9105", "tarball": "https://registry.npmjs.org/request/-/request-2.68.0.tgz", "integrity": "sha512-TTVSLA1b45Y3kP86SDLRuCe/22KJ6KFS4aDBZ7MvRwTl3BuDJP7viX6zaYdiA2C8NHpxA0lxDbtiZfFl1KugGw==", "signatures": [{"sig": "MEUCIE/xaGzMZJnERqGrdBE6/Y7M8m6LzWwohTUomgPhAEiJAiEA5t1K/oMxt2bsreASzdGtGwwjpTWewPrdWa9eqA1bXJk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.69.0": {"name": "request", "version": "2.69.0", "dependencies": {"bl": "~1.0.0", "qs": "~6.0.2", "aws4": "^1.2.1", "hawk": "~3.1.0", "extend": "~3.0.0", "caseless": "~0.11.0", "isstream": "~0.1.2", "aws-sign2": "~0.6.0", "form-data": "~1.0.0-rc3", "node-uuid": "~1.4.7", "mime-types": "~2.1.7", "oauth-sign": "~0.8.0", "stringstream": "~0.0.4", "tough-cookie": "~2.2.0", "tunnel-agent": "~0.4.1", "forever-agent": "~0.6.1", "har-validator": "~2.0.6", "is-typedarray": "~1.0.0", "http-signature": "~1.1.0", "combined-stream": "~1.0.5", "json-stringify-safe": "~5.0.1"}, "devDependencies": {"tape": "^4.2.0", "karma": "^0.13.10", "taper": "^0.4.0", "eslint": "1.10.3", "rimraf": "^2.2.8", "bluebird": "^3.0.2", "istanbul": "^0.4.0", "coveralls": "^2.11.4", "karma-cli": "^0.1.1", "karma-tap": "^1.0.3", "browserify": "^13.0.0", "codecov.io": "^0.1.6", "buffer-equal": "^1.0.0", "function-bind": "^1.0.2", "karma-coverage": "^0.5.3", "server-destroy": "^1.0.1", "karma-browserify": "^4.4.0", "browserify-istanbul": "^0.1.5", "karma-phantomjs-launcher": "^0.1.4"}, "dist": {"shasum": "cf91d2e000752b1217155c005241911991a2346a", "tarball": "https://registry.npmjs.org/request/-/request-2.69.0.tgz", "integrity": "sha512-WORLlE5HDoewOPTWQwArryfR0KPT0ac5Ck3VFbJ5g+mt2NoSgTLfEWQ+7Net/3QP7mMmKoJzsR28kOXO2Fx5Yw==", "signatures": [{"sig": "MEQCIBl+65hYG71htLL1ut+CbfgSZyHh5rUttKmIqFRnvouDAiBKfKgnOiGvnlUJhhVVRRIvTGSPZ4B+PervzWIStKHo2A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.70.0": {"name": "request", "version": "2.70.0", "dependencies": {"bl": "~1.1.2", "qs": "~6.1.0", "aws4": "^1.2.1", "hawk": "~3.1.3", "extend": "~3.0.0", "caseless": "~0.11.0", "isstream": "~0.1.2", "aws-sign2": "~0.6.0", "form-data": "~1.0.0-rc3", "node-uuid": "~1.4.7", "mime-types": "~2.1.7", "oauth-sign": "~0.8.1", "stringstream": "~0.0.4", "tough-cookie": "~2.2.0", "tunnel-agent": "~0.4.1", "forever-agent": "~0.6.1", "har-validator": "~2.0.6", "is-typedarray": "~1.0.0", "http-signature": "~1.1.0", "combined-stream": "~1.0.5", "json-stringify-safe": "~5.0.1"}, "devDependencies": {"tape": "^4.2.0", "karma": "^0.13.10", "taper": "^0.4.0", "eslint": "^2.5.3", "rimraf": "^2.2.8", "bluebird": "^3.2.1", "istanbul": "^0.4.0", "coveralls": "^2.11.4", "karma-cli": "^0.1.1", "karma-tap": "^1.0.3", "browserify": "^12.0.2", "codecov.io": "^0.1.6", "buffer-equal": "^1.0.0", "function-bind": "^1.0.2", "karma-coverage": "^0.5.3", "server-destroy": "^1.0.1", "karma-browserify": "^5.0.1", "phantomjs-prebuilt": "^2.1.3", "browserify-istanbul": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "7ecf8437d6fb553e92e2981a411b9ee2aadd7cce", "tarball": "https://registry.npmjs.org/request/-/request-2.70.0.tgz", "integrity": "sha512-66LXJcAM4BWOtCRVqLqlL4D4xGLSSIvnY43tES/vR5+0hj1QewsOtSVrvcoDv7OERIpWfzYVEo0g/5aZ1IwMCA==", "signatures": [{"sig": "MEUCIQC8vJuMYq2dlLbX2zxjsmPaYOtgg0LZfQUhMsLrLcZ2/AIgdkK6Sj7Gkw6nohv+72eGcLT6TRk3k1giijC3Zn6/ljU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.71.0": {"name": "request", "version": "2.71.0", "dependencies": {"bl": "~1.1.2", "qs": "~6.1.0", "aws4": "^1.2.1", "hawk": "~3.1.3", "extend": "~3.0.0", "caseless": "~0.11.0", "isstream": "~0.1.2", "aws-sign2": "~0.6.0", "form-data": "~1.0.0-rc3", "node-uuid": "~1.4.7", "mime-types": "~2.1.7", "oauth-sign": "~0.8.1", "stringstream": "~0.0.4", "tough-cookie": "~2.2.0", "tunnel-agent": "~0.4.1", "forever-agent": "~0.6.1", "har-validator": "~2.0.6", "is-typedarray": "~1.0.0", "http-signature": "~1.1.0", "combined-stream": "~1.0.5", "json-stringify-safe": "~5.0.1"}, "devDependencies": {"tape": "^4.2.0", "karma": "^0.13.10", "taper": "^0.4.0", "eslint": "^2.5.3", "rimraf": "^2.2.8", "bluebird": "^3.2.1", "istanbul": "^0.4.0", "coveralls": "^2.11.4", "karma-cli": "^0.1.1", "karma-tap": "^1.0.3", "browserify": "^12.0.2", "codecov.io": "^0.1.6", "buffer-equal": "^1.0.0", "function-bind": "^1.0.2", "karma-coverage": "^0.5.3", "server-destroy": "^1.0.1", "karma-browserify": "^5.0.1", "phantomjs-prebuilt": "^2.1.3", "browserify-istanbul": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "6f14643c9c5a67caee6a95cf8ef0477d5603bd91", "tarball": "https://registry.npmjs.org/request/-/request-2.71.0.tgz", "integrity": "sha512-6RFoZBS0is61M6kIsE311mGfEmh60P54zHaxpHo62repHs7dsaCppyX4fPcz2QssfLOgFxZ4t4arDz2PFQd7Fg==", "signatures": [{"sig": "MEQCIDBl5jVWug9CuxX1P6C0bUUDweJmK8bsIoyGk3hU0V2kAiA43te5iPQMhRDnOYUxiKy+/RSGqXr8I8/758WVXMudbw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.72.0": {"name": "request", "version": "2.72.0", "dependencies": {"bl": "~1.1.2", "qs": "~6.1.0", "aws4": "^1.2.1", "hawk": "~3.1.3", "extend": "~3.0.0", "caseless": "~0.11.0", "isstream": "~0.1.2", "aws-sign2": "~0.6.0", "form-data": "~1.0.0-rc3", "node-uuid": "~1.4.7", "mime-types": "~2.1.7", "oauth-sign": "~0.8.1", "stringstream": "~0.0.4", "tough-cookie": "~2.2.0", "tunnel-agent": "~0.4.1", "forever-agent": "~0.6.1", "har-validator": "~2.0.6", "is-typedarray": "~1.0.0", "http-signature": "~1.1.0", "combined-stream": "~1.0.5", "json-stringify-safe": "~5.0.1"}, "devDependencies": {"tape": "^4.2.0", "karma": "^0.13.10", "taper": "^0.4.0", "eslint": "^2.5.3", "rimraf": "^2.2.8", "bluebird": "^3.2.1", "istanbul": "^0.4.0", "coveralls": "^2.11.4", "karma-cli": "^0.1.1", "karma-tap": "^1.0.3", "browserify": "^12.0.2", "codecov.io": "^0.1.6", "buffer-equal": "^1.0.0", "function-bind": "^1.0.2", "karma-coverage": "^0.5.3", "server-destroy": "^1.0.1", "karma-browserify": "^5.0.1", "phantomjs-prebuilt": "^2.1.3", "browserify-istanbul": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "0ce3a179512620b10441f14c82e21c12c0ddb4e1", "tarball": "https://registry.npmjs.org/request/-/request-2.72.0.tgz", "integrity": "sha512-rQiQ3Eza3HNC+gBlzKxXaPwG1rQIcO0/7TKGIgA9D/obvFK//H+pzkCS4CctQ7aFk6LboTvyFXHMEdf8P4pSxg==", "signatures": [{"sig": "MEQCIFJBtrotdbGK73/aVLt/qqEBX0lzpBu8I6adYuqdvfiRAiBxg62LrLcDn2Onkk5UHor1ES7PnSyvT54Mxrkfp+Pn0A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.73.0": {"name": "request", "version": "2.73.0", "dependencies": {"bl": "~1.1.2", "qs": "~6.2.0", "aws4": "^1.2.1", "hawk": "~3.1.3", "extend": "~3.0.0", "caseless": "~0.11.0", "isstream": "~0.1.2", "aws-sign2": "~0.6.0", "form-data": "~1.0.0-rc4", "node-uuid": "~1.4.7", "mime-types": "~2.1.7", "oauth-sign": "~0.8.1", "stringstream": "~0.0.4", "tough-cookie": "~2.2.0", "tunnel-agent": "~0.4.1", "forever-agent": "~0.6.1", "har-validator": "~2.0.6", "is-typedarray": "~1.0.0", "http-signature": "~1.1.0", "combined-stream": "~1.0.5", "json-stringify-safe": "~5.0.1"}, "devDependencies": {"tape": "^4.6.0", "karma": "^1.1.1", "taper": "^0.4.0", "eslint": "^2.5.3", "rimraf": "^2.2.8", "codecov": "^1.0.1", "bluebird": "^3.2.1", "istanbul": "^0.4.0", "coveralls": "^2.11.4", "karma-cli": "^1.0.0", "karma-tap": "^1.0.3", "browserify": "^13.0.1", "buffer-equal": "^1.0.0", "function-bind": "^1.0.2", "karma-coverage": "^1.0.0", "server-destroy": "^1.0.1", "karma-browserify": "^5.0.1", "phantomjs-prebuilt": "^2.1.3", "browserify-istanbul": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "5f78a9fde4370abc8ff6479d7a84a71a14b878a2", "tarball": "https://registry.npmjs.org/request/-/request-2.73.0.tgz", "integrity": "sha512-FMowpvTkMn1jqYkIB+0fGbtlWgGGuV6WFOUBjWCSuhLKhRY29BbvY0ls6w1Ql0vqsbtjbs1XWsgw+pI40YfQhg==", "signatures": [{"sig": "MEUCICAJPu2n8AHfC0BArJgEDnCWwQaWYphPq60LJmo4VpxIAiEA4/bPSaDqx0Yx/pRuGgabnekMNcprz+YPS0EEZH5Ml8c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.74.0": {"name": "request", "version": "2.74.0", "dependencies": {"bl": "~1.1.2", "qs": "~6.2.0", "aws4": "^1.2.1", "hawk": "~3.1.3", "extend": "~3.0.0", "caseless": "~0.11.0", "isstream": "~0.1.2", "aws-sign2": "~0.6.0", "form-data": "~1.0.0-rc4", "node-uuid": "~1.4.7", "mime-types": "~2.1.7", "oauth-sign": "~0.8.1", "stringstream": "~0.0.4", "tough-cookie": "~2.3.0", "tunnel-agent": "~0.4.1", "forever-agent": "~0.6.1", "har-validator": "~2.0.6", "is-typedarray": "~1.0.0", "http-signature": "~1.1.0", "combined-stream": "~1.0.5", "json-stringify-safe": "~5.0.1"}, "devDependencies": {"tape": "^4.6.0", "karma": "^1.1.1", "taper": "^0.4.0", "eslint": "^2.5.3", "rimraf": "^2.2.8", "codecov": "^1.0.1", "bluebird": "^3.2.1", "istanbul": "^0.4.0", "coveralls": "^2.11.4", "karma-cli": "^1.0.0", "karma-tap": "^2.0.1", "browserify": "^13.0.1", "buffer-equal": "^1.0.0", "function-bind": "^1.0.2", "karma-coverage": "^1.0.0", "server-destroy": "^1.0.1", "karma-browserify": "^5.0.1", "phantomjs-prebuilt": "^2.1.3", "browserify-istanbul": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "7693ca768bbb0ea5c8ce08c084a45efa05b892ab", "tarball": "https://registry.npmjs.org/request/-/request-2.74.0.tgz", "integrity": "sha512-m3uMovC42y63jXe/Sr49/qJdqpSYwQAgYIc487l0zSXI6Z6f5cV/V4a86h2Z+AAwKpt5bfB66KrZxOfOSdh6FQ==", "signatures": [{"sig": "MEUCIAD5F2TueVV9mdv+d9mIzs7ABfFrO361U+OhAvyV66TaAiEAnkjYqaMrIS/OSMHtvb2K0J5JH79TqHDwukVBhzcshHo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.75.0": {"name": "request", "version": "2.75.0", "dependencies": {"bl": "~1.1.2", "qs": "~6.2.0", "aws4": "^1.2.1", "hawk": "~3.1.3", "extend": "~3.0.0", "caseless": "~0.11.0", "isstream": "~0.1.2", "aws-sign2": "~0.6.0", "form-data": "~2.0.0", "node-uuid": "~1.4.7", "mime-types": "~2.1.7", "oauth-sign": "~0.8.1", "stringstream": "~0.0.4", "tough-cookie": "~2.3.0", "tunnel-agent": "~0.4.1", "forever-agent": "~0.6.1", "har-validator": "~2.0.6", "is-typedarray": "~1.0.0", "http-signature": "~1.1.0", "combined-stream": "~1.0.5", "json-stringify-safe": "~5.0.1"}, "devDependencies": {"tape": "^4.6.0", "karma": "^1.1.1", "taper": "^0.4.0", "eslint": "^2.5.3", "rimraf": "^2.2.8", "codecov": "^1.0.1", "bluebird": "^3.2.1", "istanbul": "^0.4.0", "coveralls": "^2.11.4", "karma-cli": "^1.0.0", "karma-tap": "^3.0.1", "browserify": "^13.0.1", "buffer-equal": "^1.0.0", "function-bind": "^1.0.2", "karma-coverage": "^1.0.0", "server-destroy": "^1.0.1", "karma-browserify": "^5.0.1", "phantomjs-prebuilt": "^2.1.3", "browserify-istanbul": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "d2b8268a286da13eaa5d01adf5d18cc90f657d93", "tarball": "https://registry.npmjs.org/request/-/request-2.75.0.tgz", "integrity": "sha512-uNXre8CefDRFBhfB1bL0CkKBD+5E1xmx69KMjl7p+bBc0vesXLQMS+iwsI2pKRlYZOOtLzkeBfz7jItKA3XlKQ==", "signatures": [{"sig": "MEUCIAOVp1foZ9NbQyF1nHbUh8QstpfK5/ePxqQID2Zg/7fFAiEA6qlt5HOsxOJijfTvpXZgUJF5/6h/t1udulbPF++f06k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.76.0": {"name": "request", "version": "2.76.0", "dependencies": {"qs": "~6.3.0", "aws4": "^1.2.1", "hawk": "~3.1.3", "extend": "~3.0.0", "caseless": "~0.11.0", "isstream": "~0.1.2", "aws-sign2": "~0.6.0", "form-data": "~2.1.1", "node-uuid": "~1.4.7", "mime-types": "~2.1.7", "oauth-sign": "~0.8.1", "stringstream": "~0.0.4", "tough-cookie": "~2.3.0", "tunnel-agent": "~0.4.1", "forever-agent": "~0.6.1", "har-validator": "~2.0.6", "is-typedarray": "~1.0.0", "http-signature": "~1.1.0", "combined-stream": "~1.0.5", "json-stringify-safe": "~5.0.1"}, "devDependencies": {"tape": "^4.6.0", "karma": "^1.1.1", "taper": "^0.4.0", "eslint": "^2.5.3", "rimraf": "^2.2.8", "codecov": "^1.0.1", "bluebird": "^3.2.1", "istanbul": "^0.4.0", "coveralls": "^2.11.4", "karma-cli": "^1.0.0", "karma-tap": "^3.0.1", "browserify": "^13.0.1", "buffer-equal": "^1.0.0", "function-bind": "^1.0.2", "karma-coverage": "^1.0.0", "server-destroy": "^1.0.1", "karma-browserify": "^5.0.1", "phantomjs-prebuilt": "^2.1.3", "browserify-istanbul": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "be44505afef70360a0436955106be3945d95560e", "tarball": "https://registry.npmjs.org/request/-/request-2.76.0.tgz", "integrity": "sha512-oAqKWlLKtLtHlLa/dxjQ0Q03rXvGeNd4Kdj63cIbxmi2VulqWTuccD5V8a7GtI3QjtJ9294dltpp4PMBokIsww==", "signatures": [{"sig": "MEYCIQCKaJRpo4O7ZHB0AHkfXYRE2gt9GJYR/zVuwGn08VZiEgIhAOtO3otFfDBKx3gmK9gjVh2Jz1+BuRRNaYhZGU28QTdA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.77.0": {"name": "request", "version": "2.77.0", "dependencies": {"qs": "~6.3.0", "aws4": "^1.2.1", "hawk": "~3.1.3", "extend": "~3.0.0", "caseless": "~0.11.0", "isstream": "~0.1.2", "aws-sign2": "~0.6.0", "form-data": "~2.1.1", "node-uuid": "~1.4.7", "mime-types": "~2.1.7", "oauth-sign": "~0.8.1", "stringstream": "~0.0.4", "tough-cookie": "~2.3.0", "tunnel-agent": "~0.4.1", "forever-agent": "~0.6.1", "har-validator": "~2.0.6", "is-typedarray": "~1.0.0", "http-signature": "~1.1.0", "combined-stream": "~1.0.5", "json-stringify-safe": "~5.0.1"}, "devDependencies": {"tape": "^4.6.0", "karma": "^1.1.1", "taper": "^0.4.0", "eslint": "^2.5.3", "rimraf": "^2.2.8", "codecov": "^1.0.1", "bluebird": "^3.2.1", "istanbul": "^0.4.0", "coveralls": "^2.11.4", "karma-cli": "^1.0.0", "karma-tap": "^3.0.1", "browserify": "^13.0.1", "buffer-equal": "^1.0.0", "function-bind": "^1.0.2", "karma-coverage": "^1.0.0", "server-destroy": "^1.0.1", "karma-browserify": "^5.0.1", "phantomjs-prebuilt": "^2.1.3", "browserify-istanbul": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "2b00d82030ededcc97089ffa5d8810a9c2aa314b", "tarball": "https://registry.npmjs.org/request/-/request-2.77.0.tgz", "integrity": "sha512-7mwEakHB+//oEb7f8cvfI4/5w10rp5DTcHYrGMzyqGMlIqwCsjFIKw3kTZTpH44y4SbeWJEqjhlawy31bpuuBg==", "signatures": [{"sig": "MEUCIQCW3p++p+3MylLd5pvTGZpS21pSU8mLcfDYfn+1OfKITwIgfg733NHQ9F3JUj3/AD8XodPxyVq5Df7bO3cSiLMaWfs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 4"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.78.0": {"name": "request", "version": "2.78.0", "dependencies": {"qs": "~6.3.0", "aws4": "^1.2.1", "hawk": "~3.1.3", "extend": "~3.0.0", "caseless": "~0.11.0", "isstream": "~0.1.2", "aws-sign2": "~0.6.0", "form-data": "~2.1.1", "node-uuid": "~1.4.7", "mime-types": "~2.1.7", "oauth-sign": "~0.8.1", "stringstream": "~0.0.4", "tough-cookie": "~2.3.0", "tunnel-agent": "~0.4.1", "forever-agent": "~0.6.1", "har-validator": "~2.0.6", "is-typedarray": "~1.0.0", "http-signature": "~1.1.0", "combined-stream": "~1.0.5", "json-stringify-safe": "~5.0.1"}, "devDependencies": {"tape": "^4.6.0", "karma": "^1.1.1", "taper": "^0.4.0", "eslint": "^2.5.3", "rimraf": "^2.2.8", "codecov": "^1.0.1", "bluebird": "^3.2.1", "istanbul": "^0.4.0", "coveralls": "^2.11.4", "karma-cli": "^1.0.0", "karma-tap": "^3.0.1", "browserify": "^13.0.1", "buffer-equal": "^1.0.0", "function-bind": "^1.0.2", "karma-coverage": "^1.0.0", "server-destroy": "^1.0.1", "karma-browserify": "^5.0.1", "phantomjs-prebuilt": "^2.1.3", "browserify-istanbul": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "e1c8dec346e1c81923b24acdb337f11decabe9cc", "tarball": "https://registry.npmjs.org/request/-/request-2.78.0.tgz", "integrity": "sha512-ui0hctTyP0uRKj6TYbl9t5+JJ8heIIuzsEJtKpfzSlWWEVKG9zisEs3djAbw6UXXR3vKNA5k9MnN0IpsEWrksA==", "signatures": [{"sig": "MEYCIQCwk6ToVJcRGUF+yvW2SfGENyEQzRJduAJSoah+jYef8QIhAMpWw576jBMOl6KpAJd7RtMglADP9KJaY2xGhy/JpME6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 4"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.79.0": {"name": "request", "version": "2.79.0", "dependencies": {"qs": "~6.3.0", "aws4": "^1.2.1", "hawk": "~3.1.3", "uuid": "^3.0.0", "extend": "~3.0.0", "caseless": "~0.11.0", "isstream": "~0.1.2", "aws-sign2": "~0.6.0", "form-data": "~2.1.1", "mime-types": "~2.1.7", "oauth-sign": "~0.8.1", "stringstream": "~0.0.4", "tough-cookie": "~2.3.0", "tunnel-agent": "~0.4.1", "forever-agent": "~0.6.1", "har-validator": "~2.0.6", "is-typedarray": "~1.0.0", "http-signature": "~1.1.0", "combined-stream": "~1.0.5", "json-stringify-safe": "~5.0.1"}, "devDependencies": {"tape": "^4.6.0", "karma": "^1.1.1", "taper": "^0.5.0", "eslint": "^2.5.3", "rimraf": "^2.2.8", "codecov": "^1.0.1", "bluebird": "^3.2.1", "istanbul": "^0.4.0", "coveralls": "^2.11.4", "karma-cli": "^1.0.0", "karma-tap": "^3.0.1", "browserify": "^13.0.1", "buffer-equal": "^1.0.0", "function-bind": "^1.0.2", "karma-coverage": "^1.0.0", "server-destroy": "^1.0.1", "karma-browserify": "^5.0.1", "phantomjs-prebuilt": "^2.1.3", "browserify-istanbul": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "4dfe5bf6be8b8cdc37fcf93e04b65577722710de", "tarball": "https://registry.npmjs.org/request/-/request-2.79.0.tgz", "integrity": "sha512-e7MIJshe1eZAmRqg4ryaO0N9G0fs+/gpDe5FlbnIFy6zZznRSwdRFrLp63if0Yt43vrI5wowOqHv1qJdVocdOQ==", "signatures": [{"sig": "MEUCIFP15AFZnuzE88zO9aWw4GfHwYBHXHPzQ2nSRT+ION0jAiEA+uTbuNuwGhThhWnPdk6tTsMjMjHC5QwYqpsmoYG+gHM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 4"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.80.0": {"name": "request", "version": "2.80.0", "dependencies": {"qs": "~6.3.0", "aws4": "^1.2.1", "hawk": "~3.1.3", "uuid": "^3.0.0", "extend": "~3.0.0", "caseless": "~0.12.0", "isstream": "~0.1.2", "aws-sign2": "~0.6.0", "form-data": "~2.1.1", "mime-types": "~2.1.7", "oauth-sign": "~0.8.1", "stringstream": "~0.0.4", "tough-cookie": "~2.3.0", "tunnel-agent": "~0.4.1", "forever-agent": "~0.6.1", "har-validator": "~4.2.0", "is-typedarray": "~1.0.0", "http-signature": "~1.1.0", "combined-stream": "~1.0.5", "performance-now": "^0.2.0", "json-stringify-safe": "~5.0.1"}, "devDependencies": {"tape": "^4.6.0", "karma": "^1.1.1", "taper": "^0.5.0", "eslint": "^2.5.3", "rimraf": "^2.2.8", "codecov": "^1.0.1", "bluebird": "^3.2.1", "istanbul": "^0.4.0", "coveralls": "^2.11.4", "karma-cli": "^1.0.0", "karma-tap": "^3.0.1", "browserify": "^13.0.1", "buffer-equal": "^1.0.0", "function-bind": "^1.0.2", "karma-coverage": "^1.0.0", "server-destroy": "^1.0.1", "karma-browserify": "^5.0.1", "phantomjs-prebuilt": "^2.1.3", "browserify-istanbul": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "8cc162d76d79381cdefdd3505d76b80b60589bd0", "tarball": "https://registry.npmjs.org/request/-/request-2.80.0.tgz", "integrity": "sha512-js3h8XcQ/6zNNyhnnbpF6RVZixLxIkJUcMHr3k/t7NkBW4NOo2/k5olseL8gCtIcqaugPGsiGpJvsc5OQN3Xrw==", "signatures": [{"sig": "MEQCIAqLrhk26Yoajf22Sqc0GF7zPu6fdjTXQhAEk+cGYp83AiAvGU/EPC+8tNVWzLYbQ9sybQT3HVjDkITtraRQ96n8nw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 4"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.81.0": {"name": "request", "version": "2.81.0", "dependencies": {"qs": "~6.4.0", "aws4": "^1.2.1", "hawk": "~3.1.3", "uuid": "^3.0.0", "extend": "~3.0.0", "caseless": "~0.12.0", "isstream": "~0.1.2", "aws-sign2": "~0.6.0", "form-data": "~2.1.1", "mime-types": "~2.1.7", "oauth-sign": "~0.8.1", "safe-buffer": "^5.0.1", "stringstream": "~0.0.4", "tough-cookie": "~2.3.0", "tunnel-agent": "^0.6.0", "forever-agent": "~0.6.1", "har-validator": "~4.2.1", "is-typedarray": "~1.0.0", "http-signature": "~1.1.0", "combined-stream": "~1.0.5", "performance-now": "^0.2.0", "json-stringify-safe": "~5.0.1"}, "devDependencies": {"tape": "^4.6.0", "karma": "^1.1.1", "taper": "^0.5.0", "eslint": "^2.5.3", "rimraf": "^2.2.8", "codecov": "^1.0.1", "bluebird": "^3.2.1", "istanbul": "^0.4.0", "coveralls": "^2.11.4", "karma-cli": "^1.0.0", "karma-tap": "^3.0.1", "browserify": "^13.0.1", "buffer-equal": "^1.0.0", "function-bind": "^1.0.2", "karma-coverage": "^1.0.0", "server-destroy": "^1.0.1", "karma-browserify": "^5.0.1", "phantomjs-prebuilt": "^2.1.3", "browserify-istanbul": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "c6928946a0e06c5f8d6f8a9333469ffda46298a0", "tarball": "https://registry.npmjs.org/request/-/request-2.81.0.tgz", "integrity": "sha512-IZnsR7voF0miGSu29EXPRgPTuEsI/+aibNSBbN1pplrfartF5wDYGADz3iD9vmBVf2r00rckWZf8BtS5kk7Niw==", "signatures": [{"sig": "MEYCIQCI7lFyGWcxdAw6GBweX8bP1MIRTtv0+M3nASmonLUxaQIhAKSOn6XZN3DI/eAjMnSyJ5ciSgxav/1poUkoqs4nJBne", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 4"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.82.0": {"name": "request", "version": "2.82.0", "dependencies": {"qs": "~6.5.1", "aws4": "^1.6.0", "hawk": "~6.0.2", "uuid": "^3.1.0", "extend": "~3.0.1", "caseless": "~0.12.0", "isstream": "~0.1.2", "aws-sign2": "~0.7.0", "form-data": "~2.3.1", "mime-types": "~2.1.17", "oauth-sign": "~0.8.2", "safe-buffer": "^5.1.1", "stringstream": "~0.0.5", "tough-cookie": "~2.3.2", "tunnel-agent": "^0.6.0", "forever-agent": "~0.6.1", "har-validator": "~5.0.3", "is-typedarray": "~1.0.0", "http-signature": "~1.2.0", "combined-stream": "~1.0.5", "performance-now": "^2.1.0", "json-stringify-safe": "~5.0.1"}, "devDependencies": {"tape": "^4.6.0", "karma": "^1.1.1", "taper": "^0.5.0", "rimraf": "^2.2.8", "codecov": "^2.0.2", "bluebird": "^3.2.1", "istanbul": "^0.4.0", "standard": "^9.0.0", "coveralls": "^2.11.4", "karma-cli": "^1.0.0", "karma-tap": "^3.0.1", "browserify": "^13.0.1", "buffer-equal": "^1.0.0", "function-bind": "^1.0.2", "karma-coverage": "^1.0.0", "server-destroy": "^1.0.1", "karma-browserify": "^5.0.1", "phantomjs-prebuilt": "^2.1.3", "browserify-istanbul": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "2ba8a92cd7ac45660ea2b10a53ae67cd247516ea", "tarball": "https://registry.npmjs.org/request/-/request-2.82.0.tgz", "integrity": "sha512-/QWqfmyTfQ4OYs6EhB1h2wQsX9ZxbuNePCvCm0Mdz/mxw73mjdg0D4QdIl0TQBFs35CZmMXLjk0iCGK395CUDg==", "signatures": [{"sig": "MEUCIBCBGWaR5ZJthc6zq3d8eX7Ree/ESJxmlvH7angRD/2MAiEAywLfTIGIPjEeFfGt75rm2G+Hkj1GfYRB2+FDz8Ge6fg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 4"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.83.0": {"name": "request", "version": "2.83.0", "dependencies": {"qs": "~6.5.1", "aws4": "^1.6.0", "hawk": "~6.0.2", "uuid": "^3.1.0", "extend": "~3.0.1", "caseless": "~0.12.0", "isstream": "~0.1.2", "aws-sign2": "~0.7.0", "form-data": "~2.3.1", "mime-types": "~2.1.17", "oauth-sign": "~0.8.2", "safe-buffer": "^5.1.1", "stringstream": "~0.0.5", "tough-cookie": "~2.3.3", "tunnel-agent": "^0.6.0", "forever-agent": "~0.6.1", "har-validator": "~5.0.3", "is-typedarray": "~1.0.0", "http-signature": "~1.2.0", "combined-stream": "~1.0.5", "performance-now": "^2.1.0", "json-stringify-safe": "~5.0.1"}, "devDependencies": {"tape": "^4.6.0", "karma": "^1.1.1", "taper": "^0.5.0", "rimraf": "^2.2.8", "codecov": "^2.0.2", "bluebird": "^3.2.1", "istanbul": "^0.4.0", "standard": "^9.0.0", "coveralls": "^2.11.4", "karma-cli": "^1.0.0", "karma-tap": "^3.0.1", "browserify": "^13.0.1", "buffer-equal": "^1.0.0", "function-bind": "^1.0.2", "karma-coverage": "^1.0.0", "server-destroy": "^1.0.1", "karma-browserify": "^5.0.1", "phantomjs-prebuilt": "^2.1.3", "browserify-istanbul": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "ca0b65da02ed62935887808e6f510381034e3356", "tarball": "https://registry.npmjs.org/request/-/request-2.83.0.tgz", "integrity": "sha512-lR3gD69osqm6EYLk9wB/G1W/laGWjzH90t1vEa2xuxHD5KUrSzp9pUSfTm+YC5Nxt2T8nMPEvKlhbQayU7bgFw==", "signatures": [{"sig": "MEUCIQDCyesl0W5YuP4xMmWDYDzPEtqUtoMQFGg2kd7/GUYdDwIgUjA1YL2edjFiCC1y6srmYOrS00TgxL4eG4N98yyhzQg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 4"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.84.0": {"name": "request", "version": "2.84.0", "dependencies": {"qs": "~6.5.1", "aws4": "^1.6.0", "hawk": "~7.0.7", "uuid": "^3.1.0", "extend": "~3.0.1", "caseless": "~0.12.0", "isstream": "~0.1.2", "aws-sign2": "~0.7.0", "form-data": "~2.3.1", "mime-types": "~2.1.17", "oauth-sign": "~0.8.2", "safe-buffer": "^5.1.1", "stringstream": "~0.0.5", "tough-cookie": "~2.3.3", "tunnel-agent": "^0.6.0", "forever-agent": "~0.6.1", "har-validator": "~5.0.3", "is-typedarray": "~1.0.0", "http-signature": "~1.2.0", "combined-stream": "~1.0.5", "performance-now": "^2.1.0", "json-stringify-safe": "~5.0.1"}, "devDependencies": {"tape": "^4.6.0", "karma": "^1.1.1", "taper": "^0.5.0", "rimraf": "^2.2.8", "codecov": "^2.0.2", "bluebird": "^3.2.1", "istanbul": "^0.4.0", "standard": "^9.0.0", "coveralls": "^2.11.4", "karma-cli": "^1.0.0", "karma-tap": "^3.0.1", "browserify": "^13.0.1", "buffer-equal": "^1.0.0", "function-bind": "^1.0.2", "karma-coverage": "^1.0.0", "server-destroy": "^1.0.1", "karma-browserify": "^5.0.1", "phantomjs-prebuilt": "^2.1.3", "browserify-istanbul": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "59ca54a18663d7986aa37dbe4632aa8ada3a467f", "tarball": "https://registry.npmjs.org/request/-/request-2.84.0.tgz", "fileCount": 16, "integrity": "sha512-+arBW9+9rg/X6TeMxseqWVdPF1AT3KQ7cEEC4mmkdrbC6pQ6m7+iKzfU6vZ21cBD0MbygN2sn15PzwlLkZ2xpw==", "signatures": [{"sig": "MEYCIQD9mXUVwxqnxyf0zrQSorsAOFkTBNrCO59JxGacwkHEzwIhANqc4hWxvqB+5EMkcR/5zrlJXYgwEWG1N7yZje8H+ZXR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 203664}, "engines": {"node": ">= 4"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.85.0": {"name": "request", "version": "2.85.0", "dependencies": {"qs": "~6.5.1", "aws4": "^1.6.0", "hawk": "~6.0.2", "uuid": "^3.1.0", "extend": "~3.0.1", "caseless": "~0.12.0", "isstream": "~0.1.2", "aws-sign2": "~0.7.0", "form-data": "~2.3.1", "mime-types": "~2.1.17", "oauth-sign": "~0.8.2", "safe-buffer": "^5.1.1", "stringstream": "~0.0.5", "tough-cookie": "~2.3.3", "tunnel-agent": "^0.6.0", "forever-agent": "~0.6.1", "har-validator": "~5.0.3", "is-typedarray": "~1.0.0", "http-signature": "~1.2.0", "combined-stream": "~1.0.5", "performance-now": "^2.1.0", "json-stringify-safe": "~5.0.1"}, "devDependencies": {"tape": "^4.6.0", "karma": "^1.1.1", "taper": "^0.5.0", "rimraf": "^2.2.8", "codecov": "^2.0.2", "bluebird": "^3.2.1", "istanbul": "^0.4.0", "standard": "^9.0.0", "coveralls": "^2.11.4", "karma-cli": "^1.0.0", "karma-tap": "^3.0.1", "browserify": "^13.0.1", "buffer-equal": "^1.0.0", "function-bind": "^1.0.2", "karma-coverage": "^1.0.0", "server-destroy": "^1.0.1", "karma-browserify": "^5.0.1", "phantomjs-prebuilt": "^2.1.3", "browserify-istanbul": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "5a03615a47c61420b3eb99b7dba204f83603e1fa", "tarball": "https://registry.npmjs.org/request/-/request-2.85.0.tgz", "fileCount": 16, "integrity": "sha512-8H7Ehijd4js+s6wuVPLjwORxD4zeuyjYugprdOXlPSqaApmL/QOy+EB/beICHVCHkGMKNh5rvihb5ov+IDw4mg==", "signatures": [{"sig": "MEUCIQCZhZNpjAiXiiWCUQ9uwM0TsXsKoEVMccPkdq4mVhGyygIgJXPdnb8zz1oyi39aGDJ/K/9eTXbzw6QwYXuwQoCp1Lc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 203792}, "engines": {"node": ">= 4"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.86.0": {"name": "request", "version": "2.86.0", "dependencies": {"qs": "~6.5.1", "aws4": "^1.6.0", "hawk": "~6.0.2", "uuid": "^3.1.0", "extend": "~3.0.1", "caseless": "~0.12.0", "isstream": "~0.1.2", "aws-sign2": "~0.7.0", "form-data": "~2.3.1", "mime-types": "~2.1.17", "oauth-sign": "~0.8.2", "safe-buffer": "^5.1.1", "tough-cookie": "~2.3.3", "tunnel-agent": "^0.6.0", "forever-agent": "~0.6.1", "har-validator": "~5.0.3", "is-typedarray": "~1.0.0", "http-signature": "~1.2.0", "combined-stream": "~1.0.5", "performance-now": "^2.1.0", "json-stringify-safe": "~5.0.1"}, "devDependencies": {"tape": "^4.6.0", "karma": "^1.1.1", "taper": "^0.5.0", "rimraf": "^2.2.8", "codecov": "^2.0.2", "bluebird": "^3.2.1", "istanbul": "^0.4.0", "standard": "^9.0.0", "coveralls": "^2.11.4", "karma-cli": "^1.0.0", "karma-tap": "^3.0.1", "browserify": "^13.0.1", "buffer-equal": "^1.0.0", "function-bind": "^1.0.2", "karma-coverage": "^1.0.0", "server-destroy": "^1.0.1", "karma-browserify": "^5.0.1", "phantomjs-prebuilt": "^2.1.3", "browserify-istanbul": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "2b9497f449b0a32654c081a5cf426bbfb5bf5b69", "tarball": "https://registry.npmjs.org/request/-/request-2.86.0.tgz", "fileCount": 16, "integrity": "sha512-BQZih67o9r+Ys94tcIW4S7Uu8pthjrQVxhsZ/weOwHbDfACxvIyvnAbzFQxjy1jMtvFSzv5zf4my6cZsJBbVzw==", "signatures": [{"sig": "MEQCIHUguJal/cBLZgg4aMw0e3Xw5qLgvrLWDmEQhw0asL99AiBuNFDzzd67m2C81oPJ/ZWnbMalKWLJEKkpnX1CZHc/Qw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 203776, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+s08CRA9TVsSAnZWagAAbbkP/3c/1i/9l1U9DZKqfTL1\nv0mY8Dlvq/wuz8+4+IJiSTbenEK2SKuqbs1WxfLd8waekoaOWfHchodvFNQk\n2kpRvx9xq8GxNKZHty+VerGMGz9BDRl8LdeaenXBaChTCtFOc38xTf4ALwGJ\nZchyVJwij1aP9gHkY1oBsJdwh4VDrPNh3YT1IS90ExhaJ0GZI8Tx5t5i7xj6\npHwHcXh427BKyzOOe79YvyhVsORrQ+5RXYhzBiI+sLtKAsEhyi9Uu2D98YZw\nGJo4BFRltxooP1Zn3CDj+7sQZnQHK3Cr1gYS301RScVB54BMWvB+z1DHt/89\n8mOxrBMUa5/XmyD1sHwsgn7uiDMONx1ZtqOWy9q9Q9El+9pOlRKVZGbagnC/\nKKpFciZgcWbbKIC+MLjXlkREFTLr14NNd1a4C8tbBNucR3RMpPkPfUY0K86N\n0/QgEkYt1+Y+X0zdHtrah9SaOXTnDY3OQDqE8lY35zflf3iyeRjY1JvFZBH0\nZu/elUyGc3Uq7Kx1+mE3LJwbyIqiUbheH0yb8qPrwUjqbXORP09QOJiX5Z7b\naJTZV/L0qi7nt19W79wazRX9fbbJPmaToBCmWv9ghhcFXIKbmfmkRX+ntudb\n8EbEY0yF1Zl/Y3XAaHKG+tE3NkHeIIRjk8SGs2Vz+sU5JifMLoNNta43GkQN\neNGu\r\n=2QBw\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 4"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.87.0": {"name": "request", "version": "2.87.0", "dependencies": {"qs": "~6.5.1", "aws4": "^1.6.0", "uuid": "^3.1.0", "extend": "~3.0.1", "caseless": "~0.12.0", "isstream": "~0.1.2", "aws-sign2": "~0.7.0", "form-data": "~2.3.1", "mime-types": "~2.1.17", "oauth-sign": "~0.8.2", "safe-buffer": "^5.1.1", "tough-cookie": "~2.3.3", "tunnel-agent": "^0.6.0", "forever-agent": "~0.6.1", "har-validator": "~5.0.3", "is-typedarray": "~1.0.0", "http-signature": "~1.2.0", "combined-stream": "~1.0.5", "performance-now": "^2.1.0", "json-stringify-safe": "~5.0.1"}, "devDependencies": {"tape": "^4.6.0", "karma": "^1.1.1", "taper": "^0.5.0", "rimraf": "^2.2.8", "codecov": "^2.0.2", "bluebird": "^3.2.1", "istanbul": "^0.4.0", "standard": "^9.0.0", "coveralls": "^2.11.4", "karma-cli": "^1.0.0", "karma-tap": "^3.0.1", "browserify": "^13.0.1", "buffer-equal": "^1.0.0", "function-bind": "^1.0.2", "karma-coverage": "^1.0.0", "server-destroy": "^1.0.1", "karma-browserify": "^5.0.1", "phantomjs-prebuilt": "^2.1.3", "browserify-istanbul": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "32f00235cd08d482b4d0d68db93a829c0ed5756e", "tarball": "https://registry.npmjs.org/request/-/request-2.87.0.tgz", "fileCount": 17, "integrity": "sha512-fcogkm7Az5bsS6Sl0sibkbhcKsnyon/jV1kF3ajGmF0c8HrttdKTPRT9hieOaQHA5HEq6r8OyWOo/o781C1tNw==", "signatures": [{"sig": "MEQCIByUW0qUVouvpNZ5ek8rVDna2dO1N4Xy1GvU7BM69g59AiBuEs6tld7FIQJo8spzlrqcp7q067w7+zqa8wE6RhRn7Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 206652, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbAnapCRA9TVsSAnZWagAAn44P/3RyMkTB2JOuitF4Ar5t\nkc2Ma4HuCxaiFpI/2sXHN/kzYm1Vsv0W4PGeyIxPqnKu99zbUGdVPi27lfDU\nmWADUxX0ujUH5L/BoivUi/sMGvroKGMrwhO2DZlVHdfl6y6dMLyqQ7XLT7EH\nu5VJCGbmBnWfrL3ZjvHoIXOW7byAXHXoNBD85cX3s4J+wOQ7ZiWoCjfM9/9G\nSbQPYO+Ji5UNT6706DTh+doBuI9fXsdugh/9gPMGvpm9leYQ2r8KRAisuENj\nZvYwBcTq8PPtdHvwT6dKIe/HYm6IwI+f3U7rVcHjHvwb4a9RmU5jPmIqwM5v\nEj5GqFq18aoRsQkv0ktOnGBMBoklx867F2rdQZjXd+GrMzbWBAQjYpFui/pK\nlWLRz5G4FL8O7wYbGHgXYOwfkP1am5guF6FOvfq2i5ajK6gIz5JG9Ln0RUYk\nKVqfMGYWbKvt1UQCaAHqjfyq1TvMS1rrouQeJtmhbf3RqAXecw1LbZsA6SxT\n+EmP28zEcmJuuo77v6y06g4XZZ1snmGnlEx4NfweGuMtRJmTGo/mUO7aGLmx\nK2oNJGLf2TJpTPSnmysglUirr5D0RZ/mmvfmrWqxWsD+Jyngks0gD47BjCcZ\nrmUcaFslqAcNV2iY2mASr8Dts/vZfchcUSxCkHhAN7As4EsBv07ziDVlS2AR\n/gk2\r\n=6VhQ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 4"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.88.0": {"name": "request", "version": "2.88.0", "dependencies": {"qs": "~6.5.2", "aws4": "^1.8.0", "uuid": "^3.3.2", "extend": "~3.0.2", "caseless": "~0.12.0", "isstream": "~0.1.2", "aws-sign2": "~0.7.0", "form-data": "~2.3.2", "mime-types": "~2.1.19", "oauth-sign": "~0.9.0", "safe-buffer": "^5.1.2", "tough-cookie": "~2.4.3", "tunnel-agent": "^0.6.0", "forever-agent": "~0.6.1", "har-validator": "~5.1.0", "is-typedarray": "~1.0.0", "http-signature": "~1.2.0", "combined-stream": "~1.0.6", "performance-now": "^2.1.0", "json-stringify-safe": "~5.0.1"}, "devDependencies": {"tape": "^4.6.0", "karma": "^3.0.0", "taper": "^0.5.0", "rimraf": "^2.2.8", "codecov": "^3.0.4", "bluebird": "^3.2.1", "istanbul": "^0.4.0", "standard": "^9.0.0", "coveralls": "^3.0.2", "karma-cli": "^1.0.0", "karma-tap": "^3.0.1", "browserify": "^13.0.1", "buffer-equal": "^1.0.0", "function-bind": "^1.0.2", "karma-coverage": "^1.0.0", "server-destroy": "^1.0.1", "karma-browserify": "^5.0.1", "phantomjs-prebuilt": "^2.1.3", "browserify-istanbul": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "9c2fca4f7d35b592efe57c7f0a55e81052124fef", "tarball": "https://registry.npmjs.org/request/-/request-2.88.0.tgz", "fileCount": 17, "integrity": "sha512-NAqBSrijGLZdM0WZNsInLJpkJokL72XYjUpnB0iwsRgxh7dB6COrHnTBNwN0E+lHDAJzu7kLAkDeY08z2/A0hg==", "signatures": [{"sig": "MEYCIQCHVFoYUkIMyk75VggkcxvjasHT4hGrHsQ6/nPzknNjjwIhAMZ5luEIDrX4IKaZrB3mJoAy8rdu/3ofu7tjHQAY/qE4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 206894, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbbzLCRA9TVsSAnZWagAAhRcP/0ha56tDuCS2+dV<PERSON><PERSON><PERSON>\ngQLIBd2yWuZImcDNxU6k30tNhKWfzxannP+vsAFV+qRvP2pUnE7hG6Zx8OLL\nm1kck/YWtBuSCrhYb402bTSKRUCbhWdW0+ytYfDsgDkskKtY85SFtr3hauOm\nmeBkpSLiwu2EosMzjxY8jntyYPDOIK9tXTYe/Lm6ZMx40CGZo5mlS4mktfXF\n/HVfP+zjp4Gr/wBqfArJmuBKWozuKxorsHbjuRvcaQ6hDPW47mhbDFaPhWo0\n4/3u1w9enypd4LvHpVCcbKM3OdYGIcWCiy7YvJSjZHkNAR1G7yhUnLh3jQBh\nS5j4Iuif3ph6IO6DwCHFvkXzHYM5lasdJT0nXtJiF6+XUJIQtpsIDs6B3jwR\naxTE8u1BqWFq4AyHQ6xzGro/Wrn7toDuqWlsKbBeX7qFFrCYz3rYTWrM4ghH\nB57PgOQsPPn5y2Y8gRPFdQJzEs38xdFaZNWwrQUr+ZDA/yM3My9SEWr3ZcOc\ndBsYb6AH4GMHinFM6ktHpmQB7AwY+lKzY3BU3NzvN0ZwuHtg8YtdEF+NMXhG\n3NnykA8h88Oy5P4m7emNm0DJj3J1lmMd26SQybPrXta1gBFPtuvyXpM7K6HM\nQzfagTktQNzxCT8pE8voz9z2eDnZMbXkSx2RT7ejZbHdkphTfw4riRQ97hpF\niSWD\r\n=3tmo\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 4"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}, "2.88.2": {"name": "request", "version": "2.88.2", "dependencies": {"qs": "~6.5.2", "aws4": "^1.8.0", "uuid": "^3.3.2", "extend": "~3.0.2", "caseless": "~0.12.0", "isstream": "~0.1.2", "aws-sign2": "~0.7.0", "form-data": "~2.3.2", "mime-types": "~2.1.19", "oauth-sign": "~0.9.0", "safe-buffer": "^5.1.2", "tough-cookie": "~2.5.0", "tunnel-agent": "^0.6.0", "forever-agent": "~0.6.1", "har-validator": "~5.1.3", "is-typedarray": "~1.0.0", "http-signature": "~1.2.0", "combined-stream": "~1.0.6", "performance-now": "^2.1.0", "json-stringify-safe": "~5.0.1"}, "devDependencies": {"nyc": "^14.1.1", "tape": "^4.6.0", "karma": "^3.0.0", "taper": "^0.5.0", "rimraf": "^2.2.8", "codecov": "^3.0.4", "bluebird": "^3.2.1", "standard": "^9.0.0", "coveralls": "^3.0.2", "karma-cli": "^1.0.0", "karma-tap": "^3.0.1", "browserify": "^13.0.1", "buffer-equal": "^1.0.0", "function-bind": "^1.0.2", "karma-coverage": "^1.0.0", "server-destroy": "^1.0.1", "karma-browserify": "^5.0.1", "phantomjs-prebuilt": "^2.1.3", "browserify-istanbul": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "d73c918731cb5a87da047e207234146f664d12b3", "tarball": "https://registry.npmjs.org/request/-/request-2.88.2.tgz", "fileCount": 17, "integrity": "sha512-MsvtOrfG9ZcrOwAW+Qi+F6HbD0CWXEh9ou77uOb7FM2WPhwT7smM833PzanhJLsgXjN89Ir6V2PczXNnMpwKhw==", "signatures": [{"sig": "MEQCIFj6xTRl6fMJKw6ARRHIknwsRjtcirXlYWkzpg0kVEPYAiBsTXEj1GF7e9LdNxMAnxRMGbWnCOmuv+VsNPXr9ynwnA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 209281, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeQtfYCRA9TVsSAnZWagAA59oQAJLH1NJl5fM1Km3ck/W2\ncmx/3u1fgojd4MKqWSnmykUMoEAQ6qEDQ0/WVDbCkRRtynWTA8ZPdD4pIKUk\nBcfmlibHMAy4HGDIXS+fIM7jVO8vqfVefTkOPUtsDjEvGZxE4IwXN6PEhVaZ\nAQ6UaJbLUt3M3j5VbZCREXjhdq90tCqjmb/2NuuijTHScxexzzBbuBjfVqsO\nR2v/So/iaMQvJOOObiImdxZNzwwPZ5zDEFpfv3jGnG1m/Os93yNFrJ59cy0D\nMaphon1TVDrZRt7g0vn88TCIbl/bikiaa0OpTAgVtxXSkA15dcllmSsTbc5p\nfkHADrX8AdTqij9Qyir4p363UaTMfASJDLPsEDOO37zKNzt8Yzak5H+XE5oQ\ns+VF2DxTfUf0ODZBwswp+eXImmZsskkPU+Hgpw5pu9wt/vtJ/ZNupy3pIeAM\nr0gkqSNQctSUevhAcFM7dhzG2D5Htgh0905vkxE8Rio4VoD85ic/U3klAcD5\n6uWMKSDgBTgt7hb6A7npM5lqK5IK9kS4Ni9jKit/Fl7kDHpStj6w2y6tq0dj\nsoq0CB6NjGTte9/fB6Eo19DtUBlIt07LdGjqpEj0c1gYErl2Ho7JCM/hi9ha\nkhbLSs1EfH4QM2n71bUm2UGKRnXTtFexRJBzk2rScAll5N8P13eTEB879Lat\n+cyh\r\n=cj06\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 6"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142"}}, "modified": "2024-10-30T14:41:05.915Z"}