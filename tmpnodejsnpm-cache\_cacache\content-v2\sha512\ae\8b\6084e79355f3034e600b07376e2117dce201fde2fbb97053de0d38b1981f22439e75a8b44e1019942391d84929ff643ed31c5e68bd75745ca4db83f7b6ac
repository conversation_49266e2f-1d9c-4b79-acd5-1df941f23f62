{"name": "get-pixels", "dist-tags": {"latest": "3.3.3"}, "versions": {"0.0.0": {"name": "get-pixels", "version": "0.0.0", "dependencies": {"ndarray": "~0.2.1", "ndarray-ops": "~0.1.3", "pngparse": "~1.1.4"}, "devDependencies": {"tap": "~0.4.2"}, "directories": {"test": "test"}, "dist": {"shasum": "fe95a53c3b5ddeb3a7ba8bde6985483a12097eed", "tarball": "https://registry.npmjs.org/get-pixels/-/get-pixels-0.0.0.tgz", "integrity": "sha512-0pYGVpa1apf/QpwgGyKHV04XBRzpfc0DKqe2JYKyW93igcWWx/db8jSBDSEC98W+sG114YUrAApuWJyO5aAb/g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIF3HjacNAvGqgU+lbXoVGoMyb374myMty8y/CsOvNDTRAiBPW77XSxt0/h24DyxN5z5c0exrYOPnV7mVlWdbPvdRyA=="}]}}, "0.0.3": {"name": "get-pixels", "version": "0.0.3", "dependencies": {"ndarray": "~0.2.1", "ndarray-ops": "~0.1.3", "pngparse": "~1.1.4", "ppm": "0.0.0"}, "devDependencies": {"tap": "~0.4.2"}, "directories": {"test": "test"}, "dist": {"shasum": "281ad02d4ac5fb6f4a3563b1798178b7ea94adb9", "tarball": "https://registry.npmjs.org/get-pixels/-/get-pixels-0.0.3.tgz", "integrity": "sha512-JU0PCgKtJEKFCIXPbfYz1iH6Q27GGFHmqV1dKX13CY9WcFvBwWj1ApnUkV0Kb33PaHU89g+YdWAq1PxB2QZMYA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCID7BqiU+8FosvFWKn2ewMPgPiBW96l+WqJSZFHgAlBIVAiEAsS5LwyhKIzVfRspOAIB19/SepfVyWA4/II+l8GeJQmE="}]}}, "0.0.4": {"name": "get-pixels", "version": "0.0.4", "dependencies": {"ndarray": "~0.2.1", "ndarray-ops": "~0.1.3", "pngparse": "~1.1.4", "ppm": "0.0.0", "ndarray-pack": "0.0.1"}, "devDependencies": {"tap": "~0.4.2"}, "directories": {"test": "test"}, "dist": {"shasum": "c4232df33ec415460b6fbc7e49a7be3253765f5b", "tarball": "https://registry.npmjs.org/get-pixels/-/get-pixels-0.0.4.tgz", "integrity": "sha512-YjjK7aw3NnVz/br/v8wb2DheCK7JPSR6L7V+2DCO63fkPWwWKR8kq80Dclngho2z1CRb/1vye1/mw17/Ht3bQQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDI8yElse8oRZZEgLRI6VPJi5UvLHu7HIqoZ6qpE0N7BgIgfQNLp1F411ZAMg9F+/h9jPdx2mKidO8X1cMYeoB91pM="}]}}, "0.1.0": {"name": "get-pixels", "version": "0.1.0", "dependencies": {"ndarray": "~0.2.1", "ndarray-ops": "~0.1.3", "pngparse": "~1.1.4", "ppm": "0.0.0", "ndarray-pack": "0.0.1"}, "devDependencies": {"tap": "~0.4.2"}, "directories": {"test": "test"}, "dist": {"shasum": "0faf2181aaa79b2afe6cbb30b15a2bcf36fcb0c2", "tarball": "https://registry.npmjs.org/get-pixels/-/get-pixels-0.1.0.tgz", "integrity": "sha512-7QyloO/lxwLJKckwa3p+VBlhTgDjreMIvKBFw0Y9mmMoa2hxOJEnzB8SjmTNIhnEnz5ehujK6IPYhW1STttVbQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHWjEgq6DOmCWA1EfEtczXR3gMQGT6/MYUq9S5+uPtJNAiEAyTi5H7R6PJ823AuWL+HYNUpi8PeK2R4jBvj+mn8eomg="}]}}, "0.1.1": {"name": "get-pixels", "version": "0.1.1", "dependencies": {"ndarray": "~0.2.1", "ndarray-ops": "~0.1.3", "pngparse": "~1.1.4", "ppm": "0.0.0", "ndarray-pack": "0.0.1"}, "devDependencies": {"tap": "~0.4.2"}, "directories": {"test": "test"}, "dist": {"shasum": "f922932e73f7b01ff7719f71df64512e58e48972", "tarball": "https://registry.npmjs.org/get-pixels/-/get-pixels-0.1.1.tgz", "integrity": "sha512-p32kZE2rJTSHNciZ4m+424QRoKqVGPbBA3m5z6xLbjF7ALOTZXCi6kLgMLgduq7iIBqQSf8GYtUyS+8EcBiEkw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCvFg2h7H0PJCjWu1K/1oi0r+tNr2C8EaA8vjdYh6QW/QIhAJuTIKz7rRr4Yes1uyFCLXYlm84+9GceEifcFEVkXnww"}]}}, "1.0.0": {"name": "get-pixels", "version": "1.0.0", "dependencies": {"ndarray": "~1.0.0", "pngparse": "~1.1.4", "ppm": "0.0.0", "ndarray-pack": "0.0.1"}, "devDependencies": {"tap": "~0.4.2"}, "directories": {"test": "test"}, "dist": {"shasum": "90480f1fae65f9d7d3a0c7a7a7813b6439b060f3", "tarball": "https://registry.npmjs.org/get-pixels/-/get-pixels-1.0.0.tgz", "integrity": "sha512-7GJT1i5mf9giA97QFMaBDcuiwIc0xPfzOwk0FybgC1jMEgBxLYbx+Agx1ScwQoYU2BPKeY3aONsw0wllYdlkUg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFkNPjInuiCtkjO0Z7LdD+/doh9grvqwasBbB+TrMrf1AiBCC1d9x6V268mjdTv0ue200762XsptUw3el9ARvyU2Tg=="}]}}, "1.0.1": {"name": "get-pixels", "version": "1.0.1", "dependencies": {"ndarray": "~1.0.0", "pngparse": "~1.1.4", "ppm": "0.0.0", "ndarray-pack": "1.0.0"}, "devDependencies": {"tap": "~0.4.2"}, "directories": {"test": "test"}, "dist": {"shasum": "f8114df09fa25a25675c8b02bcb2142c1ced73d9", "tarball": "https://registry.npmjs.org/get-pixels/-/get-pixels-1.0.1.tgz", "integrity": "sha512-VUXdAWbNRX081mJOGLZVn9aghQbbDI/e45Rt7XLGRwo18x0WkVDrPINqQIHYyIOweTtg5nqnC988NVR8RzJaqg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD7/i9beTOSkFjfq97OjNMzoQxqnOvLH7TF/5INSzkmywIhANcvpvKa4xsErc9yh9jpTiczMSBAjYmxi1xBOU3KNsdS"}]}}, "1.1.0": {"name": "get-pixels", "version": "1.1.0", "dependencies": {"ndarray": "^1.0.13", "pngparse": "^1.1.4", "ppm": "0.0.0", "ndarray-pack": "^1.1.1", "jpeg-js": "0.0.3", "omggif": "^1.0.5", "node-bitmap": "0.0.1", "through": "^2.3.4"}, "devDependencies": {"tape": "^2.12.3", "browserify": "^3.44.0", "beefy": "^1.1.0"}, "directories": {"test": "test"}, "dist": {"shasum": "12e5a163349a9c210ae005172c9ab4b3913c600c", "tarball": "https://registry.npmjs.org/get-pixels/-/get-pixels-1.1.0.tgz", "integrity": "sha512-kXen3Xox6+WBLqA1wLT3JvbeBe8RVpCEwXIxaKPfKNBrQSHQcz/lGrh8ot+FccVAV+dftFsBioN0heEoNV+E5g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDKb6rNhBqHKCg+P/s5Egs7DoCbhsNvQqZFC1THq3r5sAiAJbJtd2C/MyOmgH5+bjDSow37gLLe79VEG1BFHcq/bHA=="}]}}, "1.1.2": {"name": "get-pixels", "version": "1.1.2", "dependencies": {"ndarray": "^1.0.13", "pngparse": "^1.1.4", "ppm": "0.0.0", "ndarray-pack": "^1.1.1", "jpeg-js": "0.0.3", "omggif": "^1.0.5", "node-bitmap": "0.0.1", "through": "^2.3.4"}, "devDependencies": {"tape": "^2.12.3", "browserify": "^3.44.0", "beefy": "^1.1.0"}, "directories": {"test": "test"}, "dist": {"shasum": "b76dbd6b615fdac91cf2481aafd824d937be65f1", "tarball": "https://registry.npmjs.org/get-pixels/-/get-pixels-1.1.2.tgz", "integrity": "sha512-YpVfE6elKBre05IY7LMbyRWlZiflTrO8KZ3TqYXTqj5y+lgbZDxp00MfdNJ3W8Ab2xAhCKmHyx8XEUDalbw8cQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEDDRFu6m05ahL3rLrfAiypjBgP/LxSL2L4IV5xv7KJ8AiBHKFsEx99dDnTcI24JBO3faQwKZ7zTsJT1g90nPXDmUQ=="}]}}, "1.1.3": {"name": "get-pixels", "version": "1.1.3", "dependencies": {"ndarray": "^1.0.13", "pngparse": "^1.1.4", "ppm": "0.0.0", "ndarray-pack": "^1.1.1", "jpeg-js": "0.0.3", "omggif": "^1.0.5", "node-bitmap": "0.0.1", "through": "^2.3.4"}, "devDependencies": {"tape": "^2.12.3", "browserify": "^3.44.0", "beefy": "^1.1.0"}, "directories": {"test": "test"}, "dist": {"shasum": "5b573b0a5720b179fa390205c91d7a0cd11e5163", "tarball": "https://registry.npmjs.org/get-pixels/-/get-pixels-1.1.3.tgz", "integrity": "sha512-QTljmTmeA2dZaKp2Fk9QWRFqt6tMn6sR17eDhCqgq6ttrZ9BnWQRwCerxJBwG6K8nGSnTJ9I1HK+ftCCYd80Hg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICV8xDoUsZ1+3Ux0iyM5WB5VY7xtsGD8w8HOAtAuAU4WAiAYkbYUaZGxic37z62eCsGfPpXFxmLKeNWZCv5bunCOvw=="}]}}, "2.0.0": {"name": "get-pixels", "version": "2.0.0", "dependencies": {"ndarray": "^1.0.13", "pngparse": "^1.1.4", "ndarray-pack": "^1.1.1", "jpeg-js": "0.0.3", "omggif": "^1.0.5", "node-bitmap": "0.0.1", "through": "^2.3.4", "request": "^2.44.0", "parse-data-uri": "^0.2.0", "mime-types": "^2.0.1", "data-uri-to-buffer": "0.0.3"}, "devDependencies": {"tape": "^2.12.3", "browserify": "^3.44.0", "beefy": "^1.1.0"}, "directories": {"test": "test"}, "dist": {"shasum": "7362f0ab038e481688fa52311d560e7f9c5b6f9f", "tarball": "https://registry.npmjs.org/get-pixels/-/get-pixels-2.0.0.tgz", "integrity": "sha512-SIiYEmy8ptzXe7O10/cHcECmV14lCwCMzd551mKrQ97HK/0ALqyNn3T0AufAnmVG7PKbc13RnUrOTHAQOPzkvw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCRnnmGMKS/OuS2Vw1ELOl0pvjlJw1Mj7FsxeiFuT0qawIhALKixQgXgmrYKJKvC2Cg90AznpJ5SW87LdFWe+phTD66"}]}}, "3.0.0": {"name": "get-pixels", "version": "3.0.0", "dependencies": {"ndarray": "^1.0.13", "pngparse": "^1.1.4", "ndarray-pack": "^1.1.1", "jpeg-js": "0.0.3", "omggif": "^1.0.5", "node-bitmap": "0.0.1", "through": "^2.3.4", "request": "^2.44.0", "parse-data-uri": "^0.2.0", "mime-types": "^2.0.1", "data-uri-to-buffer": "0.0.3"}, "devDependencies": {"tape": "^2.12.3", "browserify": "^3.44.0", "beefy": "^1.1.0"}, "directories": {"test": "test"}, "dist": {"shasum": "158b5f5572bffceb986e5121c9df55cf7256c881", "tarball": "https://registry.npmjs.org/get-pixels/-/get-pixels-3.0.0.tgz", "integrity": "sha512-6+0wxDLbTKZyMTGf+l6aVGq5s+5i1r3us2+ozk+J82cIpg7AJCF9+LK6YRCrs7IFm9C3gb0X1ujQvyz+0V01ZQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIF2HlQmJ59D5dETYWhK7cAyURHHoAPZ6LA4d60uEYspdAiEAuV+1JiBEO/Snhxs/3o4AOp6fUnDPV2JmTeCbOpM0RvI="}]}}, "3.0.1": {"name": "get-pixels", "version": "3.0.1", "dependencies": {"ndarray": "^1.0.13", "pngparse": "^1.1.4", "ndarray-pack": "^1.1.1", "jpeg-js": "0.0.3", "omggif": "^1.0.5", "node-bitmap": "0.0.1", "through": "^2.3.4", "request": "^2.44.0", "parse-data-uri": "^0.2.0", "mime-types": "^2.0.1", "data-uri-to-buffer": "0.0.3"}, "devDependencies": {"tape": "^2.12.3", "browserify": "^3.44.0", "beefy": "^1.1.0"}, "directories": {"test": "test"}, "dist": {"shasum": "2da4d516c2c5efd6310b55800614334813344a42", "tarball": "https://registry.npmjs.org/get-pixels/-/get-pixels-3.0.1.tgz", "integrity": "sha512-KrEUWkRO/owppJNpq5JerZFWMVzWnVav0v7Gz/X4E+c0onh2o5hwpRW3BhUW5MV++TC/TelP0jzUHbOsvB7zmA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDX2RDu9L9qQhuISiH7/G1WEgEyslhimafhVsRBSP1tSgIgLCsJIhKAzgngSyusFw/YRkOBotJB3Zn14B0IEXbSrU0="}]}}, "3.1.0": {"name": "get-pixels", "version": "3.1.0", "dependencies": {"ndarray": "^1.0.13", "pngparse": "^1.1.4", "ndarray-pack": "^1.1.1", "jpeg-js": "0.0.3", "omggif": "^1.0.5", "node-bitmap": "0.0.1", "through": "^2.3.4", "request": "^2.44.0", "parse-data-uri": "^0.2.0", "mime-types": "^2.0.1", "data-uri-to-buffer": "0.0.3"}, "devDependencies": {"tape": "^2.12.3", "brfs": "^1.2.0", "browserify": "^3.44.0", "beefy": "^1.1.0"}, "directories": {"test": "test"}, "dist": {"shasum": "32265cc57fd3b877211d5e3dbc477306017427e2", "tarball": "https://registry.npmjs.org/get-pixels/-/get-pixels-3.1.0.tgz", "integrity": "sha512-VhSH1DWgySW3f7GPJexw0j1TFQdbrNbbBCpBhxxvmk5dQf4T1dRZn5FX72fXp8FtqeVsUkAol6ZcgnCZ9ajgnA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFP1tlC+fU6lXZRr7LjRZw+elqAkCT5VqAOU4r2iq1+gAiBjJzSrD2uFDRjKXu2rGgIAXdCvYSjj0X10qJEDl2sfaQ=="}]}}, "3.1.1": {"name": "get-pixels", "version": "3.1.1", "dependencies": {"ndarray": "^1.0.13", "pngparse": "^1.1.4", "ndarray-pack": "^1.1.1", "jpeg-js": "^0.1.1", "omggif": "^1.0.5", "node-bitmap": "0.0.1", "through": "^2.3.4", "request": "^2.44.0", "parse-data-uri": "^0.2.0", "mime-types": "^2.0.1", "data-uri-to-buffer": "0.0.3"}, "devDependencies": {"tape": "^2.12.3", "brfs": "^1.2.0", "browserify": "^3.44.0", "beefy": "^1.1.0"}, "directories": {"test": "test"}, "dist": {"shasum": "c594377d117577ad7c02bd9b45994161d76ed378", "tarball": "https://registry.npmjs.org/get-pixels/-/get-pixels-3.1.1.tgz", "integrity": "sha512-bQF0gdeFNjFmfDNpJMteMr7nvTdnaFCpk1uEbPTDmtErIrrek0kinFfq14F9AEygH0hOXbHJI60dh9jN1Cx4pw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICc9tMm9Cw8V/JV8wKO4sJQd1dDINcPvtHOhDkJwpQLMAiEAlqqWf4frc0pyR8MFQlrYx/64f4QpKv8joCiuJ0qGqYY="}]}}, "3.2.0": {"name": "get-pixels", "version": "3.2.0", "dependencies": {"ndarray": "^1.0.13", "pngparse": "^1.1.4", "ndarray-pack": "^1.1.1", "jpeg-js": "^0.1.1", "omggif": "^1.0.5", "node-bitmap": "0.0.1", "through": "^2.3.4", "request": "^2.44.0", "parse-data-uri": "^0.2.0", "mime-types": "^2.0.1", "data-uri-to-buffer": "0.0.3"}, "devDependencies": {"tape": "^2.12.3", "brfs": "^1.2.0", "browserify": "^3.44.0", "beefy": "^1.1.0"}, "directories": {"test": "test"}, "dist": {"shasum": "04567d44cdfb3b68a5fa741d9ef65de0909eb6ad", "tarball": "https://registry.npmjs.org/get-pixels/-/get-pixels-3.2.0.tgz", "integrity": "sha512-/wb4iRHPsRk+sHy0Vvwg1YzvK4Z3YIvqplAtVoap889085DavmjMpqS4MMo6ofPWWzAoVZuOgRwM23C6loeU8A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCaT5+Cj3NE9LFkcK+xT5qOFVdJdHeLL+c5q6clriFWvQIgI+Xd40tVs2AYqf5oDD2xK5NekOsgUv5/Bhhxhpvn1pA="}]}}, "3.2.1": {"name": "get-pixels", "version": "3.2.1", "dependencies": {"ndarray": "^1.0.13", "pngparse": "^1.1.4", "ndarray-pack": "^1.1.1", "jpeg-js": "^0.1.1", "omggif": "^1.0.5", "node-bitmap": "0.0.1", "through": "^2.3.4", "request": "^2.44.0", "parse-data-uri": "^0.2.0", "mime-types": "^2.0.1", "data-uri-to-buffer": "0.0.3"}, "devDependencies": {"tape": "^2.12.3", "brfs": "^1.2.0", "browserify": "^3.44.0", "beefy": "^1.1.0"}, "directories": {"test": "test"}, "dist": {"shasum": "88be8d25e3d5a72353812aa4ff44b597e0e04bdf", "tarball": "https://registry.npmjs.org/get-pixels/-/get-pixels-3.2.1.tgz", "integrity": "sha512-KT2YVuqkautk7nhnhDOuTx1u4mjqvbE0jNQmkZLmUTJZFqTgk3a73A/muwwETCWloHrVoSE2n5WyrzgM/dwo1g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE6GBO//I7HqNrDAzwIKPRShYCaR16lxMbXTffVcShypAiEAz2Hp84F7YZlJf6DC8YqlBXMznwZvIsYARYkCAZSpbO8="}]}}, "3.2.2": {"name": "get-pixels", "version": "3.2.2", "dependencies": {"ndarray": "^1.0.13", "pngparse": "^1.1.4", "ndarray-pack": "^1.1.1", "jpeg-js": "^0.1.1", "omggif": "^1.0.5", "node-bitmap": "0.0.1", "through": "^2.3.4", "request": "^2.44.0", "parse-data-uri": "^0.2.0", "mime-types": "^2.0.1", "data-uri-to-buffer": "0.0.3"}, "devDependencies": {"tape": "^2.12.3", "brfs": "^1.2.0", "browserify": "^3.44.0", "beefy": "^1.1.0"}, "directories": {"test": "test"}, "dist": {"shasum": "83c3d10b261e93622cc558803ed730b2c9992e69", "tarball": "https://registry.npmjs.org/get-pixels/-/get-pixels-3.2.2.tgz", "integrity": "sha512-rRVlOhEQDBzc3ShLAdDDwPSi4DqVPZz41/sUAHgGqybsUZznQJy3w3IFIAJcTX4qDcFqXPMEA1eXUVlrilT1lA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBh9GCqnS1a69nBcGX3r51CqllGal2GPrH4v/mwF3IYhAiBgMQhV6WrQgjgfQO5T0gHR/QnK8/7/JlYJDugzMQ84ZQ=="}]}}, "3.2.3": {"name": "get-pixels", "version": "3.2.3", "dependencies": {"ndarray": "^1.0.13", "pngjs2": "^1.0.0", "ndarray-pack": "^1.1.1", "jpeg-js": "^0.1.1", "omggif": "^1.0.5", "node-bitmap": "0.0.1", "through": "^2.3.4", "request": "^2.44.0", "parse-data-uri": "^0.2.0", "mime-types": "^2.0.1", "data-uri-to-buffer": "0.0.3"}, "devDependencies": {"tape": "^2.12.3", "brfs": "^1.2.0", "browserify": "^3.44.0", "beefy": "^1.1.0"}, "directories": {"test": "test"}, "dist": {"shasum": "c4a220fc87236cf692969a596378e0b6f813531e", "tarball": "https://registry.npmjs.org/get-pixels/-/get-pixels-3.2.3.tgz", "integrity": "sha512-MW18kH8GLtKX7n+yzOx+Ah4bMo7P021zzQLOEikqyn4Z6hw/AY6kX/Sx3fR0Ja+4GBkmEFhGb2j73UarS3nw4g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGNGncioGBCWAA0Z8EnnVmzVcsBFg0oo+slIsZGAwJrBAiEAupzyT3H+l1u4MqgO0KclfsFh+8QKU4zq7e4W2gK5CAs="}]}}, "3.3.0": {"name": "get-pixels", "version": "3.3.0", "dependencies": {"ndarray": "^1.0.13", "pngjs": "^2.0.0", "ndarray-pack": "^1.1.1", "jpeg-js": "^0.1.1", "omggif": "^1.0.5", "node-bitmap": "0.0.1", "through": "^2.3.4", "request": "^2.44.0", "parse-data-uri": "^0.2.0", "mime-types": "^2.0.1", "data-uri-to-buffer": "0.0.3"}, "devDependencies": {"tape": "^2.12.3", "brfs": "^1.2.0", "browserify": "^3.44.0", "beefy": "^1.1.0"}, "directories": {"test": "test"}, "dist": {"shasum": "8d9795beae18850b840f749581badc05d3e36e41", "tarball": "https://registry.npmjs.org/get-pixels/-/get-pixels-3.3.0.tgz", "integrity": "sha512-dgfeutGBMAJAbWWDkq3/n17Wbtqv3gOGs9MCU7xsB+D7Tu/0u24gmC3i6tDCI074Ix5lRhueFx6AfK07jX77lg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAeQ4nGZhWwevH1uVXI7yLgong2vnv0Capu6dkzS8XAKAiEAtjWpHsMg7RaBzP+wPGjW4rzFGjh1eaOK70S4R1sCpdE="}]}}, "3.3.2": {"name": "get-pixels", "version": "3.3.2", "dependencies": {"data-uri-to-buffer": "0.0.3", "jpeg-js": "^0.3.2", "mime-types": "^2.0.1", "ndarray": "^1.0.13", "ndarray-pack": "^1.1.1", "node-bitmap": "0.0.1", "omggif": "^1.0.5", "parse-data-uri": "^0.2.0", "pngjs": "^3.3.3", "request": "^2.44.0", "through": "^2.3.4"}, "devDependencies": {"beefy": "^2.1.8", "brfs": "^1.2.0", "browserify": "^3.44.0", "tap": "^10.7.0", "tape": "^2.12.3"}, "directories": {"test": "test"}, "dist": {"integrity": "sha512-6ar+8yPxRd1pskEcl2GSEu1La0+xYRjjnkby6AYiRDDwZ0tJbPQmHnSeH9fGLskT8kvR0OukVgtZLcsENF9YKQ==", "shasum": "3f62fb8811932c69f262bba07cba72b692b4ff03", "tarball": "https://registry.npmjs.org/get-pixels/-/get-pixels-3.3.2.tgz", "fileCount": 5, "unpackedSize": 11320, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbRbvqCRA9TVsSAnZWagAAFHEP/ROrSKj5H8aOMqcl8o6J\nsq+ffwHSK0lBh+UrZUUXlkbXF0cEsqt9bBdSwB0VhpAnA0E63LJnG1mHeu4t\nYPmjPaVi4x4+qhc67R3CbjE+w7AuPylwKiYVJg9I5dcxwnStG9gCxto33RUx\nWeHkVlMrVM7zpW1+dSZbRylorOWuEv8dGK+F8fOyUwRvsuKgXIMp0+h3qRtR\nEjckaJplsm/goJXvbGgxrmuZR/xH4x4AswqF0yoswSrw8n15p5v3xII7ZaJ2\nfaCXc2ofd61MG7cnP38jgfXrmEhDelzja+6mGlkra53/nWf93QaiinEmSGMj\nomi6v3EnonYqV0dSgVmWn5nZokuWiLyzkxzm762GYJQPFhSaMyI5xCKYVEse\nR8EQ54buiFbragh5iL9JbLQUfO1dR1IVm/NSDs9fBi14le0OmCYR0d6Py/KH\nOwfG0Hpw/P5BLZEGHnqUUaq+b6jASi6ZMoDU8ro+/ouPyh1AV/cDL0JqIVvQ\nc5pOm5vRZVEYV6+kT4mzeP59R95q0xK4sHDKv4EKGSzth0X89bi6gpFKoDnO\nd//XaZJzACuil02akW7z/kfc05a6+cldI3pcURRk+iZtV6f5wMbfwYUpXinG\nAT0iQ3AAbr6SLSXA3P3LRO7g0jzCXAxsh6G2ChdzhogJkz3yG87mhHXDgUMY\nJ/6j\r\n=fNhI\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIA9FfTgvXOSHk3iwfQ6mHX2JCvBPUmudixutBT6Q65eZAiAJdZJpL1C6ohjW3uhXu8r0H59/x5PeZuQ4SL4lQWxc3Q=="}]}}, "3.3.3": {"name": "get-pixels", "version": "3.3.3", "dependencies": {"data-uri-to-buffer": "0.0.3", "jpeg-js": "^0.4.1", "mime-types": "^2.0.1", "ndarray": "^1.0.13", "ndarray-pack": "^1.1.1", "node-bitmap": "0.0.1", "omggif": "^1.0.5", "parse-data-uri": "^0.2.0", "pngjs": "^3.3.3", "request": "^2.44.0", "through": "^2.3.4"}, "devDependencies": {"beefy": "^2.1.8", "brfs": "^1.2.0", "browserify": "^3.44.0", "tap": "^10.7.0", "tape": "^2.12.3"}, "directories": {"test": "test"}, "dist": {"integrity": "sha512-5kyGBn90i9tSMUVHTqkgCHsoWoR+/lGbl4yC83Gefyr0HLIhgSWEx/2F/3YgsZ7UpYNuM6pDhDK7zebrUJ5nXg==", "shasum": "71e2dfd4befb810b5478a61c6354800976ce01c7", "tarball": "https://registry.npmjs.org/get-pixels/-/get-pixels-3.3.3.tgz", "fileCount": 5, "unpackedSize": 11320, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg8Eu5CRA9TVsSAnZWagAAtyMP/RXDxfHJ8UTAdO8ZOvLj\nka0/5coHQXxegtbXeAnx+9B2OLeKA0BW9DmIyAAhAu3cte4jqcm+UkYYKTf/\nyJ6SBNGNaF4aOb1ZtO2f1miWKNHDbPYRNKDNrnDix3+Iz/5f6pOWUQ9CRmLN\nJEJ0N633t4jm4e2cDyMdSAb6297JaZ85Zmg6S4NWRGbbenpy+HWYYNgeTsrV\nE1LuM9lOlqxzLU2NOiTP2rYFwr9y5WPE/VgkpItPgt/0c1hG9MBOIRugzwug\nCyYsmItuky/k58ZT6UvNfHCS9joQ+UrIJCKtiqHfBgQNh/FIJGXWf+lrhNvr\nLpLQzvxhww6dvxZQPCYYdqV+CXY3cEMVCQQ2vmvBdOMtNuCBGZvfc5GqK+r1\nk6R1+hmeh7uwDtyFbI2SWWKYZzuuidQNAhzvhyP0zX2kWuXlNCME0a1vuOH8\nCc4uJOWBtJXrnt98A2CMQzum9LjIdgOpc5gAv4ft9JyAnrc7L+CZFveghSF8\nalyw9A1eCiS/1dOT6W6hWi3qy04ZsJ68q+zx0T8zvsgwji4GNZUsG7lm60ju\nDG+ijcDODayVz5ysSoxZjMmAjOtngciwW0gn9YIrcXP9W/DDrZga7v4w2sgb\nP+gqZwpKGlUYp9wJulp7YtSN/+AIE3r3JghbJGmx/L8GjgiL1elCGX8a7VXt\nNw99\r\n=4Vpz\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD5WRzQPzKw0f/UP3wIDToRRS1U0OBfjWk19sio95MMfwIgWcJWYl3QaJGCdkyfpSHBfIEMOQov/qKmtdwfg0+VTEs="}]}}}, "modified": "2022-11-11T06:35:13.694Z"}