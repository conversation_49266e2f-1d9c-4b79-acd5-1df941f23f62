{"_id": "j<PERSON>y", "_rev": "698-64221fe6f070615d9f1d27b29c6b2074", "name": "j<PERSON>y", "dist-tags": {"latest": "3.7.1", "beta": "4.0.0-beta.2"}, "versions": {"1.5.1": {"name": "j<PERSON>y", "version": "1.5.1", "keywords": ["util", "dom", "j<PERSON>y"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "jquery@1.5.1", "contributors": [], "lib": "lib", "url": "jquery.com", "dist": {"shasum": "2ae2d661e906c1a01e044a71bb5b2743942183e5", "tarball": "https://registry.npmjs.org/jquery/-/jquery-1.5.1.tgz", "integrity": "sha512-a2vYRJfoPIabSy0nm0iQ1SskbqA3LbT+LH7EhbDRN2TwvR3fTPBUDbkVrwpHRBW+kyyKz8fnkQJkcv98GdrsDw==", "signatures": [{"sig": "MEQCICOiNkC0lv0d/ZnLOJI6q9vBMC7+xzoi9GzfYVfWXd1sAiBqZNYV5Tb1tnoWHt/bfCGQhsM5zGztX9b56yMVB5nDMw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./dist/node-jquery.js", "files": [""], "engines": {"node": "*"}, "deprecated": "This version is deprecated. Please upgrade to the latest version or find support at https://www.herodevs.com/support/jquery-nes.", "_npmVersion": "0.3.15", "description": "jQuery: The Write Less, Do More, JavaScript Library", "directories": {"lib": "./lib"}, "_nodeVersion": "v0.4.2", "dependencies": {"jsdom": "=0.1.20", "htmlparser": ">= 1.7.3"}, "_defaultsLoaded": true, "_engineSupported": true}, "1.6.2": {"name": "j<PERSON>y", "version": "1.6.2", "keywords": ["util", "dom", "j<PERSON>y"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "jquery@1.6.2", "contributors": [], "lib": "lib", "url": "jquery.com", "dist": {"shasum": "01757a4c5beea29e8ae697527c3131abbe997a28", "tarball": "https://registry.npmjs.org/jquery/-/jquery-1.6.2.tgz", "integrity": "sha512-8D3xPZmhpf4gIEB2RwrP/vTpoFSFWXoW5LA3WNGIsSdFzDiVNT9HPfGQHUzsX9wfpeAgirQ9g31JwyNqIP6KLQ==", "signatures": [{"sig": "MEYCIQDlLT8FPssJ5whN0dZcLFrlcWWYsbLasLBpFR0laY/a3AIhALg3a+l10xLEBsdMb4YqdX7CGRzeS9x5HTF4FIlrvXiI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./dist/node-jquery.js", "engines": {"node": "*"}, "scripts": {}, "deprecated": "This version is deprecated. Please upgrade to the latest version or find support at https://www.herodevs.com/support/jquery-nes.", "_npmVersion": "1.0.15", "description": "jQuery: The Write Less, Do More, JavaScript Library", "directories": {}, "_nodeVersion": "v0.4.8", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/jquery/1.6.2/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {"jsdom": ">=0.2.0", "htmlparser": ">= 1.7.3"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "1.6.3": {"name": "j<PERSON>y", "version": "1.6.3", "keywords": ["util", "dom", "j<PERSON>y"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "jquery@1.6.3", "maintainers": [{"name": "coolaj86", "email": "<EMAIL>"}], "contributors": [], "lib": ".", "url": "http://jquery.com", "dist": {"shasum": "e1f732fa7e718a6adb3ec20ae0eb2a64fd95ef01", "tarball": "https://registry.npmjs.org/jquery/-/jquery-1.6.3.tgz", "integrity": "sha512-Y<PERSON>TeNULsTmKKLYfO92lcUAbYAxz49pz91XXF7buVRueU9fFHalV2j9gx9DXRiwHlbsqrHnJ7QanR2I8m7SgDg==", "signatures": [{"sig": "MEYCIQDbJnk7pof5PeKJfOZilcL9bUm6k6xQ2SG6PKjmzN4RNAIhAPZFqCf5ccxvGQJpFfQKGk1+OXmNaT61em6ZLNHZJ4u2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./node-jquery.js", "engines": {"node": "*"}, "scripts": {}, "deprecated": "This version is deprecated. Please upgrade to the latest version or find support at https://www.herodevs.com/support/jquery-nes.", "_npmVersion": "1.0.22", "description": "jQuery: The Write Less, Do More, JavaScript Library (packaged for Node.JS)", "directories": {}, "_nodeVersion": "v0.4.8", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/jquery/1.6.3/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {"jsdom": ">=0.2.0", "htmlparser": ">= 1.7.3"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "1.7.2": {"name": "j<PERSON>y", "version": "1.7.2", "keywords": ["util", "dom", "j<PERSON>y"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "jquery@1.7.2", "maintainers": [{"name": "coolaj86", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/coolaj86/node-jquery", "bugs": {"url": "https://github.com/coolaj86/node-jquery/issues"}, "url": "http://jquery.com", "dist": {"shasum": "a93746763aca75a34df4c16395b0826310d0eaf2", "tarball": "https://registry.npmjs.org/jquery/-/jquery-1.7.2.tgz", "integrity": "sha512-+pUegUlLQv8LHcXM58zjMlnRBqnIAU3aTaZfmti5peytuM6WXmymOdt383LGGYdGWWKqIZBrBVemjfSkNuFJpg==", "signatures": [{"sig": "MEQCIFIOZJHsKQ4MvsMOaM4D+5NNbC9Jg7UaaDYjy2cibrAvAiAJuHy6aQCeMWzjh+poQYXkmwcFUWga6HlzaNcbmSKyLA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/node-j<PERSON>y", "engines": {"node": "0.6"}, "scripts": {"test": "grunt test"}, "_npmUser": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/coolaj86/node-jquery/blob/master/LICENSE-MIT", "type": "MIT"}], "deprecated": "This version is deprecated. Please upgrade to the latest version or find support at https://www.herodevs.com/support/jquery-nes.", "repository": {"url": "git://github.com/coolaj86/node-jquery.git", "type": "git"}, "_npmVersion": "1.1.12", "description": "jQuery: The Write Less, Do More, JavaScript Library (packaged for Node.JS)", "directories": {}, "_nodeVersion": "v0.6.14", "dependencies": {"jsdom": "~0.2.14", "htmlparser": "1.7.6", "xmlhttprequest": "~1.3.0"}, "_defaultsLoaded": true, "devDependencies": {"grunt": "~0.3.8", "nodeunit": "~0.7.4"}, "_engineSupported": true, "optionalDependencies": {}}, "1.7.3": {"name": "j<PERSON>y", "version": "1.7.3", "keywords": ["util", "dom", "j<PERSON>y"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "jquery@1.7.3", "maintainers": [{"name": "coolaj86", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/coolaj86/node-jquery", "bugs": {"url": "https://github.com/coolaj86/node-jquery/issues"}, "url": "http://jquery.com", "dist": {"shasum": "e3d00a71612ac7e9b554b438e0987d0272ddba94", "tarball": "https://registry.npmjs.org/jquery/-/jquery-1.7.3.tgz", "integrity": "sha512-0ECyFTCPngcVcjFHXgSFHyKbzfSB0WXyPyYDhjff4SAQa0UDHa3HH2D5s69hQVSgHw57E4KVMRUngfUn88JgYw==", "signatures": [{"sig": "MEUCIQDyVvRN/rk1y/o0wn79NLXw714qgwtf/EBZKMQ3xRdxEQIgeGheBf34WonVirBD/rNGPAwE7ix/vQsL/fg+3dSx22A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/node-j<PERSON>y", "engines": {"node": ">=0.6"}, "scripts": {"test": "grunt test"}, "licenses": [{"url": "https://github.com/coolaj86/node-jquery/blob/master/LICENSE-MIT", "type": "MIT"}], "deprecated": "This version is deprecated. Please upgrade to the latest version or find support at https://www.herodevs.com/support/jquery-nes.", "repository": {"url": "git://github.com/coolaj86/node-jquery.git", "type": "git"}, "description": "jQuery: The Write Less, Do More, JavaScript Library (packaged for Node.JS)", "directories": {}, "dependencies": {"jsdom": "~0.2.14", "location": "0.0.1", "navigator": "~1.0.1", "htmlparser": "1.7.6", "xmlhttprequest": "~1.4.2"}, "devDependencies": {"grunt": "~0.3.8", "nodeunit": "~0.7.4"}}, "1.8.2": {"name": "j<PERSON>y", "version": "1.8.2", "keywords": ["util", "dom", "j<PERSON>y"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "jquery@1.8.2", "maintainers": [{"name": "coolaj86", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/coolaj86/node-jquery", "bugs": {"url": "https://github.com/coolaj86/node-jquery/issues"}, "url": "http://jquery.com", "dist": {"shasum": "46790ae07c6de38124eda90bbf7336b43df93305", "tarball": "https://registry.npmjs.org/jquery/-/jquery-1.8.2.tgz", "integrity": "sha512-d3hb+jKqIrcrYRUQzh6PgJ6MVLjzwZf7NiYuxe7qKm+chGcD+g9K9l4t/9Lmvq2GcJK4HzW2Uumbd0GKtrZoyA==", "signatures": [{"sig": "MEUCIQDHukxk4AcZS7/nFsGRHYCDxYK8jBjdHMzDwAP+ZJTeBQIgYJ/jTRV+V5akMo4/nld+l0+jA9g4LV8rD2D+/R5OOHw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/node-j<PERSON>y", "engines": {"node": ">=0.6"}, "scripts": {"test": "grunt test"}, "_npmUser": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/coolaj86/node-jquery/blob/master/LICENSE-MIT", "type": "MIT"}], "deprecated": "This version is deprecated. Please upgrade to the latest version or find support at https://www.herodevs.com/support/jquery-nes.", "repository": {"url": "git://github.com/coolaj86/node-jquery.git", "type": "git"}, "_npmVersion": "1.1.61", "description": "jQuery: The Write Less, Do More, JavaScript Library (packaged for Node.JS)", "directories": {}, "dependencies": {"jsdom": "~0.2.14", "location": "0.0.1", "navigator": "~1.0.1", "htmlparser": "1.7.6", "xmlhttprequest": "~1.4.2"}, "devDependencies": {"grunt": "~0.3.8", "nodeunit": "~0.7.4"}}, "1.8.3": {"name": "j<PERSON>y", "version": "1.8.3", "keywords": ["util", "dom", "j<PERSON>y"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "jquery@1.8.3", "maintainers": [{"name": "coolaj86", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/coolaj86/node-jquery", "bugs": {"url": "https://github.com/coolaj86/node-jquery/issues"}, "url": "http://jquery.com", "dist": {"shasum": "cfa2941c05a83d966f21347f759a6d15281c60cc", "tarball": "https://registry.npmjs.org/jquery/-/jquery-1.8.3.tgz", "integrity": "sha512-gKfYOAquiKEiq445LgpmIyEwd20IpmqPq/45433e0STM59yVphR/XPuXz+QbpcxjF7aVc9ow9x8ul+yimL3Ivg==", "signatures": [{"sig": "MEQCID5w9IpBlt3XE2h+VP7ZOg5DmP1nZdc8t2Xdt49a9e86AiAZkQZkNtgpgFa6XgrOadnGSyL55RMjEtW4ZEyFuCjAyw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/node-j<PERSON>y", "engines": {"node": ">=0.6"}, "scripts": {"test": "grunt test"}, "_npmUser": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/coolaj86/node-jquery/blob/master/LICENSE-MIT", "type": "MIT"}], "deprecated": "This version is deprecated. Please upgrade to the latest version or find support at https://www.herodevs.com/support/jquery-nes.", "repository": {"url": "git://github.com/coolaj86/node-jquery.git", "type": "git"}, "_npmVersion": "1.1.61", "description": "jQuery: The Write Less, Do More, JavaScript Library (packaged for Node.JS)", "directories": {}, "dependencies": {"jsdom": "~0.2.14", "location": "0.0.1", "navigator": "~1.0.1", "contextify": "~0.1.3", "htmlparser": "1.7.6", "xmlhttprequest": "~1.4.2"}, "devDependencies": {"grunt": "~0.3.8", "nodeunit": "~0.7.4"}}, "2.1.0-beta2": {"name": "j<PERSON>y", "version": "2.1.0-beta2", "keywords": ["j<PERSON>y", "javascript", "library"], "author": {"url": "https://github.com/jquery/jquery/blob/master/AUTHORS.txt", "name": "jQuery Foundation and other contributors"}, "_id": "jquery@2.1.0-beta2", "maintainers": [{"name": "rwaldron", "email": "<EMAIL>"}, {"name": "j<PERSON>y", "email": "<EMAIL>"}], "homepage": "http://jquery.com", "bugs": {"url": "http://bugs.jquery.com"}, "dist": {"shasum": "e0fbbe2beb45b4d8f808362c7c99ef5bfee7d8c6", "tarball": "https://registry.npmjs.org/jquery/-/jquery-2.1.0-beta2.tgz", "integrity": "sha512-ZT1ccl8M7ehIUUb4w7LGVpmYpvWLP8GNUidubhV0Z1oWgNEeBL/K/aFIqRGnvYXf4RJYfs1+okem6zpA3eQB5Q==", "signatures": [{"sig": "MEQCIAhLtEoPOXJ3PebaIMJiaD//MeYTEYU3xxBDL7Ntw+H3AiBLgtRUSvDyeJOEMCrwhNLhkmfXJujNFiC848vM0pgYew==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "title": "j<PERSON><PERSON><PERSON>", "_npmUser": {"name": "j<PERSON>y", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/jquery/jquery/blob/master/MIT-LICENSE.txt", "type": "MIT"}], "deprecated": "This version is deprecated. Please upgrade to the latest version or find support at https://www.herodevs.com/support/jquery-nes.", "repository": {"url": "https://github.com/jquery/jquery.git", "type": "git"}, "_npmVersion": "1.3.11", "description": "JavaScript library for DOM operations", "directories": {}, "dependencies": {}, "devDependencies": {"grunt": "~0.4.1", "which": "~1.0.5", "gzip-js": "0.3.2", "archiver": "~0.4.10", "grunt-cli": "~0.1.11", "requirejs": "~2.1.9", "testswarm": "~1.1.0", "grunt-jsonlint": "~1.0.1", "grunt-bower-task": "~0.3.2", "load-grunt-tasks": "~0.2.0", "grunt-git-authors": "~1.2.0", "grunt-compare-size": "~0.4.0", "grunt-jscs-checker": "~0.2.3", "grunt-contrib-watch": "~0.5.3", "grunt-contrib-jshint": "~0.7.0", "grunt-contrib-uglify": "~0.2.7"}}, "2.1.0-beta3": {"name": "j<PERSON>y", "version": "2.1.0-beta3", "keywords": ["j<PERSON>y", "javascript", "library"], "author": {"url": "https://github.com/jquery/jquery/blob/2.1.0-beta3/AUTHORS.txt", "name": "jQuery Foundation and other contributors"}, "_id": "jquery@2.1.0-beta3", "maintainers": [{"name": "j<PERSON>y", "email": "<EMAIL>"}], "homepage": "http://jquery.com", "bugs": {"url": "http://bugs.jquery.com"}, "dist": {"shasum": "5a89b624d8fa625fe5fa83a12a9acb1ef8a11d02", "tarball": "https://registry.npmjs.org/jquery/-/jquery-2.1.0-beta3.tgz", "integrity": "sha512-YeEcuCQRE78AsPsRgOzI5vpCw09PRU/CRaGI0RdMibTEtRlQ3wGgDrk/+R2pgFnUDJhLNdT35zlutxtP4/CMbg==", "signatures": [{"sig": "MEYCIQDWNWNQWHdyhT0JPN5HGVIZeKB9jR5cBcZ2/pUZM3tduAIhANlGAubhQgCPlPUhFGRnTXOK5SrrtlJCYyl0WuXM6B9t", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/jquery.js", "_from": ".", "title": "j<PERSON><PERSON><PERSON>", "scripts": {"test": "grunt", "build": "npm install && grunt", "start": "grunt watch"}, "_npmUser": {"name": "j<PERSON>y", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/jquery/jquery/blob/2.1.0-beta3/MIT-LICENSE.txt", "type": "MIT"}], "deprecated": "This version is deprecated. Please upgrade to the latest version or find support at https://www.herodevs.com/support/jquery-nes.", "repository": {"url": "https://github.com/jquery/jquery.git", "type": "git"}, "_npmVersion": "1.3.21", "description": "JavaScript library for DOM operations", "directories": {}, "dependencies": {}, "devDependencies": {"grunt": "0.4.2", "gzip-js": "0.3.2", "shelljs": "0.2.6", "archiver": "0.4.10", "grunt-cli": "0.1.11", "requirejs": "2.1.9", "testswarm": "1.1.0", "grunt-jsonlint": "1.0.4", "grunt-bowercopy": "0.4.1", "load-grunt-tasks": "0.2.0", "grunt-git-authors": "1.2.0", "grunt-compare-size": "0.4.0", "grunt-jscs-checker": "0.2.6", "grunt-contrib-watch": "0.5.3", "grunt-contrib-jshint": "0.7.2", "grunt-contrib-uglify": "0.2.7"}}, "1.11.0-beta3": {"name": "j<PERSON>y", "version": "1.11.0-beta3", "keywords": ["j<PERSON>y", "javascript", "library"], "author": {"url": "https://github.com/jquery/jquery/blob/1.11.0-beta3/AUTHORS.txt", "name": "jQuery Foundation and other contributors"}, "_id": "jquery@1.11.0-beta3", "maintainers": [{"name": "j<PERSON>y", "email": "<EMAIL>"}], "homepage": "http://jquery.com", "bugs": {"url": "http://bugs.jquery.com"}, "dist": {"shasum": "0464a6aba9f35f6c83a203caa23ab420909ce852", "tarball": "https://registry.npmjs.org/jquery/-/jquery-1.11.0-beta3.tgz", "integrity": "sha512-1/Fzaq5PevzyI7nUQ5nNNiIoS+jXmD8vXUSg8iburHzay3yag1wsLzGAGwLyi8enOQsIzJhkq333r7L66iBLJQ==", "signatures": [{"sig": "MEUCIQD9A+L7dCeFluJ+FSOH2hstZwIQkLDZvra/UuUbSTRUgwIgUAFBWFSI3wkQMLfgaYEoCBcYUg/3c1XGNN2LyDUMpuo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/jquery.js", "_from": ".", "title": "j<PERSON><PERSON><PERSON>", "scripts": {"test": "grunt", "build": "npm install && grunt", "start": "grunt watch"}, "_npmUser": {"name": "j<PERSON>y", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/jquery/jquery/blob/1.11.0-beta3/MIT-LICENSE.txt", "type": "MIT"}], "deprecated": "This version is deprecated. Please upgrade to the latest version or find support at https://www.herodevs.com/support/jquery-nes.", "repository": {"url": "https://github.com/jquery/jquery.git", "type": "git"}, "_npmVersion": "1.3.21", "description": "JavaScript library for DOM operations", "directories": {}, "dependencies": {}, "devDependencies": {"grunt": "0.4.2", "gzip-js": "0.3.2", "shelljs": "0.2.6", "archiver": "0.4.10", "grunt-cli": "0.1.11", "requirejs": "2.1.9", "testswarm": "1.1.0", "grunt-jsonlint": "1.0.4", "grunt-bowercopy": "0.4.1", "load-grunt-tasks": "0.2.0", "grunt-git-authors": "1.2.0", "grunt-compare-size": "0.4.0", "grunt-jscs-checker": "0.2.6", "grunt-contrib-watch": "0.5.3", "grunt-contrib-jshint": "0.7.2", "grunt-contrib-uglify": "0.2.7"}}, "1.11.0-rc1": {"name": "j<PERSON>y", "version": "1.11.0-rc1", "keywords": ["j<PERSON>y", "javascript", "library"], "author": {"url": "https://github.com/jquery/jquery/blob/1.11.0-rc1/AUTHORS.txt", "name": "jQuery Foundation and other contributors"}, "_id": "jquery@1.11.0-rc1", "maintainers": [{"name": "j<PERSON>y", "email": "<EMAIL>"}], "homepage": "http://jquery.com", "bugs": {"url": "http://bugs.jquery.com"}, "dist": {"shasum": "c2f6a4877374647b20b080c478d8dbcdfb4960ee", "tarball": "https://registry.npmjs.org/jquery/-/jquery-1.11.0-rc1.tgz", "integrity": "sha512-HC3iIKnL5pQqJ7dotz5Ua7msBFb1pznwSLWpBJspba7cVPwOPSAkIuD1OwseRMDRjDnNyXDnP7DL2FXZmV+Ujw==", "signatures": [{"sig": "MEYCIQCa2gy4wCTkjviRkeYUsXxaZr2t7EDHwgif13lim2oeVwIhANfG1nwDitFKQiS4+/c3ylWIjZfg3YeXAX38y2rNLSi/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/jquery.js", "_from": ".", "title": "j<PERSON><PERSON><PERSON>", "scripts": {"test": "grunt", "build": "npm install && grunt", "start": "grunt watch"}, "_npmUser": {"name": "j<PERSON>y", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/jquery/jquery/blob/1.11.0-rc1/MIT-LICENSE.txt", "type": "MIT"}], "deprecated": "This version is deprecated. Please upgrade to the latest version or find support at https://www.herodevs.com/support/jquery-nes.", "repository": {"url": "https://github.com/jquery/jquery.git", "type": "git"}, "_npmVersion": "1.3.23", "description": "JavaScript library for DOM operations", "directories": {}, "dependencies": {}, "devDependencies": {"grunt": "0.4.2", "gzip-js": "0.3.2", "shelljs": "0.2.6", "archiver": "0.4.10", "grunt-cli": "0.1.11", "requirejs": "2.1.9", "testswarm": "1.1.0", "grunt-jsonlint": "1.0.4", "grunt-bowercopy": "0.5.0", "load-grunt-tasks": "0.2.0", "grunt-git-authors": "1.2.0", "grunt-compare-size": "0.4.0", "grunt-jscs-checker": "0.2.6", "grunt-contrib-watch": "0.5.3", "grunt-contrib-jshint": "0.7.2", "grunt-contrib-uglify": "0.2.7"}}, "2.1.0-rc1": {"name": "j<PERSON>y", "version": "2.1.0-rc1", "keywords": ["j<PERSON>y", "javascript", "library"], "author": {"url": "https://github.com/jquery/jquery/blob/2.1.0-rc1/AUTHORS.txt", "name": "jQuery Foundation and other contributors"}, "_id": "jquery@2.1.0-rc1", "maintainers": [{"name": "j<PERSON>y", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "scott.gonza<PERSON>z", "email": "<EMAIL>"}], "homepage": "http://jquery.com", "bugs": {"url": "http://bugs.jquery.com"}, "dist": {"shasum": "8c9f5d9a055c2fedb3f5269617ae649497d6a3b0", "tarball": "https://registry.npmjs.org/jquery/-/jquery-2.1.0-rc1.tgz", "integrity": "sha512-tf9pmn/Y5VQ0oS5N17iARL3vCtcQJdM981rdjtzKlDbWlJMNZGsVe92envWJAwVrJvm6qKD+wLs8nDyJbgVJIw==", "signatures": [{"sig": "MEUCIEcCcvU4kZTmRZCJ8NS1yQnoYuMCMWjxf1qbXZMsGFQjAiEAta+GGZ7kn+vBcRIwk+5mB7cGpJ8cv1EYUlUJdY5i5mU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/jquery.js", "_from": ".", "title": "j<PERSON><PERSON><PERSON>", "scripts": {"test": "grunt", "build": "npm install && grunt", "start": "grunt watch"}, "_npmUser": {"name": "scott.gonza<PERSON>z", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/jquery/jquery/blob/2.1.0-rc1/MIT-LICENSE.txt", "type": "MIT"}], "deprecated": "This version is deprecated. Please upgrade to the latest version or find support at https://www.herodevs.com/support/jquery-nes.", "repository": {"url": "https://github.com/jquery/jquery.git", "type": "git"}, "_npmVersion": "1.3.14", "description": "JavaScript library for DOM operations", "directories": {}, "dependencies": {}, "devDependencies": {"grunt": "0.4.2", "gzip-js": "0.3.2", "shelljs": "0.2.6", "archiver": "0.4.10", "grunt-cli": "0.1.11", "requirejs": "2.1.9", "testswarm": "1.1.0", "grunt-jsonlint": "1.0.4", "grunt-bowercopy": "0.5.0", "load-grunt-tasks": "0.2.0", "grunt-git-authors": "1.2.0", "grunt-compare-size": "0.4.0", "grunt-jscs-checker": "0.2.6", "grunt-contrib-watch": "0.5.3", "grunt-contrib-jshint": "0.7.2", "grunt-contrib-uglify": "0.2.7"}}, "1.11.0": {"name": "j<PERSON>y", "version": "1.11.0", "keywords": ["j<PERSON>y", "javascript", "library"], "author": {"url": "https://github.com/jquery/jquery/blob/1.11.0/AUTHORS.txt", "name": "jQuery Foundation and other contributors"}, "_id": "jquery@1.11.0", "maintainers": [{"name": "j<PERSON>y", "email": "<EMAIL>"}], "homepage": "http://jquery.com", "bugs": {"url": "http://bugs.jquery.com"}, "dist": {"shasum": "c67ceee19b403650d682adcf39d5c9009814d949", "tarball": "https://registry.npmjs.org/jquery/-/jquery-1.11.0.tgz", "integrity": "sha512-hfN15gXeUqsx3HxHrHYzVzfWayuGwrCMONhvexiaIV/aUlLqC8Im+G7KrKTPkHzD7HCgeAaQ7VeHRn3Dlve8Hg==", "signatures": [{"sig": "MEUCIQCisHsmKEXoi+EWl/zYZGCFwGn6hw8VgmpqSrUlR+u77gIgIEHu45km51UtyPFkLpSRmuKG0fJKiy7Ru50Lmu7f7uM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/jquery.js", "_from": ".", "title": "j<PERSON><PERSON><PERSON>", "scripts": {"test": "grunt", "build": "npm install && grunt", "start": "grunt watch"}, "_npmUser": {"name": "j<PERSON>y", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/jquery/jquery/blob/1.11.0/MIT-LICENSE.txt", "type": "MIT"}], "deprecated": "This version is deprecated. Please upgrade to the latest version or find support at https://www.herodevs.com/support/jquery-nes.", "repository": {"url": "https://github.com/jquery/jquery.git", "type": "git"}, "_npmVersion": "1.3.21", "description": "JavaScript library for DOM operations", "directories": {}, "dependencies": {}, "devDependencies": {"grunt": "0.4.2", "gzip-js": "0.3.2", "shelljs": "0.2.6", "archiver": "0.4.10", "grunt-cli": "0.1.11", "requirejs": "2.1.9", "testswarm": "1.1.0", "grunt-jsonlint": "1.0.4", "grunt-bowercopy": "0.5.0", "load-grunt-tasks": "0.2.0", "grunt-git-authors": "1.2.0", "grunt-compare-size": "0.4.0", "grunt-jscs-checker": "0.2.6", "grunt-contrib-watch": "0.5.3", "grunt-contrib-jshint": "0.7.2", "grunt-contrib-uglify": "0.2.7"}}, "2.1.0": {"name": "j<PERSON>y", "version": "2.1.0", "keywords": ["j<PERSON>y", "javascript", "library"], "author": {"url": "https://github.com/jquery/jquery/blob/2.1.0/AUTHORS.txt", "name": "jQuery Foundation and other contributors"}, "_id": "jquery@2.1.0", "maintainers": [{"name": "j<PERSON>y", "email": "<EMAIL>"}], "homepage": "http://jquery.com", "bugs": {"url": "http://bugs.jquery.com"}, "dist": {"shasum": "1c9a8c971d2b53dae10d72e16cbb5a1df16a4ace", "tarball": "https://registry.npmjs.org/jquery/-/jquery-2.1.0.tgz", "integrity": "sha512-QyJAvw0LUlUPci88C5LTFNtg4WFs70Dkqmwq4rZyOBflduKnHZakpIIDjhZVygAQbUqaghv6msUyP5TmRoNevQ==", "signatures": [{"sig": "MEQCIFy4g2oJwLjrYTzHuyx9C093mq/8ZytGc4e8G0211bNnAiBJztUS4cbsxsZG+oO+cV+I6Jy7Rfn5ctkqejwmSxqOnA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/jquery.js", "_from": ".", "title": "j<PERSON><PERSON><PERSON>", "scripts": {"test": "grunt", "build": "npm install && grunt", "start": "grunt watch"}, "_npmUser": {"name": "j<PERSON>y", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/jquery/jquery/blob/2.1.0/MIT-LICENSE.txt", "type": "MIT"}], "deprecated": "This version is deprecated. Please upgrade to the latest version or find support at https://www.herodevs.com/support/jquery-nes.", "repository": {"url": "https://github.com/jquery/jquery.git", "type": "git"}, "_npmVersion": "1.3.21", "description": "JavaScript library for DOM operations", "directories": {}, "dependencies": {}, "devDependencies": {"grunt": "0.4.2", "gzip-js": "0.3.2", "shelljs": "0.2.6", "archiver": "0.4.10", "grunt-cli": "0.1.11", "requirejs": "2.1.9", "testswarm": "1.1.0", "grunt-jsonlint": "1.0.4", "grunt-bowercopy": "0.5.0", "load-grunt-tasks": "0.2.0", "grunt-git-authors": "1.2.0", "grunt-compare-size": "0.4.0", "grunt-jscs-checker": "0.2.6", "grunt-contrib-watch": "0.5.3", "grunt-contrib-jshint": "0.7.2", "grunt-contrib-uglify": "0.2.7"}}, "2.1.1-beta1": {"name": "j<PERSON>y", "version": "2.1.1-beta1", "keywords": ["j<PERSON>y", "javascript", "library"], "author": {"url": "https://github.com/jquery/jquery/blob/2.1.1-beta1/AUTHORS.txt", "name": "jQuery Foundation and other contributors"}, "_id": "jquery@2.1.1-beta1", "maintainers": [{"name": "j<PERSON>y", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "scott.gonza<PERSON>z", "email": "<EMAIL>"}, {"name": "m_gol", "email": "<EMAIL>"}], "homepage": "http://jquery.com", "bugs": {"url": "http://bugs.jquery.com"}, "dist": {"shasum": "6306c8ea1d104775f3ef8f5c26f0a32acd710a11", "tarball": "https://registry.npmjs.org/jquery/-/jquery-2.1.1-beta1.tgz", "integrity": "sha512-D/KUiCI55zYivAAcpwO8S5letgUqgJmgFGcmigcJHQxkFkjtT0lT07TOTPkpCiVpqgCPUhlXoUNxpmbGWZFWuQ==", "signatures": [{"sig": "MEYCIQDnstz8WCxN1NaYD1E0QoUr1eQYI+kMMoVSWqvMu3DaowIhALWY50EJsuTZK3sGxQUEDeckQga/FoA/62MAzInLXhrc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/jquery.js", "_from": ".", "title": "j<PERSON><PERSON><PERSON>", "scripts": {"test": "grunt", "build": "npm install && grunt", "start": "grunt watch"}, "_npmUser": {"name": "m_gol", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/jquery/jquery/blob/2.1.1-beta1/MIT-LICENSE.txt", "type": "MIT"}], "deprecated": "This version is deprecated. Please upgrade to the latest version or find support at https://www.herodevs.com/support/jquery-nes.", "repository": {"url": "https://github.com/jquery/jquery.git", "type": "git"}, "_npmVersion": "1.4.6", "description": "JavaScript library for DOM operations", "directories": {}, "dependencies": {}, "devDependencies": {"grunt": "0.4.2", "gzip-js": "0.3.2", "grunt-cli": "0.1.13", "requirejs": "2.1.10", "testswarm": "1.1.0", "commitplease": "1.7.0", "grunt-jsonlint": "1.0.4", "grunt-bowercopy": "0.7.1", "load-grunt-tasks": "0.3.0", "grunt-git-authors": "1.2.0", "grunt-compare-size": "0.4.0", "grunt-jscs-checker": "0.3.2", "grunt-contrib-watch": "0.5.3", "grunt-contrib-jshint": "0.8.0", "grunt-contrib-uglify": "0.3.2"}}, "1.11.1-beta1": {"name": "j<PERSON>y", "version": "1.11.1-beta1", "keywords": ["j<PERSON>y", "javascript", "library"], "author": {"url": "https://github.com/jquery/jquery/blob/1.11.1-beta1/AUTHORS.txt", "name": "jQuery Foundation and other contributors"}, "_id": "jquery@1.11.1-beta1", "maintainers": [{"name": "j<PERSON>y", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "scott.gonza<PERSON>z", "email": "<EMAIL>"}, {"name": "m_gol", "email": "<EMAIL>"}], "homepage": "http://jquery.com", "bugs": {"url": "http://bugs.jquery.com"}, "dist": {"shasum": "c7eacde5e1ae06e029f1cd1b2dd444953a33e843", "tarball": "https://registry.npmjs.org/jquery/-/jquery-1.11.1-beta1.tgz", "integrity": "sha512-7IRgGBFk2z29rkhydPYWidusljFdtHL2xWl0x4TBccvL0suElxY+AL9nVE3g5nnhO1EH58lyrB0Pt1O6QdwCuw==", "signatures": [{"sig": "MEUCIBqXsjHXYgJFv39eZKZ9NJY3IJR8mzsCGCSWFFJIukRIAiEAidky+1to1DJN7Yu3kqRcnp8lUA8z3SRxJ3k8Y4BLSuo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/jquery.js", "_from": ".", "title": "j<PERSON><PERSON><PERSON>", "scripts": {"test": "grunt", "build": "npm install && grunt", "start": "grunt watch"}, "_npmUser": {"name": "m_gol", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/jquery/jquery/blob/1.11.1-beta1/MIT-LICENSE.txt", "type": "MIT"}], "deprecated": "This version is deprecated. Please upgrade to the latest version or find support at https://www.herodevs.com/support/jquery-nes.", "repository": {"url": "https://github.com/jquery/jquery.git", "type": "git"}, "_npmVersion": "1.4.6", "description": "JavaScript library for DOM operations", "directories": {}, "dependencies": {}, "devDependencies": {"grunt": "0.4.2", "gzip-js": "0.3.2", "grunt-cli": "0.1.13", "requirejs": "2.1.10", "testswarm": "1.1.0", "commitplease": "1.7.0", "grunt-jsonlint": "1.0.4", "grunt-bowercopy": "0.7.1", "load-grunt-tasks": "0.3.0", "grunt-git-authors": "1.2.0", "grunt-compare-size": "0.4.0", "grunt-jscs-checker": "0.3.2", "grunt-contrib-watch": "0.5.3", "grunt-contrib-jshint": "0.8.0", "grunt-contrib-uglify": "0.3.2"}}, "2.1.1-rc1": {"name": "j<PERSON>y", "version": "2.1.1-rc1", "keywords": ["j<PERSON>y", "javascript", "library"], "author": {"url": "https://github.com/jquery/jquery/blob/2.1.1-rc1/AUTHORS.txt", "name": "jQuery Foundation and other contributors"}, "_id": "jquery@2.1.1-rc1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "scott.gonza<PERSON>z", "email": "<EMAIL>"}, {"name": "m_gol", "email": "<EMAIL>"}], "homepage": "http://jquery.com", "bugs": {"url": "http://bugs.jquery.com"}, "dist": {"shasum": "95c494fdbbd0cefc305260e11ad46ae49a387c3d", "tarball": "https://registry.npmjs.org/jquery/-/jquery-2.1.1-rc1.tgz", "integrity": "sha512-vNLD3Dvv664L6YmjF28mn+MslrfiDZ7EC0tnuqTNbReePZ+IvEoQy/2pmSYoospovJOg0HDwTErxEt/8VjvSrQ==", "signatures": [{"sig": "MEYCIQDoxRTyx4ZvTqHRt8+Z09ouherF1434brK/epEOIysPyQIhAN/TkTvL/wsJ8B4FOhFd0tWsCNvefe417ovNXnQwRmmE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/jquery.js", "_from": ".", "title": "j<PERSON><PERSON><PERSON>", "_shasum": "95c494fdbbd0cefc305260e11ad46ae49a387c3d", "scripts": {"test": "grunt", "build": "npm install && grunt", "start": "grunt watch"}, "_npmUser": {"name": "m_gol", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/jquery/jquery/blob/2.1.1-rc1/MIT-LICENSE.txt", "type": "MIT"}], "deprecated": "This version is deprecated. Please upgrade to the latest version or find support at https://www.herodevs.com/support/jquery-nes.", "repository": {"url": "https://github.com/jquery/jquery.git", "type": "git"}, "_npmVersion": "1.4.7", "description": "JavaScript library for DOM operations", "directories": {}, "dependencies": {}, "devDependencies": {"grunt": "0.4.2", "gzip-js": "0.3.2", "grunt-cli": "0.1.13", "requirejs": "2.1.10", "testswarm": "1.1.0", "commitplease": "1.7.0", "grunt-jsonlint": "1.0.4", "grunt-bowercopy": "0.7.1", "load-grunt-tasks": "0.3.0", "grunt-git-authors": "1.2.0", "grunt-compare-size": "0.4.0", "grunt-jscs-checker": "0.4.1", "grunt-contrib-watch": "0.5.3", "grunt-contrib-jshint": "0.8.0", "grunt-contrib-uglify": "0.3.2"}}, "1.11.1-rc1": {"name": "j<PERSON>y", "version": "1.11.1-rc1", "keywords": ["j<PERSON>y", "javascript", "library"], "author": {"url": "https://github.com/jquery/jquery/blob/1.11.1-rc1/AUTHORS.txt", "name": "jQuery Foundation and other contributors"}, "_id": "j<PERSON>y@1.11.1-rc1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "scott.gonza<PERSON>z", "email": "<EMAIL>"}, {"name": "m_gol", "email": "<EMAIL>"}], "homepage": "http://jquery.com", "bugs": {"url": "http://bugs.jquery.com"}, "dist": {"shasum": "666a7df02488b48732d96e8ab9bdd34f61dd4238", "tarball": "https://registry.npmjs.org/jquery/-/jquery-1.11.1-rc1.tgz", "integrity": "sha512-JmCMrgBBW4mmwtz0hSVZx2ifKUJWV4isdq1HD8BAVCsezcuC6QEmv+DiTDzXVhnaFjrW6BwFxkUuD6M9tP78Tw==", "signatures": [{"sig": "MEUCIQDFmQd4++JduTKAFhXmMvtBCTjfQfNCEkgXsgZrSMwz4QIgMkVN6fvk4DykDFJy7J9dWuh+/luZ270r77HHZrS6/W4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/jquery.js", "_from": ".", "title": "j<PERSON><PERSON><PERSON>", "_shasum": "666a7df02488b48732d96e8ab9bdd34f61dd4238", "scripts": {"test": "grunt", "build": "npm install && grunt", "start": "grunt watch"}, "_npmUser": {"name": "m_gol", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/jquery/jquery/blob/1.11.1-rc1/MIT-LICENSE.txt", "type": "MIT"}], "deprecated": "This version is deprecated. Please upgrade to the latest version or find support at https://www.herodevs.com/support/jquery-nes.", "repository": {"url": "https://github.com/jquery/jquery.git", "type": "git"}, "_npmVersion": "1.4.7", "description": "JavaScript library for DOM operations", "directories": {}, "dependencies": {}, "devDependencies": {"grunt": "0.4.2", "gzip-js": "0.3.2", "grunt-cli": "0.1.13", "requirejs": "2.1.10", "testswarm": "1.1.0", "commitplease": "1.7.0", "grunt-jsonlint": "1.0.4", "grunt-bowercopy": "0.7.1", "load-grunt-tasks": "0.3.0", "grunt-git-authors": "1.2.0", "grunt-compare-size": "0.4.0", "grunt-jscs-checker": "0.4.1", "grunt-contrib-watch": "0.5.3", "grunt-contrib-jshint": "0.8.0", "grunt-contrib-uglify": "0.3.2"}}, "2.1.1-rc2": {"name": "j<PERSON>y", "version": "2.1.1-rc2", "keywords": ["j<PERSON>y", "javascript", "library"], "author": {"url": "https://github.com/jquery/jquery/blob/2.1.1-rc2/AUTHORS.txt", "name": "jQuery Foundation and other contributors"}, "_id": "j<PERSON>y@2.1.1-rc2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "scott.gonza<PERSON>z", "email": "<EMAIL>"}, {"name": "m_gol", "email": "<EMAIL>"}], "homepage": "http://jquery.com", "bugs": {"url": "http://bugs.jquery.com"}, "dist": {"shasum": "99833e415efa7ac8a4efecc5df6894b2f938a598", "tarball": "https://registry.npmjs.org/jquery/-/jquery-2.1.1-rc2.tgz", "integrity": "sha512-GaMghaxIW7LmDdOgawKfG7f6CEfq82W8lgotc73hjiZ3hHHI05GW/m/zAnxO3B/142DBrxHUASIMKbWkU0r3/g==", "signatures": [{"sig": "MEYCIQCzuA9CL913SGBGVxBtATdyZBA7PIjXzguss0Mwye5ikgIhAP7aoT9jq1APytg7LdzRu6Ca3fZ7JdnpQxMUmvhQPxW3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/jquery.js", "_from": ".", "title": "j<PERSON><PERSON><PERSON>", "scripts": {"test": "grunt", "build": "npm install && grunt", "start": "grunt watch"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/jquery/jquery/blob/2.1.1-rc2/MIT-LICENSE.txt", "type": "MIT"}], "deprecated": "This version is deprecated. Please upgrade to the latest version or find support at https://www.herodevs.com/support/jquery-nes.", "repository": {"url": "https://github.com/jquery/jquery.git", "type": "git"}, "_npmVersion": "1.2.25", "description": "JavaScript library for DOM operations", "directories": {}, "dependencies": {}, "devDependencies": {"grunt": "0.4.2", "gzip-js": "0.3.2", "grunt-cli": "0.1.13", "requirejs": "2.1.10", "testswarm": "1.1.0", "commitplease": "1.7.0", "grunt-jsonlint": "1.0.4", "grunt-bowercopy": "0.7.1", "load-grunt-tasks": "0.3.0", "grunt-git-authors": "1.2.0", "grunt-compare-size": "0.4.0", "grunt-jscs-checker": "0.4.1", "grunt-contrib-watch": "0.5.3", "grunt-contrib-jshint": "0.8.0", "grunt-contrib-uglify": "0.3.2"}}, "1.11.1-rc2": {"name": "j<PERSON>y", "version": "1.11.1-rc2", "keywords": ["j<PERSON>y", "javascript", "library"], "author": {"url": "https://github.com/jquery/jquery/blob/1.11.1-rc2/AUTHORS.txt", "name": "jQuery Foundation and other contributors"}, "_id": "j<PERSON>y@1.11.1-rc2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "scott.gonza<PERSON>z", "email": "<EMAIL>"}, {"name": "m_gol", "email": "<EMAIL>"}], "homepage": "http://jquery.com", "bugs": {"url": "http://bugs.jquery.com"}, "dist": {"shasum": "a4ef3edde0864d8524c5e72f59c459fd7a9ebd17", "tarball": "https://registry.npmjs.org/jquery/-/jquery-1.11.1-rc2.tgz", "integrity": "sha512-4Jx3dkGf4MAjnz9yIJJcZVDEuB4VvNmJH53l1F81wmkd291SRsBIrF0Ljco9GPaXuIwZga+TmzLqXHcZsXs3kg==", "signatures": [{"sig": "MEUCIF/ov/71fQmRmpfgUveTGypNHOsFwbUBBdA7o4BYbPlFAiEA6c7ryfnEk3cimPtNryEtjjp1o5eLqFoLf+2f97bmMsg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/jquery.js", "_from": ".", "title": "j<PERSON><PERSON><PERSON>", "scripts": {"test": "grunt", "build": "npm install && grunt", "start": "grunt watch"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/jquery/jquery/blob/1.11.1-rc2/MIT-LICENSE.txt", "type": "MIT"}], "deprecated": "This version is deprecated. Please upgrade to the latest version or find support at https://www.herodevs.com/support/jquery-nes.", "repository": {"url": "https://github.com/jquery/jquery.git", "type": "git"}, "_npmVersion": "1.2.25", "description": "JavaScript library for DOM operations", "directories": {}, "dependencies": {}, "devDependencies": {"grunt": "0.4.2", "gzip-js": "0.3.2", "grunt-cli": "0.1.13", "requirejs": "2.1.10", "testswarm": "1.1.0", "commitplease": "1.7.0", "grunt-jsonlint": "1.0.4", "grunt-bowercopy": "0.7.1", "load-grunt-tasks": "0.3.0", "grunt-git-authors": "1.2.0", "grunt-compare-size": "0.4.0", "grunt-jscs-checker": "0.4.1", "grunt-contrib-watch": "0.5.3", "grunt-contrib-jshint": "0.8.0", "grunt-contrib-uglify": "0.3.2"}}, "2.1.1": {"name": "j<PERSON>y", "version": "2.1.1", "keywords": ["j<PERSON>y", "javascript", "browser", "library"], "author": {"url": "https://github.com/jquery/jquery/blob/2.1.1/AUTHORS.txt", "name": "jQuery Foundation and other contributors"}, "_id": "jquery@2.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "scott.gonza<PERSON>z", "email": "<EMAIL>"}, {"name": "m_gol", "email": "<EMAIL>"}], "homepage": "http://jquery.com", "bugs": {"url": "http://bugs.jquery.com"}, "dist": {"shasum": "828fc60f50f7ee5983363ef4eb01c5f70af4bd5b", "tarball": "https://registry.npmjs.org/jquery/-/jquery-2.1.1.tgz", "integrity": "sha512-y+iOyLV9My82vtN1cc96DLlklSv15h5Jjug52TK+vXmgtN/dE89WvmP1W/g7lL+RWFzdsTmBwJgX/yf4RY9mPA==", "signatures": [{"sig": "MEQCIFpkxov+hkopxNgimFZLKLtPE+FDfru2DnDbB/JS4lfGAiBZPFlinf4zOCSFxGEFvFEPSoQyTmFe+t1aXzmcE06Llg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/jquery.js", "_from": ".", "title": "j<PERSON><PERSON><PERSON>", "scripts": {"test": "grunt", "build": "npm install && grunt", "start": "grunt watch"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/jquery/jquery/blob/2.1.1/MIT-LICENSE.txt", "type": "MIT"}], "deprecated": "This version is deprecated. Please upgrade to the latest version or find support at https://www.herodevs.com/support/jquery-nes.", "repository": {"url": "https://github.com/jquery/jquery.git", "type": "git"}, "_npmVersion": "1.2.25", "description": "JavaScript library for DOM operations", "directories": {}, "dependencies": {}, "devDependencies": {"grunt": "0.4.2", "gzip-js": "0.3.2", "grunt-cli": "0.1.13", "requirejs": "2.1.10", "testswarm": "1.1.0", "commitplease": "1.7.0", "grunt-jsonlint": "1.0.4", "grunt-bowercopy": "0.7.1", "load-grunt-tasks": "0.3.0", "grunt-git-authors": "1.2.0", "grunt-compare-size": "0.4.0", "grunt-jscs-checker": "0.4.1", "grunt-contrib-watch": "0.5.3", "grunt-contrib-jshint": "0.8.0", "grunt-contrib-uglify": "0.3.2"}}, "1.11.1": {"name": "j<PERSON>y", "version": "1.11.1", "keywords": ["j<PERSON>y", "javascript", "browser", "library"], "author": {"url": "https://github.com/jquery/jquery/blob/1.11.1/AUTHORS.txt", "name": "jQuery Foundation and other contributors"}, "_id": "jquery@1.11.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "scott.gonza<PERSON>z", "email": "<EMAIL>"}, {"name": "m_gol", "email": "<EMAIL>"}], "homepage": "http://jquery.com", "bugs": {"url": "http://bugs.jquery.com"}, "dist": {"shasum": "b6ec928590112ebed69e1e49cbfd0025ccd60ddb", "tarball": "https://registry.npmjs.org/jquery/-/jquery-1.11.1.tgz", "integrity": "sha512-vQfIKd5rOo2OVWkGQpcO16VRebvOiY801IzUiO6Yo89cRraqO+XE0BhvURLubLfla1GnifhLy/fGkcJ0k9kGog==", "signatures": [{"sig": "MEYCIQDXqCnT3wqXLVEZYPqMOeWKcUQ7B1bKKRAg+1gVAlWo3QIhAPgwMyEiuNxfAzqUXfrEo49M6J7xtlCx2G6s3p/fzCED", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/jquery.js", "_from": ".", "title": "j<PERSON><PERSON><PERSON>", "scripts": {"test": "grunt", "build": "npm install && grunt", "start": "grunt watch"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/jquery/jquery/blob/1.11.1/MIT-LICENSE.txt", "type": "MIT"}], "deprecated": "This version is deprecated. Please upgrade to the latest version or find support at https://www.herodevs.com/support/jquery-nes.", "repository": {"url": "https://github.com/jquery/jquery.git", "type": "git"}, "_npmVersion": "1.2.25", "description": "JavaScript library for DOM operations", "directories": {}, "dependencies": {}, "devDependencies": {"grunt": "0.4.2", "gzip-js": "0.3.2", "grunt-cli": "0.1.13", "requirejs": "2.1.10", "testswarm": "1.1.0", "commitplease": "1.7.0", "grunt-jsonlint": "1.0.4", "grunt-bowercopy": "0.7.1", "load-grunt-tasks": "0.3.0", "grunt-git-authors": "1.2.0", "grunt-compare-size": "0.4.0", "grunt-jscs-checker": "0.4.1", "grunt-contrib-watch": "0.5.3", "grunt-contrib-jshint": "0.8.0", "grunt-contrib-uglify": "0.3.2"}}, "1.9.1": {"name": "j<PERSON>y", "version": "1.9.1", "keywords": [], "author": {"url": "https://github.com/jquery/jquery/blob/master/AUTHORS.txt", "name": "jQuery Foundation and other contributors"}, "_id": "jquery@1.9.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "scott.gonza<PERSON>z", "email": "<EMAIL>"}, {"name": "m_gol", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}], "homepage": "http://jquery.com", "bugs": {"url": "http://bugs.jquery.com"}, "dist": {"shasum": "e4cd4835faaefbade535857613c0fc3ff2adaf34", "tarball": "https://registry.npmjs.org/jquery/-/jquery-1.9.1.tgz", "integrity": "sha512-gK7jP5cOEUzjyL0dy7MEMfeSFlmt1yNSdZK98CL8W6o0DiNVW5O9hLcD2bdl48mL8q7bEJgd7d9AhhDaN+iDSQ==", "signatures": [{"sig": "MEYCIQD4+badtz+2Nvi52CAdaDdTopFVHj6gHSYtx/kQIdEwpgIhAJVA9Ce9QqAyiMSbLVT7ivMq7blZ8J73uzDm2nbzUZ+h", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "title": "j<PERSON><PERSON><PERSON>", "_shasum": "e4cd4835faaefbade535857613c0fc3ff2adaf34", "gitHead": "d71f6a53927ad02d728503385d15539b73d21ac8", "scripts": {"test": "grunt"}, "_npmUser": {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/jquery/jquery/blob/master/MIT-LICENSE.txt", "type": "MIT"}], "deprecated": "This version is deprecated. Please upgrade to the latest version or find support at https://www.herodevs.com/support/jquery-nes.", "repository": {"url": "https://github.com/jquery/jquery.git", "type": "git"}, "_npmVersion": "1.5.0-alpha-1", "description": "JavaScript library for DOM operations", "directories": {}, "dependencies": {}, "devDependencies": {"grunt": "~0.4.0", "testswarm": "0.2.2", "grunt-git-authors": "~1.1.0", "grunt-compare-size": "~0.3.0", "grunt-contrib-watch": "~0.1.1", "grunt-contrib-jshint": "~0.1.1", "grunt-contrib-uglify": "~0.1.1", "grunt-update-submodules": "~0.2.0"}}, "2.1.2": {"name": "j<PERSON>y", "version": "2.1.2", "keywords": ["j<PERSON>y", "javascript", "browser", "library"], "author": {"url": "https://github.com/jquery/jquery/blob/2.1.2/AUTHORS.txt", "name": "jQuery Foundation and other contributors"}, "_id": "jquery@2.1.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "scott.gonza<PERSON>z", "email": "<EMAIL>"}, {"name": "m_gol", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}], "homepage": "http://jquery.com", "bugs": {"url": "http://bugs.jquery.com"}, "dist": {"shasum": "b68f154cb2ea4731924883e9fe20ec199d1dc1e2", "tarball": "https://registry.npmjs.org/jquery/-/jquery-2.1.2.tgz", "integrity": "sha512-lTpLlq6AjgvfzuoDwEt7miqoQ9CqrI08yi920il08UrJ71ZWWXXkNmOuPlpPKulsT5lBgR27iIT9ghIb0Tttuw==", "signatures": [{"sig": "MEQCIF6gmvnv+naH8Vpi5MtwL6dKed0cNgAYEkUC+f1Lr/XcAiATeBBRPufSziIx2AkTg0fyqUqZCLP35vzV13SacxzT6Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/jquery.js", "_from": ".", "title": "j<PERSON><PERSON><PERSON>", "_shasum": "b68f154cb2ea4731924883e9fe20ec199d1dc1e2", "gitHead": "a04f5ff9795fd6292117563623db44cf3f875868", "scripts": {"test": "grunt", "build": "npm install && grunt", "start": "grunt watch"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/jquery/jquery/blob/2.1.2/MIT-LICENSE.txt", "type": "MIT"}], "deprecated": "This version is deprecated. Please upgrade to the latest version or find support at https://www.herodevs.com/support/jquery-nes.", "repository": {"url": "https://github.com/jquery/jquery.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "JavaScript library for DOM operations", "directories": {}, "dependencies": {}, "devDependencies": {"grunt": "0.4.2", "gzip-js": "0.3.2", "grunt-cli": "0.1.13", "requirejs": "2.1.10", "testswarm": "1.1.0", "commitplease": "1.7.0", "grunt-jsonlint": "1.0.4", "grunt-bowercopy": "0.7.1", "load-grunt-tasks": "0.3.0", "grunt-git-authors": "1.2.0", "grunt-compare-size": "0.4.0", "grunt-jscs-checker": "0.4.1", "grunt-contrib-watch": "0.5.3", "grunt-contrib-jshint": "0.8.0", "grunt-contrib-uglify": "0.3.2"}}, "1.11.2": {"name": "j<PERSON>y", "version": "1.11.2", "keywords": ["j<PERSON>y", "javascript", "browser", "library"], "author": {"url": "https://github.com/jquery/jquery/blob/1.11.2/AUTHORS.txt", "name": "jQuery Foundation and other contributors"}, "_id": "j<PERSON>y@1.11.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "scott.gonza<PERSON>z", "email": "<EMAIL>"}, {"name": "m_gol", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}], "homepage": "http://jquery.com", "bugs": {"url": "http://bugs.jquery.com"}, "dist": {"shasum": "30ab26857211c37caa83da0f6903155fe49bb72d", "tarball": "https://registry.npmjs.org/jquery/-/jquery-1.11.2.tgz", "integrity": "sha512-jebe4XYv2S9CZ6RPj705j6yriQHm05BOJa9urxeji7Rq1fxBOwdjYTqJsLOAE9vNwDGIOsa/WmIQv6Q1glOyWg==", "signatures": [{"sig": "MEYCIQC09BkJy8T7vZVIoe4rLJHnv/F0PHoDv5huj6udId17ZQIhAJuTXoFGW5ZOe5Ksr19CsjsnAYAOumvzWoHuM9aXcM0e", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/jquery.js", "_from": ".", "title": "j<PERSON><PERSON><PERSON>", "_shasum": "30ab26857211c37caa83da0f6903155fe49bb72d", "gitHead": "9690801db01709bfbff5f977d07fb7cc14472908", "scripts": {"test": "grunt", "build": "npm install && grunt", "start": "grunt watch"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/jquery/jquery/blob/1.11.2/MIT-LICENSE.txt", "type": "MIT"}], "deprecated": "This version is deprecated. Please upgrade to the latest version or find support at https://www.herodevs.com/support/jquery-nes.", "repository": {"url": "https://github.com/jquery/jquery.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "JavaScript library for DOM operations", "directories": {}, "dependencies": {}, "devDependencies": {"grunt": "0.4.2", "gzip-js": "0.3.2", "grunt-cli": "0.1.13", "requirejs": "2.1.10", "testswarm": "1.1.0", "commitplease": "2.0.0", "grunt-jsonlint": "1.0.4", "grunt-bowercopy": "0.7.1", "load-grunt-tasks": "0.3.0", "grunt-git-authors": "1.2.0", "grunt-compare-size": "0.4.0", "grunt-jscs-checker": "0.4.1", "grunt-contrib-watch": "0.5.3", "grunt-contrib-jshint": "0.8.0", "grunt-contrib-uglify": "0.3.2"}}, "2.1.3": {"name": "j<PERSON>y", "version": "2.1.3", "keywords": ["j<PERSON>y", "javascript", "browser", "library"], "author": {"url": "https://github.com/jquery/jquery/blob/2.1.3/AUTHORS.txt", "name": "jQuery Foundation and other contributors"}, "_id": "jquery@2.1.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "scott.gonza<PERSON>z", "email": "<EMAIL>"}, {"name": "m_gol", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}], "homepage": "http://jquery.com", "bugs": {"url": "http://bugs.jquery.com"}, "dist": {"shasum": "6ec55204673d505d39432c5bf5cfad10e1dbad2e", "tarball": "https://registry.npmjs.org/jquery/-/jquery-2.1.3.tgz", "integrity": "sha512-1thSX0P6ZEPXU9fYrJD+lHbG4K3dYj8cNG+vZA/sFvSOgvkd55MOj5c79slivyLwPCeTYoHlRP4uMzPgUgpITQ==", "signatures": [{"sig": "MEYCIQDdvnCDnnLRXJyN523rb2yI0zEh2LARyTW5fR9OHutQZQIhAJBelCRnuLAwDINiwqpFu0rI5MIoTU/juJnvH9iV9dCO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/jquery.js", "_from": ".", "title": "j<PERSON><PERSON><PERSON>", "_shasum": "6ec55204673d505d39432c5bf5cfad10e1dbad2e", "gitHead": "8f2a9d9272d6ed7f32d3a484740ab342c02541e0", "scripts": {"test": "grunt", "build": "npm install && grunt", "start": "grunt watch"}, "_npmUser": {"name": "m_gol", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/jquery/jquery/blob/2.1.3/MIT-LICENSE.txt", "type": "MIT"}], "deprecated": "This version is deprecated. Please upgrade to the latest version or find support at https://www.herodevs.com/support/jquery-nes.", "repository": {"url": "https://github.com/jquery/jquery.git", "type": "git"}, "_npmVersion": "2.1.14", "description": "JavaScript library for DOM operations", "directories": {}, "_nodeVersion": "0.11.14", "dependencies": {}, "devDependencies": {"grunt": "0.4.2", "jsdom": "1.5.0", "gzip-js": "0.3.2", "grunt-cli": "0.1.13", "requirejs": "2.1.10", "testswarm": "1.1.0", "commitplease": "2.0.0", "grunt-jsonlint": "1.0.4", "grunt-bowercopy": "0.7.1", "load-grunt-tasks": "0.3.0", "grunt-git-authors": "1.2.0", "grunt-compare-size": "0.4.0", "grunt-jscs-checker": "0.4.1", "grunt-contrib-watch": "0.5.3", "grunt-contrib-jshint": "0.8.0", "grunt-contrib-uglify": "0.3.2"}}, "2.1.4": {"name": "j<PERSON>y", "version": "2.1.4", "keywords": ["j<PERSON>y", "javascript", "browser", "library"], "author": {"url": "https://github.com/jquery/jquery/blob/2.1.4/AUTHORS.txt", "name": "jQuery Foundation and other contributors"}, "_id": "jquery@2.1.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "scott.gonza<PERSON>z", "email": "<EMAIL>"}, {"name": "m_gol", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}], "homepage": "http://jquery.com", "bugs": {"url": "http://bugs.jquery.com"}, "dist": {"shasum": "228bde698a0c61431dc2630a6a154f15890d2317", "tarball": "https://registry.npmjs.org/jquery/-/jquery-2.1.4.tgz", "integrity": "sha512-wWR+eCq/T/Qt0NcFyM+QVho0ZVzWxFYANijmSMImXiM5mjr1aOaf4SF0eOEPc92bbK2L2vDpxw3lIszus7eO8Q==", "signatures": [{"sig": "MEUCIQCtAGzp8uWekppv9CelRFeo2SKsbwbaV4KzPpXtx+Gq3gIgI09UBCiyua8Dn4AjE5qyIvLzVIhQu89SMgrFF55qbu8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/jquery.js", "_from": ".", "title": "j<PERSON><PERSON><PERSON>", "_shasum": "228bde698a0c61431dc2630a6a154f15890d2317", "gitHead": "7751e69b615c6eca6f783a81e292a55725af6b85", "scripts": {"test": "grunt", "build": "npm install && grunt", "start": "grunt watch"}, "_npmUser": {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/jquery/jquery/blob/2.1.4/MIT-LICENSE.txt", "type": "MIT"}], "deprecated": "This version is deprecated. Please upgrade to the latest version or find support at https://www.herodevs.com/support/jquery-nes.", "repository": {"url": "https://github.com/jquery/jquery.git", "type": "git"}, "_npmVersion": "2.7.4", "description": "JavaScript library for DOM operations", "directories": {}, "_nodeVersion": "0.12.2", "dependencies": {}, "devDependencies": {"grunt": "0.4.2", "jsdom": "1.5.0", "gzip-js": "0.3.2", "grunt-cli": "0.1.13", "requirejs": "2.1.10", "testswarm": "1.1.0", "commitplease": "2.0.0", "grunt-jsonlint": "1.0.4", "grunt-bowercopy": "0.7.1", "load-grunt-tasks": "0.3.0", "grunt-git-authors": "1.2.0", "grunt-compare-size": "0.4.0", "grunt-jscs-checker": "0.4.1", "grunt-contrib-watch": "0.5.3", "grunt-contrib-jshint": "0.8.0", "grunt-contrib-uglify": "0.3.2"}}, "1.11.3": {"name": "j<PERSON>y", "version": "1.11.3", "keywords": ["j<PERSON>y", "javascript", "browser", "library"], "author": {"url": "https://github.com/jquery/jquery/blob/1.11.3/AUTHORS.txt", "name": "jQuery Foundation and other contributors"}, "_id": "j<PERSON>y@1.11.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "scott.gonza<PERSON>z", "email": "<EMAIL>"}, {"name": "m_gol", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}], "homepage": "http://jquery.com", "bugs": {"url": "http://bugs.jquery.com"}, "dist": {"shasum": "dd8b74278b27102d29df63eae28308a8cfa1b583", "tarball": "https://registry.npmjs.org/jquery/-/jquery-1.11.3.tgz", "integrity": "sha512-pHM/XLofp0FJc0/0AsRm8q/5ob+a1kno+vfclXGozaMBPv3qD7Xq19loECVcBB4MOLdygnyneQMPTsH5QiVNBQ==", "signatures": [{"sig": "MEQCIFrlxarDWVqAjzBIsFDjr6wkkRYWfMwfqdwAIrrbFyIKAiBmvwiXZGT9OI5lzUUd8eJh4Raaz8Yn0xg9r3DKYXnyIA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/jquery.js", "_from": ".", "title": "j<PERSON><PERSON><PERSON>", "_shasum": "dd8b74278b27102d29df63eae28308a8cfa1b583", "gitHead": "1472290917f17af05e98007136096784f9051fab", "scripts": {"test": "grunt", "build": "npm install && grunt", "start": "grunt watch"}, "_npmUser": {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/jquery/jquery/blob/1.11.3/MIT-LICENSE.txt", "type": "MIT"}], "deprecated": "This version is deprecated. Please upgrade to the latest version or find support at https://www.herodevs.com/support/jquery-nes.", "repository": {"url": "https://github.com/jquery/jquery.git", "type": "git"}, "_npmVersion": "2.7.4", "description": "JavaScript library for DOM operations", "directories": {}, "_nodeVersion": "0.12.2", "dependencies": {}, "devDependencies": {"grunt": "0.4.2", "gzip-js": "0.3.2", "grunt-cli": "0.1.13", "requirejs": "2.1.10", "testswarm": "1.1.0", "commitplease": "2.0.0", "grunt-jsonlint": "1.0.4", "grunt-bowercopy": "0.7.1", "load-grunt-tasks": "0.3.0", "grunt-git-authors": "1.2.0", "grunt-compare-size": "0.4.0", "grunt-jscs-checker": "0.4.1", "grunt-contrib-watch": "0.5.3", "grunt-contrib-jshint": "0.8.0", "grunt-contrib-uglify": "0.3.2"}}, "3.0.0-alpha1": {"name": "j<PERSON>y", "version": "3.0.0-alpha1", "keywords": ["j<PERSON>y", "javascript", "browser", "library"], "author": {"url": "https://github.com/jquery/jquery/blob/3.0.0-alpha1/AUTHORS.txt", "name": "jQuery Foundation and other contributors"}, "license": "MIT", "_id": "jquery@3.0.0-alpha1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "scott.gonza<PERSON>z", "email": "<EMAIL>"}, {"name": "m_gol", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}], "homepage": "http://jquery.com", "bugs": {"url": "https://github.com/jquery/jquery/issues"}, "dist": {"shasum": "3493d672266e21c2dffb2714f935448edebe3c62", "tarball": "https://registry.npmjs.org/jquery/-/jquery-3.0.0-alpha1.tgz", "integrity": "sha512-agCHkB3RtPYzPifHRYPuxAoWFX+t09VtJKAzPOjUvts/qq5P/1SULEbdoY8hFUSS3eTY/03CMlSfaRAip0T36A==", "signatures": [{"sig": "MEYCIQCz6wQOCe9l3Om2aYOJiSC+0SNupTPhJgXUPLaKbSVROwIhAKo1LDV52EXOxPpXy6tW/+hTmcCyDUyBKwvnQYC3FRhq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/jquery.js", "_from": ".", "title": "j<PERSON><PERSON><PERSON>", "_shasum": "3493d672266e21c2dffb2714f935448edebe3c62", "gitHead": "2c92869b752bb8e0fe74c3183f40f3f58b7b906d", "scripts": {"test": "grunt && grunt test", "build": "npm install && grunt", "start": "grunt watch"}, "_npmUser": {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}, "deprecated": "This version is deprecated. Please upgrade to the latest version or find support at https://www.herodevs.com/support/jquery-nes.", "repository": {"url": "git+https://github.com/jquery/jquery.git", "type": "git"}, "_npmVersion": "2.11.3", "description": "JavaScript library for DOM operations", "directories": {}, "_nodeVersion": "0.12.7", "commitplease": {"components": ["Docs", "Tests", "Build", "Release", "Core", "Ajax", "Attributes", "Callbacks", "CSS", "Data", "Deferred", "Dimensions", "Effects", "Event", "Manipulation", "Offset", "Queue", "Selector", "Serialize", "Traversing", "Wrap"]}, "dependencies": {}, "jsdomVersions": {"iojs": "5.3.0", "node": "3.1.2"}, "devDependencies": {"q": "1.1.2", "grunt": "0.4.5", "sinon": "1.10.3", "sizzle": "2.2.0", "core-js": "0.9.17", "gzip-js": "0.3.2", "qunitjs": "1.17.1", "grunt-cli": "0.1.13", "requirejs": "2.1.17", "testswarm": "1.1.0", "win-spawn": "2.0.0", "grunt-babel": "5.0.1", "commitplease": "2.0.0", "grunt-npmcopy": "0.1.0", "grunt-jsonlint": "1.0.4", "load-grunt-tasks": "1.0.0", "grunt-git-authors": "2.0.1", "grunt-compare-size": "0.4.0", "grunt-jscs-checker": "0.8.1", "grunt-contrib-watch": "0.6.1", "native-promise-only": "0.7.8-a", "grunt-contrib-jshint": "0.11.2", "grunt-contrib-uglify": "0.7.0", "promises-aplus-tests": "2.1.0"}}, "1.12.0": {"name": "j<PERSON>y", "version": "1.12.0", "keywords": ["j<PERSON>y", "javascript", "browser", "library"], "author": {"url": "https://github.com/jquery/jquery/blob/1.12-stable/AUTHORS.txt", "name": "jQuery Foundation and other contributors"}, "license": "MIT", "_id": "jquery@1.12.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "scott.gonza<PERSON>z", "email": "<EMAIL>"}, {"name": "m_gol", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}], "homepage": "http://jquery.com", "bugs": {"url": "https://github.com/jquery/jquery/issues"}, "dist": {"shasum": "44653be4e3e4628b106bf2141dfd10fbca6021ef", "tarball": "https://registry.npmjs.org/jquery/-/jquery-1.12.0.tgz", "integrity": "sha512-fnEEC7ylFkIKfOGvEky9Odcth1VYo61dfkQLxlDHjtNoIY9ssxybvTSG5xLDbutWdKn+grAu4EUJfhSUZODktQ==", "signatures": [{"sig": "MEQCIDYuuwR3kchPL81PJG9VRaxngxz7JuZ+SdZyIBIaG9IfAiAraTRyMqTtYQZllzmGOozZnFAXdkNu0sdSselagUJoGQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/jquery.js", "_from": ".", "title": "j<PERSON><PERSON><PERSON>", "_shasum": "44653be4e3e4628b106bf2141dfd10fbca6021ef", "gitHead": "efbdc6e3f0fa3d3cd4d3d8bfa37990b707f7c2e1", "scripts": {"test": "grunt && grunt test", "build": "npm install && grunt", "start": "grunt watch"}, "_npmUser": {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}, "deprecated": "This version is deprecated. Please upgrade to the latest version or find support at https://www.herodevs.com/support/jquery-nes.", "repository": {"url": "git+https://github.com/jquery/jquery.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "JavaScript library for DOM operations", "directories": {}, "_nodeVersion": "5.2.0", "commitplease": {"components": ["Docs", "Tests", "Build", "Release", "Core", "Ajax", "Attributes", "Callbacks", "CSS", "Data", "Deferred", "Deprecated", "Dimensions", "Effects", "Event", "Manipulation", "Offset", "Queue", "Selector", "Serialize", "Traversing", "Wrap"]}, "dependencies": {}, "devDependencies": {"npm": "2.1.12", "grunt": "0.4.5", "jsdom": "5.6.1", "sinon": "1.12.2", "sizzle": "2.2.1", "core-js": "0.9.17", "gzip-js": "0.3.2", "qunitjs": "1.17.1", "grunt-cli": "0.1.13", "requirejs": "2.1.17", "testswarm": "1.1.0", "win-spawn": "2.0.0", "grunt-jscs": "2.1.0", "grunt-babel": "5.0.1", "commitplease": "2.0.0", "grunt-npmcopy": "0.1.0", "grunt-jsonlint": "1.0.4", "load-grunt-tasks": "1.0.0", "grunt-git-authors": "2.0.1", "qunit-assert-step": "1.0.3", "grunt-compare-size": "0.4.0", "grunt-contrib-watch": "0.6.1", "strip-json-comments": "1.0.3", "grunt-contrib-jshint": "0.11.2", "grunt-contrib-uglify": "0.9.2"}}, "2.2.0": {"name": "j<PERSON>y", "version": "2.2.0", "keywords": ["j<PERSON>y", "javascript", "browser", "library"], "author": {"url": "https://github.com/jquery/jquery/blob/2.2.0/AUTHORS.txt", "name": "jQuery Foundation and other contributors"}, "license": "MIT", "_id": "jquery@2.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "scott.gonza<PERSON>z", "email": "<EMAIL>"}, {"name": "m_gol", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}], "homepage": "http://jquery.com", "bugs": {"url": "https://github.com/jquery/jquery/issues"}, "dist": {"shasum": "d0e84ebbf199da51bf7ec39307f19b35754e9cba", "tarball": "https://registry.npmjs.org/jquery/-/jquery-2.2.0.tgz", "integrity": "sha512-bQLMb56JzEBBs2CMzy8llOL0J1xm/JkDma6+UnF2cSPYJmvQHS9yZ+YGny/1YydkdZm8j0Ny8peiaeoWEwy9Zg==", "signatures": [{"sig": "MEQCIH0FIbDhGs1cBD/Z+HKc6TgFrNbVgX4oHfOirJTC9NwOAiBrYb8ADClEOgxpqUxw7d4cvwP8WDjwZQOmfegKy8qwNQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/jquery.js", "_from": ".", "title": "j<PERSON><PERSON><PERSON>", "_shasum": "d0e84ebbf199da51bf7ec39307f19b35754e9cba", "gitHead": "6fc01e29bdad0964f62ef56d01297039cdcadbe5", "scripts": {"test": "grunt && grunt test", "build": "npm install && grunt", "start": "grunt watch"}, "_npmUser": {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}, "deprecated": "This version is deprecated. Please upgrade to the latest version or find support at https://www.herodevs.com/support/jquery-nes.", "repository": {"url": "git+https://github.com/jquery/jquery.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "JavaScript library for DOM operations", "directories": {}, "_nodeVersion": "5.2.0", "commitplease": {"components": ["Docs", "Tests", "Build", "Release", "Core", "Ajax", "Attributes", "Callbacks", "CSS", "Data", "Deferred", "Deprecated", "Dimensions", "Effects", "Event", "Manipulation", "Offset", "Queue", "Selector", "Serialize", "Traversing", "Wrap"]}, "dependencies": {}, "devDependencies": {"grunt": "0.4.5", "jsdom": "5.6.1", "sinon": "1.10.3", "sizzle": "2.2.1", "core-js": "0.9.17", "gzip-js": "0.3.2", "qunitjs": "1.17.1", "grunt-cli": "0.1.13", "requirejs": "2.1.17", "testswarm": "1.1.0", "win-spawn": "2.0.0", "grunt-jscs": "2.1.0", "grunt-babel": "5.0.1", "commitplease": "2.0.0", "grunt-npmcopy": "0.1.0", "grunt-jsonlint": "1.0.4", "load-grunt-tasks": "1.0.0", "grunt-git-authors": "2.0.1", "qunit-assert-step": "1.0.3", "grunt-compare-size": "0.4.0", "grunt-contrib-watch": "0.6.1", "strip-json-comments": "1.0.3", "grunt-contrib-jshint": "0.11.2", "grunt-contrib-uglify": "0.9.2"}}, "3.0.0-beta1": {"name": "j<PERSON>y", "version": "3.0.0-beta1", "keywords": ["j<PERSON>y", "javascript", "browser", "library"], "author": {"url": "https://github.com/jquery/jquery/blob/3.0.0-beta1/AUTHORS.txt", "name": "jQuery Foundation and other contributors"}, "license": "MIT", "_id": "jquery@3.0.0-beta1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "scott.gonza<PERSON>z", "email": "<EMAIL>"}, {"name": "m_gol", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}], "homepage": "https://jquery.com", "bugs": {"url": "https://github.com/jquery/jquery/issues"}, "dist": {"shasum": "d2a4e368e2eed7050bf66abbbb54db2ea345349d", "tarball": "https://registry.npmjs.org/jquery/-/jquery-3.0.0-beta1.tgz", "integrity": "sha512-Figes9JmTdOM9qddlSqUXdURQvo1/aQM+edtMu4xqpAdwt1OeX2NxY6Dufv4kqSWVuAhJxzgZs+ss/60hXJenw==", "signatures": [{"sig": "MEUCIHnLCxogmkErAEqDtgXS8FibZh15X1IEsdB6tKWx/RDGAiEAtSW9cZSr4GsCDu269G/2azRvxqAGW8hLO5AJSfU0Cr4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/jquery.js", "_from": ".", "title": "j<PERSON><PERSON><PERSON>", "_shasum": "d2a4e368e2eed7050bf66abbbb54db2ea345349d", "gitHead": "2ef761afd9addf78193f5191ece03bb20c9182c2", "scripts": {"test": "grunt && grunt test", "build": "npm install && grunt", "start": "grunt watch"}, "_npmUser": {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}, "deprecated": "This version is deprecated. Please upgrade to the latest version or find support at https://www.herodevs.com/support/jquery-nes.", "repository": {"url": "git+https://github.com/jquery/jquery.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "JavaScript library for DOM operations", "directories": {}, "_nodeVersion": "5.2.0", "commitplease": {"components": ["Docs", "Tests", "Build", "Release", "Core", "Ajax", "Attributes", "Callbacks", "CSS", "Data", "Deferred", "Deprecated", "Dimensions", "Effects", "Event", "Manipulation", "Offset", "Queue", "Selector", "Serialize", "Traversing", "Wrap"]}, "dependencies": {}, "devDependencies": {"q": "1.1.2", "grunt": "0.4.5", "jsdom": "5.6.1", "sinon": "1.10.3", "sizzle": "2.3.0", "core-js": "0.9.17", "gzip-js": "0.3.2", "qunitjs": "1.17.1", "grunt-cli": "0.1.13", "requirejs": "2.1.17", "testswarm": "1.1.0", "win-spawn": "2.0.0", "grunt-jscs": "2.1.0", "grunt-babel": "5.0.1", "commitplease": "2.0.0", "grunt-npmcopy": "0.1.0", "grunt-jsonlint": "1.0.4", "load-grunt-tasks": "1.0.0", "grunt-git-authors": "2.0.1", "qunit-assert-step": "1.0.3", "grunt-compare-size": "0.4.0", "grunt-contrib-watch": "0.6.1", "native-promise-only": "0.7.8-a", "strip-json-comments": "1.0.3", "grunt-contrib-jshint": "0.11.2", "grunt-contrib-uglify": "0.9.2", "promises-aplus-tests": "2.1.0"}}, "1.12.1": {"name": "j<PERSON>y", "version": "1.12.1", "keywords": ["j<PERSON>y", "javascript", "browser", "library"], "author": {"url": "https://github.com/jquery/jquery/blob/1.12-stable/AUTHORS.txt", "name": "jQuery Foundation and other contributors"}, "license": "MIT", "_id": "jquery@1.12.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "scott.gonza<PERSON>z", "email": "<EMAIL>"}, {"name": "m_gol", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}], "homepage": "http://jquery.com", "bugs": {"url": "https://github.com/jquery/jquery/issues"}, "dist": {"shasum": "9cc34ce4780d4ceb90c44328f071064f01960c18", "tarball": "https://registry.npmjs.org/jquery/-/jquery-1.12.1.tgz", "integrity": "sha512-mxtI+UjxLlh9HztuA/F6X/9lv8H8qRfRkZWDLQaE8ijQRLDj25OXMxyzuL34Sy7JWMfYMw2PzcmljuIigRJb2A==", "signatures": [{"sig": "MEUCIA638qvG1620CxLrrnvginSm08r9DQo2/iVQtYVQZeqSAiEAtQ7oTfg6NozAwQz4+GLzwacf0DV2TEylVCO5eQeczME=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/jquery.js", "_from": ".", "title": "j<PERSON><PERSON><PERSON>", "_shasum": "9cc34ce4780d4ceb90c44328f071064f01960c18", "gitHead": "56ead6ffbf8560c521e7e94518d35db42b19f5f3", "scripts": {"test": "grunt && grunt test", "build": "npm install && grunt", "start": "grunt watch"}, "_npmUser": {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}, "deprecated": "This version is deprecated. Please upgrade to the latest version or find support at https://www.herodevs.com/support/jquery-nes.", "repository": {"url": "git+https://github.com/jquery/jquery.git", "type": "git"}, "_npmVersion": "2.14.19", "description": "JavaScript library for DOM operations", "directories": {}, "_nodeVersion": "0.10.42", "commitplease": {"components": ["Docs", "Tests", "Build", "Release", "Core", "Ajax", "Attributes", "Callbacks", "CSS", "Data", "Deferred", "Deprecated", "Dimensions", "Effects", "Event", "Manipulation", "Offset", "Queue", "Selector", "Serialize", "Traversing", "Wrap"]}, "dependencies": {}, "devDependencies": {"npm": "2.1.12", "grunt": "0.4.5", "jsdom": "5.6.1", "sinon": "1.12.2", "sizzle": "2.2.1", "core-js": "0.9.17", "gzip-js": "0.3.2", "qunitjs": "1.17.1", "grunt-cli": "0.1.13", "requirejs": "2.1.17", "testswarm": "1.1.0", "win-spawn": "2.0.0", "grunt-jscs": "2.1.0", "grunt-babel": "5.0.1", "commitplease": "2.0.0", "grunt-npmcopy": "0.1.0", "grunt-jsonlint": "1.0.4", "load-grunt-tasks": "1.0.0", "grunt-git-authors": "2.0.1", "qunit-assert-step": "1.0.3", "grunt-compare-size": "0.4.0", "grunt-contrib-watch": "0.6.1", "strip-json-comments": "1.0.3", "grunt-contrib-jshint": "0.11.2", "grunt-contrib-uglify": "0.9.2"}, "_npmOperationalInternal": {"tmp": "tmp/jquery-1.12.1.tgz_1456168080336_0.4474994211923331", "host": "packages-5-east.internal.npmjs.com"}}, "2.2.1": {"name": "j<PERSON>y", "version": "2.2.1", "keywords": ["j<PERSON>y", "javascript", "browser", "library"], "author": {"url": "https://github.com/jquery/jquery/blob/2.2.1/AUTHORS.txt", "name": "jQuery Foundation and other contributors"}, "license": "MIT", "_id": "jquery@2.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "scott.gonza<PERSON>z", "email": "<EMAIL>"}, {"name": "m_gol", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}], "homepage": "http://jquery.com", "bugs": {"url": "https://github.com/jquery/jquery/issues"}, "dist": {"shasum": "3c3e16854ad3d2ac44ac65021b17426d22ad803f", "tarball": "https://registry.npmjs.org/jquery/-/jquery-2.2.1.tgz", "integrity": "sha512-UH92IZolHWqKt1IxAXpdUex5+zJNo1zqqUVMRVc3RTiqrM6yQ+OVecnjdd6BqbQMv+AD4oUCQ6NTUWvUQ/Hrfg==", "signatures": [{"sig": "MEQCIH6HDbLwHip3bKVZN6lKskMjInZeLctP+++4bZ6EkmeaAiB2PmYyqPTEnZBPyCQxXFaV7L+k0CuNLwMFpw9jh+gjqQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/jquery.js", "_from": ".", "title": "j<PERSON><PERSON><PERSON>", "_shasum": "3c3e16854ad3d2ac44ac65021b17426d22ad803f", "gitHead": "788eaba2f83e7b7445c7a83a50c81c0704423874", "scripts": {"test": "grunt && grunt test", "build": "npm install && grunt", "start": "grunt watch"}, "_npmUser": {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}, "deprecated": "This version is deprecated. Please upgrade to the latest version or find support at https://www.herodevs.com/support/jquery-nes.", "repository": {"url": "git+https://github.com/jquery/jquery.git", "type": "git"}, "_npmVersion": "2.14.19", "description": "JavaScript library for DOM operations", "directories": {}, "_nodeVersion": "0.10.42", "commitplease": {"components": ["Docs", "Tests", "Build", "Release", "Core", "Ajax", "Attributes", "Callbacks", "CSS", "Data", "Deferred", "Deprecated", "Dimensions", "Effects", "Event", "Manipulation", "Offset", "Queue", "Selector", "Serialize", "Traversing", "Wrap"]}, "dependencies": {}, "devDependencies": {"grunt": "0.4.5", "jsdom": "5.6.1", "sinon": "1.10.3", "sizzle": "2.2.1", "core-js": "0.9.17", "gzip-js": "0.3.2", "qunitjs": "1.17.1", "grunt-cli": "0.1.13", "requirejs": "2.1.17", "testswarm": "1.1.0", "win-spawn": "2.0.0", "grunt-jscs": "2.1.0", "grunt-babel": "5.0.1", "commitplease": "2.0.0", "grunt-npmcopy": "0.1.0", "grunt-jsonlint": "1.0.4", "load-grunt-tasks": "1.0.0", "grunt-git-authors": "2.0.1", "qunit-assert-step": "1.0.3", "grunt-compare-size": "0.4.0", "grunt-contrib-watch": "0.6.1", "strip-json-comments": "1.0.3", "grunt-contrib-jshint": "0.11.2", "grunt-contrib-uglify": "0.9.2"}, "_npmOperationalInternal": {"tmp": "tmp/jquery-2.2.1.tgz_1456168325917_0.42471840139478445", "host": "packages-9-west.internal.npmjs.com"}}, "1.12.2": {"name": "j<PERSON>y", "version": "1.12.2", "keywords": ["j<PERSON>y", "javascript", "browser", "library"], "author": {"url": "https://github.com/jquery/jquery/blob/1.12-stable/AUTHORS.txt", "name": "jQuery Foundation and other contributors"}, "license": "MIT", "_id": "jquery@1.12.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "scott.gonza<PERSON>z", "email": "<EMAIL>"}, {"name": "m_gol", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}], "homepage": "http://jquery.com", "bugs": {"url": "https://github.com/jquery/jquery/issues"}, "dist": {"shasum": "b8a8b45937312a19eebbcf5a0589b0311c8220bb", "tarball": "https://registry.npmjs.org/jquery/-/jquery-1.12.2.tgz", "integrity": "sha512-v+uMW6VWgtyJTYHtjRFVCPmLdJrYUQUkSAyE89FwTgoYahdTQwuZ+ChZIJdjfxcQvLstYgB6/mODwTxeB1pJkw==", "signatures": [{"sig": "MEQCIDg4BJVnmI5oViBQdMbdlfS3wm7jEj7GpDz/ewdO/pJtAiBXp+7JZFKGideJlC1Cx5uROWK8vTVKNRl05NoooGuOSw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/jquery.js", "_from": ".", "title": "j<PERSON><PERSON><PERSON>", "_shasum": "b8a8b45937312a19eebbcf5a0589b0311c8220bb", "gitHead": "a9b5f8ac96f6aa7bfc7b0795cb16d65c4f15b64e", "scripts": {"test": "grunt && grunt test", "build": "npm install && grunt", "start": "grunt watch"}, "_npmUser": {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}, "deprecated": "This version is deprecated. Please upgrade to the latest version or find support at https://www.herodevs.com/support/jquery-nes.", "repository": {"url": "git+https://github.com/jquery/jquery.git", "type": "git"}, "_npmVersion": "3.7.3", "description": "JavaScript library for DOM operations", "directories": {}, "_nodeVersion": "5.8.0", "commitplease": {"components": ["Docs", "Tests", "Build", "Release", "Core", "Ajax", "Attributes", "Callbacks", "CSS", "Data", "Deferred", "Deprecated", "Dimensions", "Effects", "Event", "Manipulation", "Offset", "Queue", "Selector", "Serialize", "Traversing", "Wrap"]}, "dependencies": {}, "devDependencies": {"npm": "2.1.12", "grunt": "0.4.5", "jsdom": "5.6.1", "sinon": "1.12.2", "sizzle": "2.2.1", "core-js": "0.9.17", "gzip-js": "0.3.2", "qunitjs": "1.17.1", "grunt-cli": "0.1.13", "requirejs": "2.1.17", "testswarm": "1.1.0", "win-spawn": "2.0.0", "grunt-jscs": "2.1.0", "grunt-babel": "5.0.1", "commitplease": "2.0.0", "grunt-npmcopy": "0.1.0", "grunt-jsonlint": "1.0.4", "load-grunt-tasks": "1.0.0", "grunt-git-authors": "2.0.1", "qunit-assert-step": "1.0.3", "grunt-compare-size": "0.4.0", "grunt-contrib-watch": "0.6.1", "strip-json-comments": "1.0.3", "grunt-contrib-jshint": "0.11.2", "grunt-contrib-uglify": "0.9.2"}, "_npmOperationalInternal": {"tmp": "tmp/jquery-1.12.2.tgz_1458236759160_0.3557943068444729", "host": "packages-12-west.internal.npmjs.com"}}, "2.2.2": {"name": "j<PERSON>y", "version": "2.2.2", "keywords": ["j<PERSON>y", "javascript", "browser", "library"], "author": {"url": "https://github.com/jquery/jquery/blob/2.2.2/AUTHORS.txt", "name": "jQuery Foundation and other contributors"}, "license": "MIT", "_id": "jquery@2.2.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "scott.gonza<PERSON>z", "email": "<EMAIL>"}, {"name": "m_gol", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}], "homepage": "http://jquery.com", "bugs": {"url": "https://github.com/jquery/jquery/issues"}, "dist": {"shasum": "3e302dc61eb329a21e9efac937d731f061134c59", "tarball": "https://registry.npmjs.org/jquery/-/jquery-2.2.2.tgz", "integrity": "sha512-D7eqvNhFca7JVGdcnyKhVdCsNgMGev8mC295EIaLKq8Xp5u4UPkEhIYJD2ceO968J9EqaTqUPlJFba+Y9pCBOA==", "signatures": [{"sig": "MEUCIQCyGm8G647MWGqEQT0H6Iky2DHTJojP/xka6iVb3aKQxgIgHxNgLShg42YwdBYIrCmNE6lmiX3g/4BLKDUglIzLyAw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/jquery.js", "_from": ".", "title": "j<PERSON><PERSON><PERSON>", "_shasum": "3e302dc61eb329a21e9efac937d731f061134c59", "gitHead": "086d381cd2f3b4b8b0af85ecb2c9593a61e5b4bd", "scripts": {"test": "grunt && grunt test", "build": "npm install && grunt", "start": "grunt watch"}, "_npmUser": {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}, "deprecated": "This version is deprecated. Please upgrade to the latest version or find support at https://www.herodevs.com/support/jquery-nes.", "repository": {"url": "git+https://github.com/jquery/jquery.git", "type": "git"}, "_npmVersion": "3.7.3", "description": "JavaScript library for DOM operations", "directories": {}, "_nodeVersion": "5.8.0", "commitplease": {"components": ["Docs", "Tests", "Build", "Release", "Core", "Ajax", "Attributes", "Callbacks", "CSS", "Data", "Deferred", "Deprecated", "Dimensions", "Effects", "Event", "Manipulation", "Offset", "Queue", "Selector", "Serialize", "Traversing", "Wrap"]}, "dependencies": {}, "devDependencies": {"grunt": "0.4.5", "jsdom": "5.6.1", "sinon": "1.10.3", "sizzle": "2.2.1", "core-js": "0.9.17", "gzip-js": "0.3.2", "qunitjs": "1.17.1", "grunt-cli": "0.1.13", "requirejs": "2.1.17", "testswarm": "1.1.0", "win-spawn": "2.0.0", "grunt-jscs": "2.1.0", "grunt-babel": "5.0.1", "commitplease": "2.0.0", "grunt-npmcopy": "0.1.0", "grunt-jsonlint": "1.0.4", "load-grunt-tasks": "1.0.0", "grunt-git-authors": "2.0.1", "qunit-assert-step": "1.0.3", "grunt-compare-size": "0.4.0", "grunt-contrib-watch": "0.6.1", "strip-json-comments": "1.0.3", "grunt-contrib-jshint": "0.11.2", "grunt-contrib-uglify": "0.9.2"}, "_npmOperationalInternal": {"tmp": "tmp/jquery-2.2.2.tgz_1458237146417_0.4190880397800356", "host": "packages-12-west.internal.npmjs.com"}}, "1.12.3": {"name": "j<PERSON>y", "version": "1.12.3", "keywords": ["j<PERSON>y", "javascript", "browser", "library"], "author": {"url": "https://github.com/jquery/jquery/blob/1.12-stable/AUTHORS.txt", "name": "jQuery Foundation and other contributors"}, "license": "MIT", "_id": "jquery@1.12.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "scott.gonza<PERSON>z", "email": "<EMAIL>"}, {"name": "m_gol", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}], "homepage": "http://jquery.com", "bugs": {"url": "https://github.com/jquery/jquery/issues"}, "dist": {"shasum": "1298b88b908e7c7f7501eb8c1a61f1ac8337b531", "tarball": "https://registry.npmjs.org/jquery/-/jquery-1.12.3.tgz", "integrity": "sha512-FzM42/Ew+Hb8ha2OlhHRBLgWIZS32gZ0+NvWTf+ZvVvGaIlJkOiXQyb7VBjv4L6fJfmTrRf3EsAmbfsHDhfemw==", "signatures": [{"sig": "MEQCIES53gfWR4FdHQ5NBiyR4WKSmv/Fp45ttdUVMazLEJGMAiAkG5vxm6SIEHJdvDrpd2aIqJaBqp8gZQVqasdYQeBQaA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/jquery.js", "_from": ".", "title": "j<PERSON><PERSON><PERSON>", "_shasum": "1298b88b908e7c7f7501eb8c1a61f1ac8337b531", "gitHead": "3a43d7e563314bf32970b773dd31ecf2b90813dd", "scripts": {"test": "grunt && grunt test", "build": "npm install && grunt", "start": "grunt watch"}, "_npmUser": {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}, "deprecated": "This version is deprecated. Please upgrade to the latest version or find support at https://www.herodevs.com/support/jquery-nes.", "repository": {"url": "git+https://github.com/jquery/jquery.git", "type": "git"}, "_npmVersion": "3.7.3", "description": "JavaScript library for DOM operations", "directories": {}, "_nodeVersion": "5.8.0", "commitplease": {"components": ["Docs", "Tests", "Build", "Release", "Core", "Ajax", "Attributes", "Callbacks", "CSS", "Data", "Deferred", "Deprecated", "Dimensions", "Effects", "Event", "Manipulation", "Offset", "Queue", "Selector", "Serialize", "Traversing", "Wrap"]}, "dependencies": {}, "devDependencies": {"npm": "2.1.12", "grunt": "0.4.5", "jsdom": "5.6.1", "sinon": "1.12.2", "sizzle": "2.2.1", "core-js": "0.9.17", "gzip-js": "0.3.2", "qunitjs": "1.17.1", "grunt-cli": "0.1.13", "requirejs": "2.1.17", "testswarm": "1.1.0", "win-spawn": "2.0.0", "grunt-jscs": "2.1.0", "grunt-babel": "5.0.1", "commitplease": "2.0.0", "grunt-npmcopy": "0.1.0", "grunt-jsonlint": "1.0.4", "load-grunt-tasks": "1.0.0", "grunt-git-authors": "2.0.1", "qunit-assert-step": "1.0.3", "grunt-compare-size": "0.4.0", "grunt-contrib-watch": "0.6.1", "strip-json-comments": "1.0.3", "grunt-contrib-jshint": "0.11.2", "grunt-contrib-uglify": "0.9.2"}, "_npmOperationalInternal": {"tmp": "tmp/jquery-1.12.3.tgz_1459884094815_0.5328964435029775", "host": "packages-12-west.internal.npmjs.com"}}, "2.2.3": {"name": "j<PERSON>y", "version": "2.2.3", "keywords": ["j<PERSON>y", "javascript", "browser", "library"], "author": {"url": "https://github.com/jquery/jquery/blob/2.2.3/AUTHORS.txt", "name": "jQuery Foundation and other contributors"}, "license": "MIT", "_id": "jquery@2.2.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "scott.gonza<PERSON>z", "email": "<EMAIL>"}, {"name": "m_gol", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}], "homepage": "http://jquery.com", "bugs": {"url": "https://github.com/jquery/jquery/issues"}, "dist": {"shasum": "45e07e4190334de36c9e1a64b43b1f1373d91758", "tarball": "https://registry.npmjs.org/jquery/-/jquery-2.2.3.tgz", "integrity": "sha512-l0jtsiw1HL3My+7qvDUXuLBL/IbNjFexfzzHmAa/cNhzbhO2Jjme0cIHzRb/cnu9acXHFQLcLQEWzGW+Fk/EZg==", "signatures": [{"sig": "MEUCIQCV3AkBbPnL//uOczIdTiCB/EtjMDc3KipX0PjHcprllAIgJFT2B1yi7uu/+JIdsAIHqbOkNmDmk5YFxuFiVmqHWs0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/jquery.js", "_from": ".", "title": "j<PERSON><PERSON><PERSON>", "_shasum": "45e07e4190334de36c9e1a64b43b1f1373d91758", "gitHead": "af22a351b2ea5801ffb1695abb3bb34d5bed9198", "scripts": {"test": "grunt && grunt test", "build": "npm install && grunt", "start": "grunt watch"}, "_npmUser": {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}, "deprecated": "This version is deprecated. Please upgrade to the latest version or find support at https://www.herodevs.com/support/jquery-nes.", "repository": {"url": "git+https://github.com/jquery/jquery.git", "type": "git"}, "_npmVersion": "3.7.3", "description": "JavaScript library for DOM operations", "directories": {}, "_nodeVersion": "5.8.0", "commitplease": {"components": ["Docs", "Tests", "Build", "Release", "Core", "Ajax", "Attributes", "Callbacks", "CSS", "Data", "Deferred", "Deprecated", "Dimensions", "Effects", "Event", "Manipulation", "Offset", "Queue", "Selector", "Serialize", "Traversing", "Wrap"]}, "dependencies": {}, "devDependencies": {"grunt": "0.4.5", "jsdom": "5.6.1", "sinon": "1.10.3", "sizzle": "2.2.1", "core-js": "0.9.17", "gzip-js": "0.3.2", "qunitjs": "1.17.1", "grunt-cli": "0.1.13", "requirejs": "2.1.17", "testswarm": "1.1.0", "win-spawn": "2.0.0", "grunt-jscs": "2.1.0", "grunt-babel": "5.0.1", "commitplease": "2.0.0", "grunt-npmcopy": "0.1.0", "grunt-jsonlint": "1.0.4", "load-grunt-tasks": "1.0.0", "grunt-git-authors": "2.0.1", "qunit-assert-step": "1.0.3", "grunt-compare-size": "0.4.0", "grunt-contrib-watch": "0.6.1", "strip-json-comments": "1.0.3", "grunt-contrib-jshint": "0.11.2", "grunt-contrib-uglify": "0.9.2"}, "_npmOperationalInternal": {"tmp": "tmp/jquery-2.2.3.tgz_1459884434885_0.992488760035485", "host": "packages-12-west.internal.npmjs.com"}}, "1.12.4": {"name": "j<PERSON>y", "version": "1.12.4", "keywords": ["j<PERSON>y", "javascript", "browser", "library"], "author": {"url": "https://github.com/jquery/jquery/blob/1.12-stable/AUTHORS.txt", "name": "jQuery Foundation and other contributors"}, "license": "MIT", "_id": "jquery@1.12.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "scott.gonza<PERSON>z", "email": "<EMAIL>"}, {"name": "m_gol", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}], "homepage": "http://jquery.com", "bugs": {"url": "https://github.com/jquery/jquery/issues"}, "dist": {"shasum": "01e1dfba290fe73deba77ceeacb0f9ba2fec9e0c", "tarball": "https://registry.npmjs.org/jquery/-/jquery-1.12.4.tgz", "integrity": "sha512-UEVp7PPK9xXYSk8xqXCJrkXnKZtlgWkd2GsAQbMRFK6S/ePU2JN5G2Zum8hIVjzR3CpdfSqdqAzId/xd4TJHeg==", "signatures": [{"sig": "MEUCIDHisx/rZUFX6b5CQPCn886s2VbV2l/fwSSbZarrlKTSAiEAtblO5Ck0v0hfSWOcWdXYV84UG46rtdA09k7XBlVzZi8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/jquery.js", "_from": ".", "title": "j<PERSON><PERSON><PERSON>", "_shasum": "01e1dfba290fe73deba77ceeacb0f9ba2fec9e0c", "gitHead": "5e89585e0121e72ff47de177c5ef604f3089a53d", "scripts": {"test": "grunt && grunt test", "build": "npm install && grunt", "start": "grunt watch"}, "_npmUser": {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}, "deprecated": "This version is deprecated. Please upgrade to the latest version or find support at https://www.herodevs.com/support/jquery-nes.", "repository": {"url": "git+https://github.com/jquery/jquery.git", "type": "git"}, "_npmVersion": "2.15.1", "description": "JavaScript library for DOM operations", "directories": {}, "_nodeVersion": "0.10.45", "commitplease": {"components": ["Docs", "Tests", "Build", "Release", "Core", "Ajax", "Attributes", "Callbacks", "CSS", "Data", "Deferred", "Deprecated", "Dimensions", "Effects", "Event", "Manipulation", "Offset", "Queue", "Selector", "Serialize", "Traversing", "Wrap"]}, "dependencies": {}, "devDependencies": {"npm": "2.1.12", "grunt": "0.4.5", "jsdom": "5.6.1", "sinon": "1.12.2", "sizzle": "2.2.1", "core-js": "0.9.17", "gzip-js": "0.3.2", "qunitjs": "1.17.1", "grunt-cli": "0.1.13", "requirejs": "2.1.17", "testswarm": "1.1.0", "win-spawn": "2.0.0", "grunt-jscs": "2.1.0", "grunt-babel": "5.0.1", "commitplease": "2.0.0", "grunt-npmcopy": "0.1.0", "grunt-jsonlint": "1.0.4", "load-grunt-tasks": "1.0.0", "grunt-git-authors": "2.0.1", "qunit-assert-step": "1.0.3", "grunt-compare-size": "0.4.0", "grunt-contrib-watch": "0.6.1", "strip-json-comments": "1.0.3", "grunt-contrib-jshint": "0.11.2", "grunt-contrib-uglify": "0.9.2"}, "_npmOperationalInternal": {"tmp": "tmp/jquery-1.12.4.tgz_1463764744844_0.4810373710934073", "host": "packages-12-west.internal.npmjs.com"}}, "2.2.4": {"name": "j<PERSON>y", "version": "2.2.4", "keywords": ["j<PERSON>y", "javascript", "browser", "library"], "author": {"url": "https://github.com/jquery/jquery/blob/2.2.4/AUTHORS.txt", "name": "jQuery Foundation and other contributors"}, "license": "MIT", "_id": "jquery@2.2.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "scott.gonza<PERSON>z", "email": "<EMAIL>"}, {"name": "m_gol", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}], "homepage": "http://jquery.com", "bugs": {"url": "https://github.com/jquery/jquery/issues"}, "dist": {"shasum": "2c89d6889b5eac522a7eea32c14521559c6cbf02", "tarball": "https://registry.npmjs.org/jquery/-/jquery-2.2.4.tgz", "integrity": "sha512-lBHj60ezci2u1v2FqnZIraShGgEXq35qCzMv4lITyHGppTnA13rwR0MgwyNJh9TnDs3aXUvd1xjAotfraMHX/Q==", "signatures": [{"sig": "MEUCIQC3l9kAhdiLPRkfEWF306UI8+Osk6NpMrOW7fI/gFrnHAIgIeBsx9KIqt53H+E7kF4I37R+//Zhd8i4IFRKjbW4uiA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/jquery.js", "_from": ".", "title": "j<PERSON><PERSON><PERSON>", "_shasum": "2c89d6889b5eac522a7eea32c14521559c6cbf02", "gitHead": "c0185ab7c75aab88762c5aae780b9d83b80eda72", "scripts": {"test": "grunt && grunt test", "build": "npm install && grunt", "start": "grunt watch"}, "_npmUser": {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}, "deprecated": "This version is deprecated. Please upgrade to the latest version or find support at https://www.herodevs.com/support/jquery-nes.", "repository": {"url": "git+https://github.com/jquery/jquery.git", "type": "git"}, "_npmVersion": "2.15.1", "description": "JavaScript library for DOM operations", "directories": {}, "_nodeVersion": "0.10.45", "commitplease": {"components": ["Docs", "Tests", "Build", "Release", "Core", "Ajax", "Attributes", "Callbacks", "CSS", "Data", "Deferred", "Deprecated", "Dimensions", "Effects", "Event", "Manipulation", "Offset", "Queue", "Selector", "Serialize", "Traversing", "Wrap"]}, "dependencies": {}, "devDependencies": {"grunt": "0.4.5", "jsdom": "5.6.1", "sinon": "1.10.3", "sizzle": "2.2.1", "core-js": "0.9.17", "gzip-js": "0.3.2", "qunitjs": "1.17.1", "grunt-cli": "0.1.13", "requirejs": "2.1.17", "testswarm": "1.1.0", "win-spawn": "2.0.0", "grunt-jscs": "2.1.0", "grunt-babel": "5.0.1", "commitplease": "2.0.0", "grunt-npmcopy": "0.1.0", "grunt-jsonlint": "1.0.4", "load-grunt-tasks": "1.0.0", "grunt-git-authors": "2.0.1", "qunit-assert-step": "1.0.3", "grunt-compare-size": "0.4.0", "grunt-contrib-watch": "0.6.1", "strip-json-comments": "1.0.3", "grunt-contrib-jshint": "0.11.2", "grunt-contrib-uglify": "0.9.2"}, "_npmOperationalInternal": {"tmp": "tmp/jquery-2.2.4.tgz_1463765166836_0.5834389675874263", "host": "packages-16-east.internal.npmjs.com"}}, "3.0.0-rc1": {"name": "j<PERSON>y", "version": "3.0.0-rc1", "keywords": ["j<PERSON>y", "javascript", "browser", "library"], "author": {"url": "https://github.com/jquery/jquery/blob/3.0.0-rc1/AUTHORS.txt", "name": "jQuery Foundation and other contributors"}, "license": "MIT", "_id": "jquery@3.0.0-rc1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "scott.gonza<PERSON>z", "email": "<EMAIL>"}, {"name": "m_gol", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}], "homepage": "https://jquery.com", "bugs": {"url": "https://github.com/jquery/jquery/issues"}, "dist": {"shasum": "d69fc540b0a56be13e8aecde5a8766ade7a44f8e", "tarball": "https://registry.npmjs.org/jquery/-/jquery-3.0.0-rc1.tgz", "integrity": "sha512-QG+iUpFCwjRcBMzd48ecTOPJ6YPwekuyAtvOM8xJdc+f4zXrB5KabUrZSWAANo8mQJAab/0lIM9E9Y5umj6Slw==", "signatures": [{"sig": "MEUCIQCBxhhvTwThBjDwxv9sXuHpTukklSEoVWXQiJrNs0TTDAIgMwxxM+fcJGLfcqFnCOeh4ebBGro9V/8oRflqe9CuSqo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/jquery.js", "_from": ".", "title": "j<PERSON><PERSON><PERSON>", "_shasum": "d69fc540b0a56be13e8aecde5a8766ade7a44f8e", "gitHead": "e503a93188dc4b5b42e2340f805f2d90b404bc50", "scripts": {"test": "grunt && grunt test", "build": "npm install && grunt", "start": "grunt watch", "precommit": "grunt precommit_lint"}, "_npmUser": {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}, "deprecated": "This version is deprecated. Please upgrade to the latest version or find support at https://www.herodevs.com/support/jquery-nes.", "repository": {"url": "git+https://github.com/jquery/jquery.git", "type": "git"}, "_npmVersion": "2.15.1", "description": "JavaScript library for DOM operations", "directories": {}, "_nodeVersion": "0.10.45", "commitplease": {"components": ["Docs", "Tests", "Build", "Support", "Release", "Core", "Ajax", "Attributes", "Callbacks", "CSS", "Data", "Deferred", "Deprecated", "Dimensions", "Effects", "Event", "Manipulation", "Offset", "Queue", "Selector", "Serialize", "Traversing", "Wrap"]}, "dependencies": {}, "devDependencies": {"q": "1.4.1", "grunt": "1.0.1", "husky": "0.11.4", "jsdom": "5.6.1", "sinon": "1.17.3", "sizzle": "2.3.0", "core-js": "2.2.2", "gzip-js": "0.3.2", "insight": "0.8.1", "qunitjs": "1.23.1", "grunt-cli": "1.2.0", "requirejs": "2.2.0", "testswarm": "1.1.0", "grunt-jscs": "2.8.0", "cross-spawn": "2.2.3", "grunt-babel": "6.0.0", "grunt-newer": "1.2.0", "commitplease": "2.3.1", "grunt-npmcopy": "0.1.0", "grunt-jsonlint": "1.0.7", "load-grunt-tasks": "3.5.0", "grunt-git-authors": "3.2.0", "qunit-assert-step": "1.0.3", "grunt-compare-size": "0.4.2", "babel-preset-es2015": "6.6.0", "grunt-contrib-watch": "1.0.0", "native-promise-only": "0.8.1", "strip-json-comments": "2.0.1", "grunt-contrib-jshint": "1.0.0", "grunt-contrib-uglify": "1.0.1", "promises-aplus-tests": "2.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/jquery-3.0.0-rc1.tgz_1463771627380_0.12211154378019273", "host": "packages-16-east.internal.npmjs.com"}}, "3.0.0": {"name": "j<PERSON>y", "version": "3.0.0", "keywords": ["j<PERSON>y", "javascript", "browser", "library"], "author": {"url": "https://github.com/jquery/jquery/blob/3.0.0/AUTHORS.txt", "name": "jQuery Foundation and other contributors"}, "license": "MIT", "_id": "jquery@3.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "scott.gonza<PERSON>z", "email": "<EMAIL>"}, {"name": "m_gol", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}], "homepage": "https://jquery.com", "bugs": {"url": "https://github.com/jquery/jquery/issues"}, "dist": {"shasum": "95a2a9541291a9f819e016f85ba247116d03e4ab", "tarball": "https://registry.npmjs.org/jquery/-/jquery-3.0.0.tgz", "integrity": "sha512-jECOt1lOmmGYGL44KEMokUD0C6aVODXaF7NuFux3BWUTtvJ5Rdy7vUZu3q4ip4uSZI8gNqAm5dVk75YnoyYu4Q==", "signatures": [{"sig": "MEQCIDJluPuDdR6dmH+wpOxq2S+ewll2dYDafDiEr6mBa5iIAiA4n+BAW2EBUzw+xqSRtqgIowqaiper9yidRvUmKpNhzw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/jquery.js", "_from": ".", "title": "j<PERSON><PERSON><PERSON>", "_shasum": "95a2a9541291a9f819e016f85ba247116d03e4ab", "gitHead": "0078f86be166a8747819d5d1516776a662cb69df", "scripts": {"test": "grunt && grunt test", "build": "npm install && grunt", "start": "grunt watch", "precommit": "grunt precommit_lint"}, "_npmUser": {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}, "deprecated": "This version is deprecated. Please upgrade to the latest version or find support at https://www.herodevs.com/support/jquery-nes.", "repository": {"url": "git+https://github.com/jquery/jquery.git", "type": "git"}, "_npmVersion": "2.15.1", "description": "JavaScript library for DOM operations", "directories": {}, "_nodeVersion": "0.10.45", "commitplease": {"components": ["Docs", "Tests", "Build", "Support", "Release", "Core", "Ajax", "Attributes", "Callbacks", "CSS", "Data", "Deferred", "Deprecated", "Dimensions", "Effects", "Event", "Manipulation", "Offset", "Queue", "Selector", "Serialize", "Traversing", "Wrap"]}, "dependencies": {}, "devDependencies": {"q": "1.4.1", "grunt": "1.0.1", "husky": "0.11.4", "jsdom": "5.6.1", "sinon": "1.17.3", "sizzle": "2.3.0", "core-js": "2.2.2", "gzip-js": "0.3.2", "insight": "0.8.1", "qunitjs": "1.23.1", "grunt-cli": "1.2.0", "requirejs": "2.2.0", "testswarm": "1.1.0", "grunt-jscs": "2.8.0", "cross-spawn": "2.2.3", "grunt-babel": "6.0.0", "grunt-newer": "1.2.0", "commitplease": "2.3.1", "grunt-npmcopy": "0.1.0", "grunt-jsonlint": "1.0.7", "load-grunt-tasks": "3.5.0", "grunt-git-authors": "3.2.0", "qunit-assert-step": "1.0.3", "grunt-compare-size": "0.4.2", "babel-preset-es2015": "6.6.0", "grunt-contrib-watch": "1.0.0", "native-promise-only": "0.8.1", "strip-json-comments": "2.0.1", "grunt-contrib-jshint": "1.0.0", "grunt-contrib-uglify": "1.0.1", "promises-aplus-tests": "2.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/jquery-3.0.0.tgz_1465497191024_0.9057256667874753", "host": "packages-16-east.internal.npmjs.com"}}, "3.1.0": {"name": "j<PERSON>y", "version": "3.1.0", "keywords": ["j<PERSON>y", "javascript", "browser", "library"], "author": {"url": "https://github.com/jquery/jquery/blob/3.1.0/AUTHORS.txt", "name": "jQuery Foundation and other contributors"}, "license": "MIT", "_id": "jquery@3.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "scott.gonza<PERSON>z", "email": "<EMAIL>"}, {"name": "m_gol", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}], "homepage": "https://jquery.com", "bugs": {"url": "https://github.com/jquery/jquery/issues"}, "dist": {"shasum": "129f6f1ae94b18f09010b008d0d6011e40613d7f", "tarball": "https://registry.npmjs.org/jquery/-/jquery-3.1.0.tgz", "integrity": "sha512-oPgbA8XiMd/HDs0MI1mrFsq7lwLvTOYA9eh5YIl0sGlmhbEAfEqnTBuDsRqr+yXr7XWeKM39QSzucRT5CNKQOA==", "signatures": [{"sig": "MEUCIQDYeGSJq1JeWGmW+77htw+bJOCWI1b951kvbzZihQ6l0AIgSWZnsQdh3G/pZvE8k8FrioiJFAt0HwVLA8Mr42Apkyw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/jquery.js", "_from": ".", "title": "j<PERSON><PERSON><PERSON>", "_shasum": "129f6f1ae94b18f09010b008d0d6011e40613d7f", "gitHead": "6f02bc382c0529d3b4f68f6b2ad21876642dbbfe", "scripts": {"test": "grunt && grunt test", "build": "npm install && grunt", "start": "grunt watch", "precommit": "grunt precommit_lint"}, "_npmUser": {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}, "deprecated": "This version is deprecated. Please upgrade to the latest version or find support at https://www.herodevs.com/support/jquery-nes.", "repository": {"url": "git+https://github.com/jquery/jquery.git", "type": "git"}, "_npmVersion": "2.15.8", "description": "JavaScript library for DOM operations", "directories": {}, "_nodeVersion": "4.4.7", "commitplease": {"components": ["Docs", "Tests", "Build", "Support", "Release", "Core", "Ajax", "Attributes", "Callbacks", "CSS", "Data", "Deferred", "Deprecated", "Dimensions", "Effects", "Event", "Manipulation", "Offset", "Queue", "Selector", "Serialize", "Traversing", "Wrap"]}, "dependencies": {}, "devDependencies": {"q": "1.4.1", "grunt": "1.0.1", "husky": "0.11.4", "jsdom": "5.6.1", "sinon": "1.17.3", "sizzle": "2.3.0", "core-js": "2.2.2", "gzip-js": "0.3.2", "insight": "0.8.1", "qunitjs": "1.23.1", "grunt-cli": "1.2.0", "requirejs": "2.2.0", "testswarm": "1.1.0", "cross-spawn": "2.2.3", "grunt-babel": "6.0.0", "grunt-newer": "1.2.0", "commitplease": "2.3.1", "grunt-eslint": "18.1.0", "grunt-npmcopy": "0.1.0", "grunt-jsonlint": "1.0.7", "load-grunt-tasks": "3.5.0", "grunt-git-authors": "3.2.0", "qunit-assert-step": "1.0.3", "grunt-compare-size": "0.4.2", "babel-preset-es2015": "6.6.0", "grunt-contrib-watch": "1.0.0", "native-promise-only": "0.8.1", "strip-json-comments": "2.0.1", "eslint-config-jquery": "0.1.6", "grunt-contrib-uglify": "1.0.1", "promises-aplus-tests": "2.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/jquery-3.1.0.tgz_1467927964329_0.882518710102886", "host": "packages-16-east.internal.npmjs.com"}}, "3.1.1": {"name": "j<PERSON>y", "version": "3.1.1", "keywords": ["j<PERSON>y", "javascript", "browser", "library"], "author": {"url": "https://github.com/jquery/jquery/blob/3.1.1/AUTHORS.txt", "name": "jQuery Foundation and other contributors"}, "license": "MIT", "_id": "jquery@3.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "scott.gonza<PERSON>z", "email": "<EMAIL>"}, {"name": "m_gol", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}], "homepage": "https://jquery.com", "bugs": {"url": "https://github.com/jquery/jquery/issues"}, "dist": {"shasum": "347c1c21c7e004115e0a4da32cece041fad3c8a3", "tarball": "https://registry.npmjs.org/jquery/-/jquery-3.1.1.tgz", "integrity": "sha512-zuiWRmx65Kd1oFGdWLvAjbsumdOBFvs3r1vuqCyKFF79R7exBHNbiNEJ8LR+xkzgfxqBznMD/4Cv6PHXS6mvEw==", "signatures": [{"sig": "MEMCH1cI4pzbOdeKkchJ1ktKuSQrTd6R3uXc7HYd3MvioqcCIGCbANYrKhj7K4QNgnZW/gL8k8SyAMXkVsDjutMVi07x", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/jquery.js", "_from": ".", "title": "j<PERSON><PERSON><PERSON>", "_shasum": "347c1c21c7e004115e0a4da32cece041fad3c8a3", "gitHead": "1b30f3ad466ebf2714d47eda34dbd7fdf6849fe3", "scripts": {"test": "grunt && grunt test:slow", "build": "npm install && grunt", "start": "grunt watch", "commitmsg": "node node_modules/commitplease", "precommit": "grunt lint:newer"}, "_npmUser": {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}, "deprecated": "This version is deprecated. Please upgrade to the latest version or find support at https://www.herodevs.com/support/jquery-nes.", "repository": {"url": "git+https://github.com/jquery/jquery.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "JavaScript library for DOM operations", "directories": {}, "_nodeVersion": "6.6.0", "commitplease": {"nohook": true, "components": ["Docs", "Tests", "Build", "Support", "Release", "Core", "Ajax", "Attributes", "Callbacks", "CSS", "Data", "Deferred", "Deprecated", "Dimensions", "Effects", "Event", "Manipulation", "Offset", "Queue", "Selector", "Serialize", "Traversing", "Wrap"], "markerPattern": "^((clos|fix|resolv)(e[sd]|ing))|(refs?)", "ticketPattern": "^((Closes|Fixes) ([a-zA-Z]{2,}-)[0-9]+)|(Refs? [^#])"}, "dependencies": {}, "devDependencies": {"q": "1.4.1", "grunt": "1.0.1", "husky": "0.11.4", "jsdom": "5.6.1", "sinon": "1.17.3", "sizzle": "2.3.3", "core-js": "2.2.2", "gzip-js": "0.3.2", "insight": "0.8.1", "qunitjs": "1.23.1", "grunt-cli": "1.2.0", "requirejs": "2.2.0", "testswarm": "1.1.0", "cross-spawn": "2.2.3", "grunt-babel": "6.0.0", "grunt-newer": "1.2.0", "commitplease": "2.6.1", "grunt-eslint": "19.0.0", "grunt-npmcopy": "0.1.0", "grunt-jsonlint": "1.0.7", "load-grunt-tasks": "3.5.0", "grunt-git-authors": "3.2.0", "qunit-assert-step": "1.0.3", "grunt-compare-size": "0.4.2", "babel-preset-es2015": "6.6.0", "grunt-contrib-watch": "1.0.0", "native-promise-only": "0.8.1", "strip-json-comments": "2.0.1", "eslint-config-jquery": "1.0.0", "grunt-contrib-uglify": "1.0.1", "promises-aplus-tests": "2.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/jquery-3.1.1.tgz_1474583566957_0.15473420196212828", "host": "packages-12-west.internal.npmjs.com"}}, "3.2.0": {"name": "j<PERSON>y", "version": "3.2.0", "keywords": ["j<PERSON>y", "javascript", "browser", "library"], "author": {"url": "https://github.com/jquery/jquery/blob/3.2.0/AUTHORS.txt", "name": "JS Foundation and other contributors"}, "license": "MIT", "_id": "jquery@3.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mgol", "email": "<EMAIL>"}, {"name": "scott.gonza<PERSON>z", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}], "homepage": "https://jquery.com", "bugs": {"url": "https://github.com/jquery/jquery/issues"}, "dist": {"shasum": "3bdbba66e1eee0785532dddadb0e0d2521ca584b", "tarball": "https://registry.npmjs.org/jquery/-/jquery-3.2.0.tgz", "integrity": "sha512-WsYJ4O1VHy0/8YeiMw0+DrRgqxQK9yzzem/kELh5abmIIm6aKd+6xq719wv5n07yPgV1jYydLpdyCjUQsBd4CA==", "signatures": [{"sig": "MEYCIQCKBt4mfIY56h143MfZhwQj1HCdPHtDqGg3ugadCRD8SgIhAPPrl3OStNHsKUI/b6sUaTULPNVciL2kV6krPI7ORzHc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/jquery.js", "_from": ".", "title": "j<PERSON><PERSON><PERSON>", "_shasum": "3bdbba66e1eee0785532dddadb0e0d2521ca584b", "gitHead": "a81259fff4ea0c7b4cd98f04050c829640395a31", "scripts": {"test": "grunt && grunt test:slow", "build": "npm install && grunt", "start": "grunt watch", "commitmsg": "node node_modules/commitplease", "precommit": "grunt lint:newer"}, "_npmUser": {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}, "deprecated": "This version is deprecated. Please upgrade to the latest version or find support at https://www.herodevs.com/support/jquery-nes.", "repository": {"url": "git+https://github.com/jquery/jquery.git", "type": "git"}, "_npmVersion": "4.1.2", "description": "JavaScript library for DOM operations", "directories": {}, "_nodeVersion": "7.7.3", "commitplease": {"nohook": true, "components": ["Docs", "Tests", "Build", "Support", "Release", "Core", "Ajax", "Attributes", "Callbacks", "CSS", "Data", "Deferred", "Deprecated", "Dimensions", "Effects", "Event", "Manipulation", "Offset", "Queue", "Selector", "Serialize", "Traversing", "Wrap"], "markerPattern": "^((clos|fix|resolv)(e[sd]|ing))|^(refs?)", "ticketPattern": "^((Closes|Fixes) ([a-zA-Z]{2,}-)[0-9]+)|^(Refs? [^#])"}, "dependencies": {}, "devDependencies": {"q": "1.4.1", "grunt": "1.0.1", "husky": "0.11.4", "jsdom": "5.6.1", "sinon": "1.17.3", "sizzle": "2.3.3", "core-js": "2.2.2", "gzip-js": "0.3.2", "insight": "0.8.1", "qunitjs": "1.23.1", "grunt-cli": "1.2.0", "requirejs": "2.2.0", "testswarm": "1.1.0", "cross-spawn": "2.2.3", "grunt-babel": "6.0.0", "grunt-newer": "1.2.0", "commitplease": "2.6.1", "grunt-eslint": "19.0.0", "grunt-npmcopy": "0.1.0", "grunt-jsonlint": "1.0.7", "load-grunt-tasks": "3.5.0", "grunt-git-authors": "3.2.0", "qunit-assert-step": "1.0.3", "grunt-compare-size": "0.4.2", "babel-preset-es2015": "6.6.0", "grunt-contrib-watch": "1.0.0", "native-promise-only": "0.8.1", "strip-json-comments": "2.0.1", "eslint-config-jquery": "1.0.0", "grunt-contrib-uglify": "1.0.1", "promises-aplus-tests": "2.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/jquery-3.2.0.tgz_1489699855733_0.5328386940527707", "host": "packages-18-east.internal.npmjs.com"}}, "3.2.1": {"name": "j<PERSON>y", "version": "3.2.1", "keywords": ["j<PERSON>y", "javascript", "browser", "library"], "author": {"url": "https://github.com/jquery/jquery/blob/3.2.1/AUTHORS.txt", "name": "JS Foundation and other contributors"}, "license": "MIT", "_id": "jquery@3.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mgol", "email": "<EMAIL>"}, {"name": "scott.gonza<PERSON>z", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}], "homepage": "https://jquery.com", "bugs": {"url": "https://github.com/jquery/jquery/issues"}, "dist": {"shasum": "5c4d9de652af6cd0a770154a631bba12b015c787", "tarball": "https://registry.npmjs.org/jquery/-/jquery-3.2.1.tgz", "integrity": "sha512-iQUctXqe/nSa7hshPkQnJaJEUfxM139//hg2nJj+wsqVvd/YgXALR3jTNGh7BylgsyfcC8r4i2cJrClGBkDu5Q==", "signatures": [{"sig": "MEUCIBi5j6+xwte7iNc+d1dQPRa5nERksrLkpr0Eu6+78UaGAiEAlNeWmTbV40sENmZSE+f4/bXHqVRU42/kTaZBwC0A2ec=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/jquery.js", "_from": ".", "title": "j<PERSON><PERSON><PERSON>", "_shasum": "5c4d9de652af6cd0a770154a631bba12b015c787", "gitHead": "77d2a51d0520d2ee44173afdf4e40a9201f5964e", "scripts": {"test": "grunt && grunt test:slow", "build": "npm install && grunt", "start": "grunt watch", "commitmsg": "node node_modules/commitplease", "precommit": "grunt lint:newer"}, "_npmUser": {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}, "deprecated": "This version is deprecated. Please upgrade to the latest version or find support at https://www.herodevs.com/support/jquery-nes.", "repository": {"url": "git+https://github.com/jquery/jquery.git", "type": "git"}, "_npmVersion": "4.4.4", "description": "JavaScript library for DOM operations", "directories": {}, "_nodeVersion": "7.7.3", "commitplease": {"nohook": true, "components": ["Docs", "Tests", "Build", "Support", "Release", "Core", "Ajax", "Attributes", "Callbacks", "CSS", "Data", "Deferred", "Deprecated", "Dimensions", "Effects", "Event", "Manipulation", "Offset", "Queue", "Selector", "Serialize", "Traversing", "Wrap"], "markerPattern": "^((clos|fix|resolv)(e[sd]|ing))|^(refs?)", "ticketPattern": "^((Closes|Fixes) ([a-zA-Z]{2,}-)[0-9]+)|^(Refs? [^#])"}, "dependencies": {}, "devDependencies": {"q": "1.4.1", "grunt": "1.0.1", "husky": "0.11.4", "jsdom": "5.6.1", "sinon": "1.17.3", "sizzle": "2.3.3", "core-js": "2.2.2", "gzip-js": "0.3.2", "insight": "0.8.1", "qunitjs": "1.23.1", "grunt-cli": "1.2.0", "requirejs": "2.2.0", "testswarm": "1.1.0", "cross-spawn": "2.2.3", "grunt-babel": "6.0.0", "grunt-newer": "1.2.0", "commitplease": "2.6.1", "grunt-eslint": "19.0.0", "grunt-npmcopy": "0.1.0", "grunt-jsonlint": "1.0.7", "load-grunt-tasks": "3.5.0", "grunt-git-authors": "3.2.0", "qunit-assert-step": "1.0.3", "grunt-compare-size": "0.4.2", "babel-preset-es2015": "6.6.0", "grunt-contrib-watch": "1.0.0", "native-promise-only": "0.8.1", "strip-json-comments": "2.0.1", "eslint-config-jquery": "1.0.0", "grunt-contrib-uglify": "1.0.1", "promises-aplus-tests": "2.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/jquery-3.2.1.tgz_1490036530067_0.19497186387889087", "host": "packages-12-west.internal.npmjs.com"}}, "3.3.0": {"name": "j<PERSON>y", "version": "3.3.0", "keywords": ["j<PERSON>y", "javascript", "browser", "library"], "author": {"url": "https://github.com/jquery/jquery/blob/3.3.0/AUTHORS.txt", "name": "JS Foundation and other contributors"}, "license": "MIT", "_id": "jquery@3.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mgol", "email": "<EMAIL>"}, {"name": "scott.gonza<PERSON>z", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}], "homepage": "https://jquery.com", "bugs": {"url": "https://github.com/jquery/jquery/issues"}, "dist": {"shasum": "06004bc2d0204ce92822a794ee8efb50283bb9ff", "tarball": "https://registry.npmjs.org/jquery/-/jquery-3.3.0.tgz", "integrity": "sha512-1SmQFTqu24RtvnvLN/D1RFIsOBGqLQYsGJgZxejd69Rw9ACBJvSgppA+A+wBcXgASwRSoX1aDN1I5ZNIrFC6Xw==", "signatures": [{"sig": "MEYCIQD7T1asIN/xLJumyI8hJhu2gJYIcR3dtB39k0Hw8bTDCgIhAL5PPJRfYUvtr50rP0vhtdvo1ffAOilepeokEF5jpmnu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/jquery.js", "title": "j<PERSON><PERSON><PERSON>", "gitHead": "9a8a1c63930edc9fb6fab9e75b3eee578762b8a5", "scripts": {"test": "grunt && grunt test:slow && grunt karma:main", "build": "npm install && grunt", "start": "grunt watch", "jenkins": "npm run test:browserless", "commitmsg": "node node_modules/commitplease", "precommit": "grunt lint:newer qunit_fixture", "test:browser": "grunt && grunt karma:main", "test:browserless": "grunt && grunt test:slow"}, "_npmUser": {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}, "deprecated": "This version is deprecated. Please upgrade to the latest version or find support at https://www.herodevs.com/support/jquery-nes.", "repository": {"url": "git+https://github.com/jquery/jquery.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "JavaScript library for DOM operations", "directories": {}, "_nodeVersion": "9.3.0", "commitplease": {"nohook": true, "components": ["Docs", "Tests", "Build", "Support", "Release", "Core", "Ajax", "Attributes", "Callbacks", "CSS", "Data", "Deferred", "Deprecated", "Dimensions", "Effects", "Event", "Manipulation", "Offset", "Queue", "Selector", "Serialize", "Traversing", "Wrap"], "markerPattern": "^((clos|fix|resolv)(e[sd]|ing))|^(refs?)", "ticketPattern": "^((Closes|Fixes) ([a-zA-Z]{2,}-)[0-9]+)|^(Refs? [^#])"}, "dependencies": {"npm": "4.4.1", "chalk": "1.1.3", "shelljs": "0.7.7", "archiver": "1.3.0"}, "devDependencies": {"q": "1.5.0", "grunt": "1.0.1", "husky": "0.14.3", "jsdom": "5.6.1", "karma": "1.7.0", "sinon": "2.3.7", "sizzle": "2.3.3", "core-js": "2.4.1", "gzip-js": "0.3.2", "insight": "0.8.4", "qunitjs": "1.23.1", "raw-body": "2.2.0", "grunt-cli": "1.2.0", "requirejs": "2.3.3", "testswarm": "1.1.0", "uglify-js": "3.3.4", "babel-core": "7.0.0-beta.0", "grunt-babel": "7.0.0", "grunt-karma": "2.0.0", "grunt-newer": "1.3.0", "karma-qunit": "1.2.1", "commitplease": "2.7.10", "grunt-eslint": "20.0.0", "grunt-npmcopy": "0.1.0", "grunt-jsonlint": "1.1.0", "load-grunt-tasks": "3.5.2", "grunt-git-authors": "3.2.0", "qunit-assert-step": "1.0.3", "grunt-compare-size": "0.4.2", "grunt-contrib-watch": "1.0.0", "native-promise-only": "0.8.1", "strip-json-comments": "2.0.1", "eslint-config-jquery": "1.0.1", "grunt-contrib-uglify": "3.0.1", "promises-aplus-tests": "2.1.2", "karma-chrome-launcher": "2.2.0", "karma-firefox-launcher": "1.0.1", "karma-browserstack-launcher": "1.3.0", "babel-plugin-transform-es2015-for-of": "7.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/jquery-3.3.0.tgz_1516388631205_0.827812286792323", "host": "s3://npm-registry-packages"}}, "3.3.1": {"name": "j<PERSON>y", "version": "3.3.1", "keywords": ["j<PERSON>y", "javascript", "browser", "library"], "author": {"url": "https://github.com/jquery/jquery/blob/3.3.1/AUTHORS.txt", "name": "JS Foundation and other contributors"}, "license": "MIT", "_id": "jquery@3.3.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mgol", "email": "<EMAIL>"}, {"name": "scott.gonza<PERSON>z", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}], "homepage": "https://jquery.com", "bugs": {"url": "https://github.com/jquery/jquery/issues"}, "dist": {"shasum": "958ce29e81c9790f31be7792df5d4d95fc57fbca", "tarball": "https://registry.npmjs.org/jquery/-/jquery-3.3.1.tgz", "integrity": "sha512-Ubldcmxp5np52/ENotGxlLe6aGMvmF4R8S6tZjsP6Knsaxd/xp3Zrh50cG93lR6nPXyUFwzN3ZSOQI0wRJNdGg==", "signatures": [{"sig": "MEYCIQC4KGUONy7Dh2MfTGMVGHEkLMbmH+fJDh40rWe11lJIJwIhAPlmTSbtaoARUjam5ocuUDdAoQocH3TGAdyNNh1MT51C", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/jquery.js", "title": "j<PERSON><PERSON><PERSON>", "gitHead": "9e8ec3d10fad04748176144f108d7355662ae75e", "scripts": {"test": "grunt && grunt test:slow && grunt karma:main", "build": "npm install && grunt", "start": "grunt watch", "jenkins": "npm run test:browserless", "commitmsg": "node node_modules/commitplease", "precommit": "grunt lint:newer qunit_fixture", "test:browser": "grunt && grunt karma:main", "test:browserless": "grunt && grunt test:slow"}, "_npmUser": {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}, "deprecated": "This version is deprecated. Please upgrade to the latest version or find support at https://www.herodevs.com/support/jquery-nes.", "repository": {"url": "git+https://github.com/jquery/jquery.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "JavaScript library for DOM operations", "directories": {}, "_nodeVersion": "9.3.0", "commitplease": {"nohook": true, "components": ["Docs", "Tests", "Build", "Support", "Release", "Core", "Ajax", "Attributes", "Callbacks", "CSS", "Data", "Deferred", "Deprecated", "Dimensions", "Effects", "Event", "Manipulation", "Offset", "Queue", "Selector", "Serialize", "Traversing", "Wrap"], "markerPattern": "^((clos|fix|resolv)(e[sd]|ing))|^(refs?)", "ticketPattern": "^((Closes|Fixes) ([a-zA-Z]{2,}-)[0-9]+)|^(Refs? [^#])"}, "dependencies": {}, "devDependencies": {"q": "1.5.0", "grunt": "1.0.1", "husky": "0.14.3", "jsdom": "5.6.1", "karma": "1.7.0", "sinon": "2.3.7", "sizzle": "2.3.3", "core-js": "2.4.1", "gzip-js": "0.3.2", "insight": "0.8.4", "qunitjs": "1.23.1", "raw-body": "2.2.0", "grunt-cli": "1.2.0", "requirejs": "2.3.3", "testswarm": "1.1.0", "uglify-js": "3.3.4", "babel-core": "7.0.0-beta.0", "grunt-babel": "7.0.0", "grunt-karma": "2.0.0", "grunt-newer": "1.3.0", "karma-qunit": "1.2.1", "commitplease": "2.7.10", "grunt-eslint": "20.0.0", "grunt-npmcopy": "0.1.0", "grunt-jsonlint": "1.1.0", "load-grunt-tasks": "3.5.2", "grunt-git-authors": "3.2.0", "qunit-assert-step": "1.0.3", "grunt-compare-size": "0.4.2", "grunt-contrib-watch": "1.0.0", "native-promise-only": "0.8.1", "strip-json-comments": "2.0.1", "eslint-config-jquery": "1.0.1", "grunt-contrib-uglify": "3.0.1", "promises-aplus-tests": "2.1.2", "karma-chrome-launcher": "2.2.0", "karma-firefox-launcher": "1.0.1", "karma-browserstack-launcher": "1.3.0", "babel-plugin-transform-es2015-for-of": "7.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/jquery-3.3.1.tgz_1516469230473_0.5458589680492878", "host": "s3://npm-registry-packages"}}, "3.4.0": {"name": "j<PERSON>y", "version": "3.4.0", "keywords": ["j<PERSON>y", "javascript", "browser", "library"], "author": {"url": "https://github.com/jquery/jquery/blob/3.4.0/AUTHORS.txt", "name": "JS Foundation and other contributors"}, "license": "MIT", "_id": "jquery@3.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mgol", "email": "<EMAIL>"}, {"name": "scott.gonza<PERSON>z", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}], "homepage": "https://jquery.com", "bugs": {"url": "https://github.com/jquery/jquery/issues"}, "dist": {"shasum": "8de513fa0fa4b2c7d2e48a530e26f0596936efdf", "tarball": "https://registry.npmjs.org/jquery/-/jquery-3.4.0.tgz", "fileCount": 125, "integrity": "sha512-ggRCXln9zEqv6OqAGXFEcshF5dSBvCkzj6Gm2gzuR5fWawaX8t7cxKVkkygKODrDAzKdoYw3l/e3pm3vlT4IbQ==", "signatures": [{"sig": "MEUCIBqknn/FO1i0MZzthgvQwog69ha6ZWs/eKjby4tgOhmQAiEAjtsxoiW6mnu84TmN6EJ4eSYFq8TAiUNFsB+datRFBM4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1293692, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcrkmiCRA9TVsSAnZWagAA0FYP/3PRUJ9bvMXsEo01m0m0\n4/24H91hGidu20i5V2JNBhtlNttTH3NLlPYKGHO3wHFEA8iAKuiYyo/V5UlQ\nv4jUKqcFCtaz2pgdBICbAt0cNc1ZbRxXTL1ZI1HublxTkyx/d0kP0l001vYA\nIeLarSNrx4749LDHOeprO6TPoFeB+aCusWbLexcNs/Tel9Frn0oufEiDTjCI\nHAJHfjGDW8IZBw0fQaem7CxaUPiVFRUZghxYpM5ThGbGtxIsAiN9EQDpDmVC\nq855FOu7FsLviN20mJpJmq9ZuiGF9DoUA5QJIQ9INZBfd/uQ6TWRcqBqVtwH\nIgdfAVAACPBF+Zax3dy4cAOU+UHGqs/nStZ7wmewexsDdXb3PzkDvUxluzmL\nocg6y7gpp39Ev7qV+SNg7ZccgW/2rWHfy/l/Aofb0TFV53FCc3DEc9avzUkS\nPZE2jCTu3mUR1vpqr31R+AVo2ZMhTfcrGw2WwP1IDqc+Nt+7ySBiWVQHnT4a\nIjHALqztNKEkqFvxgm4VUkPgqqpFF4msCg5a8jwjJPiGKJgllv1clVwlFk77\nKQHztESClmebfruOidrX0qmBpixrraC2T401gZT9bV4qfqu6Td3q00TBkZjI\nKsiBfs6hq3Py3MOJt3S2xwx8smuhB7Q05e5TMficC0QMURF14U4Yi6WGJ5or\nBJa5\r\n=w1RE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/jquery.js", "husky": {"hooks": {"commit-msg": "node node_modules/commitplease", "pre-commit": "grunt lint:newer qunit_fixture"}}, "title": "j<PERSON><PERSON><PERSON>", "gitHead": "180c5c33e477463ed93a9d5f7ac7cad7c9809f1c", "scripts": {"test": "grunt && grunt test:slow && grunt karma:main", "build": "npm install && grunt", "start": "grunt watch", "jenkins": "npm run test:browserless", "test:browser": "grunt && grunt karma:main", "test:browserless": "grunt && grunt test:slow"}, "_npmUser": {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}, "deprecated": "This version is deprecated. Please upgrade to the latest version or find support at https://www.herodevs.com/support/jquery-nes.", "repository": {"url": "git+https://github.com/jquery/jquery.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "JavaScript library for DOM operations", "directories": {}, "_nodeVersion": "8.15.1", "commitplease": {"nohook": true, "components": ["Docs", "Tests", "Build", "Support", "Release", "Core", "Ajax", "Attributes", "Callbacks", "CSS", "Data", "Deferred", "Deprecated", "Dimensions", "Effects", "Event", "Manipulation", "Offset", "Queue", "Selector", "Serialize", "Traversing", "Wrap"], "markerPattern": "^((clos|fix|resolv)(e[sd]|ing))|^(refs?)", "ticketPattern": "^((Closes|Fixes) ([a-zA-Z]{2,}-)[0-9]+)|^(Refs? [^#])"}, "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"q": "1.5.1", "grunt": "1.0.3", "husky": "1.3.1", "jsdom": "13.2.0", "karma": "4.0.1", "qunit": "2.9.2", "sinon": "2.3.7", "sizzle": "2.3.4", "core-js": "2.6.5", "gzip-js": "0.3.2", "insight": "0.10.1", "raw-body": "2.3.3", "grunt-cli": "1.3.2", "requirejs": "2.3.6", "testswarm": "1.1.0", "uglify-js": "3.4.7", "@babel/core": "7.3.3", "grunt-babel": "8.0.0", "grunt-karma": "3.0.1", "grunt-newer": "1.3.0", "karma-qunit": "3.0.0", "commitplease": "3.2.0", "grunt-eslint": "21.0.0", "grunt-npmcopy": "0.1.0", "grunt-jsonlint": "1.1.0", "load-grunt-tasks": "4.0.0", "grunt-git-authors": "3.2.0", "karma-ie-launcher": "1.0.0", "grunt-compare-size": "0.4.2", "grunt-contrib-watch": "1.1.0", "native-promise-only": "0.8.1", "strip-json-comments": "2.0.1", "eslint-config-jquery": "1.0.1", "grunt-contrib-uglify": "3.4.0", "karma-jsdom-launcher": "7.1.0", "promises-aplus-tests": "2.1.2", "karma-chrome-launcher": "2.2.0", "karma-firefox-launcher": "1.1.0", "karma-browserstack-launcher": "1.4.0", "@babel/plugin-transform-for-of": "7.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/jquery_3.4.0_1554925986191_0.6962955867267493", "host": "s3://npm-registry-packages"}}, "3.4.1": {"name": "j<PERSON>y", "version": "3.4.1", "keywords": ["j<PERSON>y", "javascript", "browser", "library"], "author": {"url": "https://github.com/jquery/jquery/blob/3.4.1/AUTHORS.txt", "name": "JS Foundation and other contributors"}, "license": "MIT", "_id": "jquery@3.4.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mgol", "email": "<EMAIL>"}, {"name": "scott.gonza<PERSON>z", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}], "homepage": "https://jquery.com", "bugs": {"url": "https://github.com/jquery/jquery/issues"}, "dist": {"shasum": "714f1f8d9dde4bdfa55764ba37ef214630d80ef2", "tarball": "https://registry.npmjs.org/jquery/-/jquery-3.4.1.tgz", "fileCount": 125, "integrity": "sha512-36+AdBzCL+y6qjw5Tx7HgzeGCzC81MDDgaUP8ld2zhx58HdqXGoBd+tHdrBMiyjGQs0Hxs/MLZTu/eHNJJuWPw==", "signatures": [{"sig": "MEYCIQDJwzRm3I2HQxdow4ZA/pCAXsqoEB5aDEEIf5Lg0/jR6wIhAJFYGHUU9g1AmBKEiYaUDd+ydNqZOEU6cvFdMgvq0/oq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1295318, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcyg1rCRA9TVsSAnZWagAAeOEP/2+ykv0pLlPLU8YLeOoS\nUTBRx0TwY8fcGwZfsxB2JERZfrQQM7l1l3aS7Rb5XYmL4rlHrnGq571H2ogF\ntuEPsLjSGbM+3eM6Y1MRCTXn4cp3KXVKU+oBuFrA6gSQkhPB9nYjaf0jUl5L\nVYMjB6CiqB5Pxy0j3hCrT7iMkoxY6b+V/3rN7HqRj1pL1N71QLrVyYv9AiHU\nuXaKlsI4xWg1RDrt4+Ef8xe4n5bwfWxn8hNIBwlldvexknA+FGz/LY42FOl7\n9alWTyfJMmK15ggMD02W8iCLTaUxC4Hz16ZeoGnnOZkPaZCSZEpAHCJSh5BV\n9J7GGhpiEtlQRQQbhRYl2nAb5Z02APUIJ7tQt1JYAtb1WJNJgOjDITvFqpxj\nbOc47BP+7pcWF4rqWviIjWts4mU3w0Yl+ZUaTSB6lOjBSH/ONC4CspyfKlQg\nu1Kcrk8skTT4fDNQcXLTDdXXCssqrU8nRtzKarLjuBvd0Cs0ZPSZqEJQG6OA\nJ7zdqnqwzfRrfbQwWi7OozkjSrnS2mLwFTxiuAsO86AgzwKYZQrMwEV89LXQ\n8cRPhH1jvPgh2RcLraKTuQsSZ2o/h+mrq8lhIDNBVJXmUZKNRSW9GNoD8QEY\na52Ygcu8lCFbtJ1gUvHwOzHaRmNqZb7dwMGV5zbTMSFViGSN1qabM8jNOwl4\nCp1s\r\n=n88a\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/jquery.js", "husky": {"hooks": {"commit-msg": "node node_modules/commitplease", "pre-commit": "grunt lint:newer qunit_fixture"}}, "title": "j<PERSON><PERSON><PERSON>", "gitHead": "15bc73803f76bc53b654b9fdbbbc096f56d7c03d", "scripts": {"test": "grunt && grunt test:slow && grunt karma:main", "build": "npm install && grunt", "start": "grunt watch", "jenkins": "npm run test:browserless", "test:browser": "grunt && grunt karma:main", "test:browserless": "grunt && grunt test:slow"}, "_npmUser": {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}, "deprecated": "This version is deprecated. Please upgrade to the latest version or find support at https://www.herodevs.com/support/jquery-nes.", "repository": {"url": "git+https://github.com/jquery/jquery.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "JavaScript library for DOM operations", "directories": {}, "_nodeVersion": "8.16.0", "commitplease": {"nohook": true, "components": ["Docs", "Tests", "Build", "Support", "Release", "Core", "Ajax", "Attributes", "Callbacks", "CSS", "Data", "Deferred", "Deprecated", "Dimensions", "Effects", "Event", "Manipulation", "Offset", "Queue", "Selector", "Serialize", "Traversing", "Wrap"], "markerPattern": "^((clos|fix|resolv)(e[sd]|ing))|^(refs?)", "ticketPattern": "^((Closes|Fixes) ([a-zA-Z]{2,}-)[0-9]+)|^(Refs? [^#])"}, "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"q": "1.5.1", "grunt": "1.0.3", "husky": "1.3.1", "jsdom": "13.2.0", "karma": "4.0.1", "qunit": "2.9.2", "sinon": "2.3.7", "sizzle": "2.3.4", "core-js": "2.6.5", "gzip-js": "0.3.2", "insight": "0.10.1", "raw-body": "2.3.3", "grunt-cli": "1.3.2", "requirejs": "2.3.6", "testswarm": "1.1.0", "uglify-js": "3.4.7", "@babel/core": "7.3.3", "grunt-babel": "8.0.0", "grunt-karma": "3.0.1", "grunt-newer": "1.3.0", "karma-qunit": "3.0.0", "commitplease": "3.2.0", "grunt-eslint": "21.0.0", "grunt-npmcopy": "0.1.0", "grunt-jsonlint": "1.1.0", "load-grunt-tasks": "4.0.0", "grunt-git-authors": "3.2.0", "karma-ie-launcher": "1.0.0", "grunt-compare-size": "0.4.2", "grunt-contrib-watch": "1.1.0", "native-promise-only": "0.8.1", "strip-json-comments": "2.0.1", "eslint-config-jquery": "1.0.1", "grunt-contrib-uglify": "3.4.0", "karma-jsdom-launcher": "7.1.0", "promises-aplus-tests": "2.1.2", "karma-chrome-launcher": "2.2.0", "karma-firefox-launcher": "1.1.0", "karma-browserstack-launcher": "1.4.0", "@babel/plugin-transform-for-of": "7.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/jquery_3.4.1_1556745578228_0.09546423386966585", "host": "s3://npm-registry-packages"}}, "3.5.0": {"name": "j<PERSON>y", "version": "3.5.0", "keywords": ["j<PERSON>y", "javascript", "browser", "library"], "author": {"url": "https://github.com/jquery/jquery/blob/3.5.0/AUTHORS.txt", "name": "JS Foundation and other contributors"}, "license": "MIT", "_id": "jquery@3.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mgol", "email": "<EMAIL>"}, {"name": "scott.gonza<PERSON>z", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}], "homepage": "https://jquery.com", "bugs": {"url": "https://github.com/jquery/jquery/issues"}, "dist": {"shasum": "9980b97d9e4194611c36530e7dc46a58d7340fc9", "tarball": "https://registry.npmjs.org/jquery/-/jquery-3.5.0.tgz", "fileCount": 123, "integrity": "sha512-Xb7SVYMvygPxbFMpTFQiHh1J7HClEaThguL15N/Gg37Lri/qKyhRGZYzHRyLH8Stq3Aow0LsHO2O2ci86fCrNQ==", "signatures": [{"sig": "MEUCIQDrphmu+1xaugLGDiqFI/zxppjHDVSLX4lfsTNzjMoexgIgGq7+8kWd10sLFUCtpWKVvbfdsr4xYl1/BoeB7Nfd6m4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1315634, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJekJO8CRA9TVsSAnZWagAA1VwP+gPgHR/8IXclArWJPyPw\nI44aJFffRpyaddibT6bkwAyqELi/oZiso5hmW7QaJv9S1Moq7m1c1aIcMCDi\nPbEJ6jop+F3eNgtULVIwk6OzF1DlC9AkRW/j0LQUlgun1kmlID2ToRPVt8sL\noO/B3Bcm0Qw6E6qPS8kIfxQJ+7b+cTr02BFX9nTm3JgXz/bNJIAYUbnQEt8l\nQPIosesCk1cYr8RxY31dNUAn80g9mpOTmTcV1ZhKqMwsMu7YVfOVUOH0rcWb\nu+AkZ+VgXTvjWE6rHpUwePU0xny/Ssf2RkklHTa4ljQXCp1wWNkRbNK4bZkb\nbCJlYZU1bcxfHUYXm/n1X8HmG+ZO/ZCd55A9/URm+Pd7PdZVxgj/lHGDDvV6\nadjrlgTPjT+qGqVDJBXfEQfzliDbleMG5/dm2go3wGUIvJphJAFyVd7aZPaL\n9WKxwh7Ul2vceZNCpZ8W3x3dXZ203OkX1s8Qk9F8j2eZcsmK6QtFyWVupNUK\nRw/1jNiHkcM0Ugr0jro+04WuU6nocad6Yex8Yih0pMkS1XnM0SSZ+pNr0SxF\nk37NffltL0RgfwMKEHBeTW6LJ1m9ty8Xpja1PxQTU1Qt821y1mzqExq9Arvk\n5sPiXVtu9sP5E7RRqDnA1vGutvDG7r+awFDcAM8upINhzDiWFittI9FUFJgz\no/vp\r\n=n9GM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/jquery.js", "husky": {"hooks": {"commit-msg": "node node_modules/commitplease", "pre-commit": "grunt lint:newer qunit_fixture"}}, "title": "j<PERSON><PERSON><PERSON>", "gitHead": "1fd78cd729742aa26f64783f9188e169e41f194e", "scripts": {"test": "npm run test:slim && npm run test:no-deprecated && npm run test:no-sizzle && grunt && grunt test:slow && grunt karma:main && grunt karma:amd", "build": "npm install && grunt", "start": "grunt watch", "jenkins": "npm run test:browserless", "test:amd": "grunt && grunt karma:amd", "test:slim": "grunt test:prepare && grunt custom:slim && grunt karma:main", "test:browser": "grunt && grunt karma:main", "test:no-sizzle": "grunt test:prepare && grunt custom:-sizzle && grunt karma:main", "test:browserless": "grunt && grunt test:slow", "test:no-deprecated": "grunt test:prepare && grunt custom:-deprecated && grunt karma:main"}, "_npmUser": {"name": "mgol", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jquery/jquery.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "JavaScript library for DOM operations", "directories": {}, "_nodeVersion": "12.16.2", "commitplease": {"nohook": true, "components": ["Docs", "Tests", "Build", "Support", "Release", "Core", "Ajax", "Attributes", "Callbacks", "CSS", "Data", "Deferred", "Deprecated", "Dimensions", "Effects", "Event", "Manipulation", "Offset", "Queue", "Selector", "Serialize", "Traversing", "Wrap"], "markerPattern": "^((clos|fix|resolv)(e[sd]|ing))|^(refs?)", "ticketPattern": "^((Closes|Fixes) ([a-zA-Z]{2,}-)[0-9]+)|^(Refs? [^#])"}, "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"q": "1.5.1", "grunt": "1.0.3", "husky": "1.3.1", "jsdom": "13.2.0", "karma": "4.0.1", "qunit": "2.9.2", "sinon": "2.3.7", "sizzle": "2.3.5", "core-js": "2.6.5", "gzip-js": "0.3.2", "insight": "0.10.1", "raw-body": "2.3.3", "grunt-cli": "1.3.2", "requirejs": "2.3.6", "testswarm": "1.1.0", "uglify-js": "3.4.7", "@babel/core": "7.3.3", "grunt-babel": "8.0.0", "grunt-karma": "3.0.1", "grunt-newer": "1.3.0", "karma-qunit": "3.0.0", "commitplease": "3.2.0", "grunt-eslint": "22.0.0", "grunt-npmcopy": "0.1.0", "grunt-jsonlint": "1.1.0", "load-grunt-tasks": "4.0.0", "grunt-git-authors": "3.2.0", "karma-ie-launcher": "1.0.0", "grunt-compare-size": "0.4.2", "grunt-contrib-watch": "1.1.0", "native-promise-only": "0.8.1", "strip-json-comments": "2.0.1", "eslint-config-jquery": "2.0.0", "grunt-contrib-uglify": "3.4.0", "karma-jsdom-launcher": "7.1.0", "promises-aplus-tests": "2.1.2", "karma-chrome-launcher": "2.2.0", "karma-firefox-launcher": "1.1.0", "karma-browserstack-launcher": "1.4.0", "@babel/plugin-transform-for-of": "7.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/jquery_3.5.0_1586533307712_0.5876383610331921", "host": "s3://npm-registry-packages"}}, "3.5.1": {"name": "j<PERSON>y", "version": "3.5.1", "keywords": ["j<PERSON>y", "javascript", "browser", "library"], "author": {"url": "https://github.com/jquery/jquery/blob/3.5.1/AUTHORS.txt", "name": "JS Foundation and other contributors"}, "license": "MIT", "_id": "jquery@3.5.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mgol", "email": "<EMAIL>"}, {"name": "scott.gonza<PERSON>z", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}], "homepage": "https://jquery.com", "bugs": {"url": "https://github.com/jquery/jquery/issues"}, "dist": {"shasum": "d7b4d08e1bfdb86ad2f1a3d039ea17304717abb5", "tarball": "https://registry.npmjs.org/jquery/-/jquery-3.5.1.tgz", "fileCount": 123, "integrity": "sha512-XwIBPqcMn57FxfT+Go5pzySnm4KWkT1Tv7gjrpT1srtf8Weynl6R273VJ5GjkRb51IzMp5nbaPjJXMWeju2MKg==", "signatures": [{"sig": "MEUCIQD1/V8m8iNuNM+/GxMwlO26CdFvABCsnciO5C9tPj0LRgIgWz7C9QItmdZmbVxt8VioW6d7W28wxgEnQ22YWFD1Y8k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1315593, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesJwhCRA9TVsSAnZWagAA+SIQAI9RCw96kNZnbA/ONf5x\n9JqCbsyBPH8ZV76Q/lq/OOJgTQqSWirb2iK2BCrPbcEdzQ9WUKrmmiFSGOLH\ne1yQW4gi4+MzL+prjipb/GKZJINgr0I4/Rd0TAMuVapqNFWc01phXOWH9zk6\n9t0lYWlpa9RA07Dn+2lTvU3sGkzEfAoamBhRIL3L9LVMTDXFyQXU2FqNKqCG\nLyEyomvO7juax8kQaRsl6pWZ9VAkXIvRMNI3fvWqFaGMaShK0hP0woymcZER\nlk6mW+TsYeU4cEBYWLNo2ei+NkLYAT7mo4Yt6PudZ7hCWLZV0OHHs5K9m5m5\nS6ppOPUCPAGi0G2ubxi8fw6vbhlLD7JWmzWksF0dE6+qQv+e9VHEhwchjUXR\n+RFLqZOEjevhf/RE0wbmjYurw8QIxgnUjXzKbgHdcTrOutBcW5Kv3xavq0v2\nJNnnuvDolqAbWBBUg0vNKsedduudLgzXXS2OnpLNGn3ctKoEIVV79G6OWBJB\noX1lFAimK9uT/CF4YSeUMY7ghZR4RRLrY0XcNRCLAMh17NThNpIyZk+hCEbQ\nHO9FOD+yhtz4sF5tz/RrSdHkt5hl5J/g9H6eTiPmbKydchdKd/V8RpAObVs/\nTg2LEk7UHh/l5UXK2IJfk+WLwd86g1rwxsJ48MLoGutTroR8MRrQ/78Htoij\nk+WY\r\n=XCQe\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/jquery.js", "husky": {"hooks": {"commit-msg": "node node_modules/commitplease", "pre-commit": "grunt lint:newer qunit_fixture"}}, "title": "j<PERSON><PERSON><PERSON>", "gitHead": "4c0e4becb8263bb5b3e6dadc448d8e7305ef8215", "scripts": {"test": "npm run test:slim && npm run test:no-deprecated && npm run test:no-sizzle && grunt && grunt test:slow && grunt karma:main && grunt karma:amd", "build": "npm install && grunt", "start": "grunt watch", "jenkins": "npm run test:browserless", "test:amd": "grunt && grunt karma:amd", "test:slim": "grunt test:prepare && grunt custom:slim && grunt karma:main", "test:browser": "grunt && grunt karma:main", "test:no-sizzle": "grunt test:prepare && grunt custom:-sizzle && grunt karma:main", "test:browserless": "grunt && grunt test:slow", "test:no-deprecated": "grunt test:prepare && grunt custom:-deprecated && grunt karma:main"}, "_npmUser": {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jquery/jquery.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "JavaScript library for DOM operations", "directories": {}, "_nodeVersion": "12.16.1", "commitplease": {"nohook": true, "components": ["Docs", "Tests", "Build", "Support", "Release", "Core", "Ajax", "Attributes", "Callbacks", "CSS", "Data", "Deferred", "Deprecated", "Dimensions", "Effects", "Event", "Manipulation", "Offset", "Queue", "Selector", "Serialize", "Traversing", "Wrap"], "markerPattern": "^((clos|fix|resolv)(e[sd]|ing))|^(refs?)", "ticketPattern": "^((Closes|Fixes) ([a-zA-Z]{2,}-)[0-9]+)|^(Refs? [^#])"}, "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"q": "1.5.1", "grunt": "1.0.3", "husky": "1.3.1", "jsdom": "13.2.0", "karma": "4.0.1", "qunit": "2.9.2", "sinon": "2.3.7", "sizzle": "2.3.5", "core-js": "2.6.5", "gzip-js": "0.3.2", "insight": "0.10.1", "raw-body": "2.3.3", "grunt-cli": "1.3.2", "requirejs": "2.3.6", "testswarm": "1.1.0", "uglify-js": "3.4.7", "@babel/core": "7.3.3", "grunt-babel": "8.0.0", "grunt-karma": "3.0.1", "grunt-newer": "1.3.0", "karma-qunit": "3.0.0", "commitplease": "3.2.0", "grunt-eslint": "22.0.0", "grunt-npmcopy": "0.1.0", "grunt-jsonlint": "1.1.0", "load-grunt-tasks": "4.0.0", "grunt-git-authors": "3.2.0", "karma-ie-launcher": "1.0.0", "grunt-compare-size": "0.4.2", "grunt-contrib-watch": "1.1.0", "native-promise-only": "0.8.1", "strip-json-comments": "2.0.1", "eslint-config-jquery": "2.0.0", "grunt-contrib-uglify": "3.4.0", "karma-jsdom-launcher": "7.1.0", "promises-aplus-tests": "2.1.2", "karma-chrome-launcher": "2.2.0", "karma-firefox-launcher": "1.1.0", "karma-browserstack-launcher": "1.4.0", "@babel/plugin-transform-for-of": "7.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/jquery_3.5.1_1588632608615_0.33880236700918", "host": "s3://npm-registry-packages"}}, "3.6.0": {"name": "j<PERSON>y", "version": "3.6.0", "keywords": ["j<PERSON>y", "javascript", "browser", "library"], "author": {"url": "https://github.com/jquery/jquery/blob/3.6.0/AUTHORS.txt", "name": "OpenJS Foundation and other contributors"}, "license": "MIT", "_id": "jquery@3.6.0", "maintainers": [{"name": "scott.gonza<PERSON>z", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}, {"name": "mgol", "email": "<EMAIL>"}], "homepage": "https://jquery.com", "bugs": {"url": "https://github.com/jquery/jquery/issues"}, "dist": {"shasum": "c72a09f15c1bdce142f49dbf1170bdf8adac2470", "tarball": "https://registry.npmjs.org/jquery/-/jquery-3.6.0.tgz", "fileCount": 123, "integrity": "sha512-JVzAR/AjBvVt2BmYhxRCSYysDsPcssdmTFnzyLEts9qNwmjmu4JTAMYubEfwVOSwpQ1I1sKKFcxhZCI2buerfw==", "signatures": [{"sig": "MEYCIQCDYnUSuaGw7s73P3g8oj8MInugGd6Oi5/llwoZvk50QgIhAMFSBi26sWrMaEYBZx+2YIhILTyrOXi90S2TN+j3RQsa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1318507, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgPnHsCRA9TVsSAnZWagAA3tQP/i738n4AHT4QJxYnLamb\nvQEB9yjcsipiGgV2nY/eNbXtljhGZ+vARJk1Sa4j3vGWmGSKgMRW+k2chtsZ\nnDJ91MXzjP4uJwgTTTn+bWWWmINwYqnLIxbYEpsBt1ugmFcv1AsyZQUCTu/G\naPzwsJWdxzDCRige0PC3NTT8ImeUonXnM6OBZ65ICXfi+BzZMWm50DSzzkA2\n/jdURwOiLNHtrJgilWfLqVVW6WWxfCOXcaTPD4K8J+1twLdrTAaA9/ECsX/c\nrb+pp7JLbqdAHFIHjl5R4nlsF2CsJmLI+dYSDCPql9ivkWcWXX+b0EHUOtBZ\nHDP4NjtYCIP11xdtmKqhTkC0JkOvUUI+nhCOOLWitH9W5AhJQAWrbroOptuv\nEKzc4Cvh2yEZvGX92zcFnbZlGDw7jRNgME8xyxbgK/zOPyoKBciqT2aFn9xI\nuDKj6hDonMSwqhoOuYcAFY2YfbR45uuJLKBC04oxjbrIF7o+WxbmAehjFxDH\nfiECruH9aCr7loRHOKcoo9Xp1y90B1JXiSJBARI/Jfq+6g2crZFvlBvFIX5P\nhSxvTdlb1/kBjxjcRtfgSmaUBUBQqKJEkB5YCh6FZ9XoGHRdcc0PocXFbXHh\nvyE63iiqk1rQYnDf/lPAp+Om8RSzyUCGCajGrCWsSzpt3EHapnqVY8OfT90p\nofL1\r\n=b8nm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/jquery.js", "husky": {"hooks": {"commit-msg": "commitplease .git/COMMIT_EDITMSG", "pre-commit": "grunt lint:newer qunit_fixture"}}, "title": "j<PERSON><PERSON><PERSON>", "gitHead": "e786e3d9707ffd9b0dd330ca135b66344dcef85a", "scripts": {"test": "npm run test:slim && npm run test:no-deprecated && npm run test:no-sizzle && grunt && grunt test:slow && grunt karma:main && grunt karma:amd", "build": "npm install && grunt", "start": "grunt watch", "jenkins": "npm run test:browserless", "test:amd": "grunt && grunt karma:amd", "test:slim": "grunt test:prepare && grunt custom:slim && grunt karma:main", "test:browser": "grunt && grunt karma:main", "test:no-sizzle": "grunt test:prepare && grunt custom:-sizzle && grunt karma:main", "test:browserless": "grunt && grunt test:slow", "test:no-deprecated": "grunt test:prepare && grunt custom:-deprecated && grunt karma:main"}, "_npmUser": {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jquery/jquery.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "JavaScript library for DOM operations", "directories": {}, "_nodeVersion": "14.16.0", "commitplease": {"nohook": true, "components": ["Docs", "Tests", "Build", "Support", "Release", "Core", "Ajax", "Attributes", "Callbacks", "CSS", "Data", "Deferred", "Deprecated", "Dimensions", "Effects", "Event", "Manipulation", "Offset", "Queue", "Selector", "Serialize", "Traversing", "Wrap"], "markerPattern": "^((clos|fix|resolv)(e[sd]|ing))|^(refs?)", "ticketPattern": "^((Closes|Fixes) ([a-zA-Z]{2,}-)[0-9]+)|^(Refs? [^#])"}, "_hasShrinkwrap": false, "devDependencies": {"q": "1.5.1", "grunt": "1.3.0", "husky": "1.3.1", "jsdom": "13.2.0", "karma": "5.2.3", "qunit": "2.9.2", "sinon": "2.3.7", "sizzle": "2.3.6", "core-js": "2.6.5", "gzip-js": "0.3.2", "insight": "0.10.1", "raw-body": "2.3.3", "grunt-cli": "1.3.2", "requirejs": "2.3.6", "testswarm": "1.1.2", "uglify-js": "3.4.7", "@babel/core": "7.3.3", "grunt-babel": "8.0.0", "grunt-karma": "4.0.0", "grunt-newer": "1.3.0", "karma-qunit": "3.0.0", "commitplease": "3.2.0", "grunt-eslint": "22.0.0", "grunt-npmcopy": "0.2.0", "grunt-jsonlint": "1.1.0", "load-grunt-tasks": "5.1.0", "grunt-git-authors": "3.2.0", "karma-ie-launcher": "1.0.0", "grunt-compare-size": "0.4.2", "grunt-contrib-watch": "1.1.0", "native-promise-only": "0.8.1", "strip-json-comments": "2.0.1", "eslint-config-jquery": "3.0.0", "grunt-contrib-uglify": "3.4.0", "karma-jsdom-launcher": "8.0.2", "promises-aplus-tests": "2.1.2", "karma-chrome-launcher": "2.2.0", "karma-firefox-launcher": "1.1.0", "karma-browserstack-launcher": "1.4.0", "@babel/plugin-transform-for-of": "7.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/jquery_3.6.0_1614705131780_0.39064720927102825", "host": "s3://npm-registry-packages"}}, "3.6.1": {"name": "j<PERSON>y", "version": "3.6.1", "keywords": ["j<PERSON>y", "javascript", "browser", "library"], "author": {"url": "https://github.com/jquery/jquery/blob/3.6.1/AUTHORS.txt", "name": "OpenJS Foundation and other contributors"}, "license": "MIT", "_id": "jquery@3.6.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}, {"name": "mgol", "email": "<EMAIL>"}, {"name": "openjsfoundation", "email": "<EMAIL>"}], "homepage": "https://jquery.com", "bugs": {"url": "https://github.com/jquery/jquery/issues"}, "dist": {"shasum": "fab0408f8b45fc19f956205773b62b292c147a16", "tarball": "https://registry.npmjs.org/jquery/-/jquery-3.6.1.tgz", "fileCount": 126, "integrity": "sha512-opJeO4nCucVnsjiXOE+/PcCgYw9Gwpvs/a6B1LL/lQhwWwpbVEVYDZ1FokFr8PRc7ghYlrFPuyHuiiDNTQxmcw==", "signatures": [{"sig": "MEUCIEgLA93BcgJTZJo+3DmUtUjH7Vodri1icSkFe2PuEPKbAiEAonlrvCk8PRk4WKirYuwIjBeoQdpJZsEvJCb9+JMAQfU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1323376, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjCQjQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoAOw//S2k6gewPxCe7KjPBv0a0tBDkYyI5MSie3BhwaTuZIaJFcOhf\r\nXIL1pkcY6/ON6lhvXh3a9GefllfkWNVq1FbulyDuKekrUZ4/I7yFI4EPWhqG\r\nqGfAkAkAfZ1v11xleibBHUtNSGdFDO8NyLmz3jrsFiqf/VikGrrDI97hiy7h\r\nx4xpqZCASnWv6m+BfeQ8Q3mqzANl6UIVdoyfVaP2NsSunQkaH1qsliSa3zGh\r\nJnF9v/MaOuxwtc94EbHy49Vi2C5Kavgp9XhBh3qLSwWIf17aF7qWPgzdkdEe\r\n1Licnuu944K8FE6BOcA3PS9Bh7gdmvWlgEzSp0xkxnxHqe6/PcguT+WTwWmt\r\nOJjvjEcaS1pzqyNa+Js81D25pb5ulJ9yIKnj0wdjDaArEw1nF+HAGgVy58x5\r\nVU5+3Jbz4YzXIYlfxmTI6V8bzpSCkuBmARwmPp6NtqCCMPFhQWy2GoYXCft/\r\nGrfujyqBhM7Qk+Ym+AK/wQqyKe01t3YVo/pWUzUGuIbkZv9oLDOvHKP9IFSL\r\nSnudOMwOf/M38pSxgXWfEwEQ1Px3t3gHBpEHqPLzl7n4X2chfjY5fO1f2yGd\r\nFd6jwH80qO90W99J7NOmlBdVBB2XRGJnzLTKzMdtCS42IXxeB27pGPJLD2BT\r\nSn4RQRvIlMYLEOiE1+SliufoDHI7LhaYRvo=\r\n=IwCL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/jquery.js", "husky": {"hooks": {"commit-msg": "commitplease .git/COMMIT_EDITMSG", "pre-commit": "grunt lint:newer qunit_fixture"}}, "title": "j<PERSON><PERSON><PERSON>", "gitHead": "3711efedf0ca2e998cd0417324f717f2e0b828ec", "scripts": {"test": "npm run test:slim && npm run test:no-deprecated && npm run test:no-sizzle && grunt && grunt test:slow && grunt karma:main && grunt karma:amd", "build": "npm install && grunt", "start": "grunt watch", "jenkins": "npm run test:browserless", "test:amd": "grunt && grunt karma:amd", "test:slim": "grunt test:prepare && grunt custom:slim && grunt karma:main", "test:browser": "grunt && grunt karma:main", "test:no-sizzle": "grunt test:prepare && grunt custom:-sizzle && grunt karma:main", "test:browserless": "grunt && grunt test:slow", "test:no-deprecated": "grunt test:prepare && grunt custom:-deprecated && grunt karma:main"}, "_npmUser": {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jquery/jquery.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "JavaScript library for DOM operations", "directories": {}, "_nodeVersion": "16.17.0", "commitplease": {"nohook": true, "components": ["Docs", "Tests", "Build", "Support", "Release", "Core", "Ajax", "Attributes", "Callbacks", "CSS", "Data", "Deferred", "Deprecated", "Dimensions", "Effects", "Event", "Manipulation", "Offset", "Queue", "Selector", "Serialize", "Traversing", "Wrap"], "markerPattern": "^((clos|fix|resolv)(e[sd]|ing))|^(refs?)", "ticketPattern": "^((Closes|Fixes) ([a-zA-Z]{2,}-)[0-9]+)|^(Refs? [^#])"}, "_hasShrinkwrap": false, "devDependencies": {"q": "1.5.1", "grunt": "1.4.1", "husky": "4.2.5", "jsdom": "19.0.0", "karma": "^6.3.17", "qunit": "2.9.2", "sinon": "2.3.7", "colors": "1.4.0", "sizzle": "2.3.6", "core-js": "2.6.5", "gzip-js": "0.3.2", "insight": "0.10.3", "raw-body": "2.3.3", "grunt-cli": "1.4.3", "requirejs": "2.3.6", "testswarm": "1.1.2", "uglify-js": "3.4.7", "@babel/core": "7.3.3", "grunt-babel": "8.0.0", "grunt-karma": "4.0.2", "grunt-newer": "1.3.0", "karma-qunit": "4.1.2", "commitplease": "3.2.0", "grunt-eslint": "22.0.0", "grunt-npmcopy": "0.2.0", "grunt-jsonlint": "2.1.2", "load-grunt-tasks": "5.1.0", "grunt-git-authors": "3.2.0", "karma-ie-launcher": "1.0.0", "grunt-compare-size": "0.4.2", "grunt-contrib-watch": "1.1.0", "native-promise-only": "0.8.1", "strip-json-comments": "2.0.1", "eslint-config-jquery": "3.0.0", "grunt-contrib-uglify": "3.4.0", "karma-jsdom-launcher": "12.0.0", "promises-aplus-tests": "2.1.2", "karma-chrome-launcher": "3.1.1", "karma-firefox-launcher": "2.1.2", "karma-browserstack-launcher": "1.6.0", "@babel/plugin-transform-for-of": "7.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/jquery_3.6.1_1661536464434_0.19824222485914755", "host": "s3://npm-registry-packages"}}, "3.6.2": {"name": "j<PERSON>y", "version": "3.6.2", "keywords": ["j<PERSON>y", "javascript", "browser", "library"], "author": {"url": "https://github.com/jquery/jquery/blob/3.6.2/AUTHORS.txt", "name": "OpenJS Foundation and other contributors"}, "license": "MIT", "_id": "j<PERSON>y@3.6.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}, {"name": "mgol", "email": "<EMAIL>"}, {"name": "openjsfoundation", "email": "<EMAIL>"}], "homepage": "https://jquery.com", "bugs": {"url": "https://github.com/jquery/jquery/issues"}, "dist": {"shasum": "8302bbc9160646f507bdf59d136a478b312783c4", "tarball": "https://registry.npmjs.org/jquery/-/jquery-3.6.2.tgz", "fileCount": 126, "integrity": "sha512-/e7ulNIEEYk1Z/l4X0vpxGt+B/dNsV8ghOPAWZaJs8pkGvsSC0tm33aMGylXcj/U7y4IcvwtMXPMyBFZn/gK9A==", "signatures": [{"sig": "MEUCIQCSZh+vgdA1lWHP0VKjJnqLEmXMnPHcpms1m5o5Vxi6EQIgfwca602mr/CMMguGbt+jLMP/mmP/fksj/mXJ88hwOfI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1336641, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjmJMhACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrzfA//XpEep4e9YzV4PZ4u2aWqMzM0cKjoJsNO4CpQKz+ctXoa1VQW\r\nmnZ1VwG21Oe47PtHYDPzV0RwE+mCSa8lqY/ZXWj6Sd17eLXjLe6pXVcdRgOv\r\nHpu0t0zoR3pMRmg1EReibd3UCh6OTH4vgYqF8oIsFYKWiNJabIOmMnubRfe6\r\nphgnQW9RH94EoM5J9GTN3dlo0W7y9yTydWTTNJueJlByUvfOj721w2lSvz0A\r\nk9xXn7cEZ5mjm2/gUbaYcRmud6BlbFiR18TFfA+SPGYzQVNcH78EvnYHudk6\r\nB5+TTCWtiGFjmLovdl6pzv01pwy2jTPvmgepURsXxzBW5nLg6wk2/P9ESq1C\r\n3KAIPoIm5qlgBs50Qb0wXPKnniphbpH4HdPPYrSTS6XAfO1pZbTyWY8BB7Wd\r\ni00pkCG9NlAwGsMnUnONSjAbjh79oyg11ig58UOz66eTC6atM440SStMdBmY\r\nV1IG6PId4xfcUwR/OszTIstJdP3JoQ8QXrIyi7Tx1bZmMZCSOkqbGrCDaRHC\r\nAYK4Q4zaYyqAuMnhzU6qgcxqe4Vbd5l6Ibt4L/55mLJFDPF6J9Ddljb1ksQO\r\nxRzHplvTlDfWvZ1f+X4KSFYC5eWCGEi2m2QCsv76Z7ZZrCDLRElYb969TE88\r\nbw0YLuL800huUdZ8qbaDKaIb0wJMDdu2uZQ=\r\n=gFXE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/jquery.js", "husky": {"hooks": {"commit-msg": "commitplease .git/COMMIT_EDITMSG", "pre-commit": "grunt lint:newer qunit_fixture"}}, "title": "j<PERSON><PERSON><PERSON>", "gitHead": "17f2a6864677d6dca5c1a2f05c02420ad899045b", "scripts": {"test": "npm run test:slim && npm run test:no-deprecated && npm run test:no-sizzle && grunt && grunt test:slow && grunt karma:main && grunt karma:amd", "build": "npm install && grunt", "start": "grunt watch", "jenkins": "npm run test:browserless", "test:amd": "grunt && grunt karma:amd", "test:slim": "grunt test:prepare && grunt custom:slim && grunt karma:main", "test:browser": "grunt && grunt karma:main", "test:no-sizzle": "grunt test:prepare && grunt custom:-sizzle && grunt karma:main", "test:browserless": "grunt && grunt test:slow", "test:no-deprecated": "grunt test:prepare && grunt custom:-deprecated && grunt karma:main"}, "_npmUser": {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jquery/jquery.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "JavaScript library for DOM operations", "directories": {}, "_nodeVersion": "18.12.1", "commitplease": {"nohook": true, "components": ["Docs", "Tests", "Build", "Support", "Release", "Core", "Ajax", "Attributes", "Callbacks", "CSS", "Data", "Deferred", "Deprecated", "Dimensions", "Effects", "Event", "Manipulation", "Offset", "Queue", "Selector", "Serialize", "Traversing", "Wrap"], "markerPattern": "^((clos|fix|resolv)(e[sd]|ing))|^(refs?)", "ticketPattern": "^((Closes|Fixes) ([a-zA-Z]{2,}-)[0-9]+)|^(Refs? [^#])"}, "_hasShrinkwrap": false, "devDependencies": {"q": "1.5.1", "grunt": "1.5.3", "husky": "4.2.5", "jsdom": "19.0.0", "karma": "^6.3.17", "qunit": "2.9.2", "sinon": "2.3.7", "colors": "1.4.0", "sizzle": "2.3.8", "core-js": "2.6.5", "gzip-js": "0.3.2", "insight": "0.10.3", "raw-body": "2.3.3", "grunt-cli": "1.4.3", "requirejs": "2.3.6", "testswarm": "1.1.2", "uglify-js": "3.4.7", "@babel/core": "7.3.3", "grunt-babel": "8.0.0", "grunt-karma": "4.0.2", "grunt-newer": "1.3.0", "karma-qunit": "4.1.2", "commitplease": "3.2.0", "grunt-eslint": "22.0.0", "grunt-npmcopy": "0.2.0", "grunt-jsonlint": "2.1.2", "load-grunt-tasks": "5.1.0", "grunt-git-authors": "3.2.0", "karma-ie-launcher": "1.0.0", "grunt-compare-size": "0.4.2", "grunt-contrib-watch": "1.1.0", "native-promise-only": "0.8.1", "strip-json-comments": "2.0.1", "eslint-config-jquery": "3.0.0", "grunt-contrib-uglify": "3.4.0", "karma-jsdom-launcher": "12.0.0", "promises-aplus-tests": "2.1.2", "karma-chrome-launcher": "3.1.1", "karma-firefox-launcher": "2.1.2", "karma-browserstack-launcher": "1.6.0", "@babel/plugin-transform-for-of": "7.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/jquery_3.6.2_1670943521483_0.9221061401179282", "host": "s3://npm-registry-packages"}}, "3.6.3": {"name": "j<PERSON>y", "version": "3.6.3", "keywords": ["j<PERSON>y", "javascript", "browser", "library"], "author": {"url": "https://github.com/jquery/jquery/blob/3.6.3/AUTHORS.txt", "name": "OpenJS Foundation and other contributors"}, "license": "MIT", "_id": "j<PERSON>y@3.6.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}, {"name": "mgol", "email": "<EMAIL>"}, {"name": "openjsfoundation", "email": "<EMAIL>"}], "homepage": "https://jquery.com", "bugs": {"url": "https://github.com/jquery/jquery/issues"}, "dist": {"shasum": "23ed2ffed8a19e048814f13391a19afcdba160e6", "tarball": "https://registry.npmjs.org/jquery/-/jquery-3.6.3.tgz", "fileCount": 126, "integrity": "sha512-bZ5Sy3YzKo9Fyc8wH2iIQK4JImJ6R0GWI9kL1/k7Z91ZBNgkRXE6U0JfHIizZbort8ZunhSI3jw9I6253ahKfg==", "signatures": [{"sig": "MEUCIFevmtgDu7vdJHGJo/VfbSbHPdCorznh7MNieo6Ek2uLAiEA4J4NQpov4Pz2TEhzYdOVS61LrLb2wT8/zgptqcGfhww=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1336532, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjoiqBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoo3g/9Gtf9/WJQKNGHua9rsKajPFGU1MyY29tSUkJEF1o4e1AtibAd\r\n+MRCyZDxESrT6XRVWLS4t8pWIGR8FtaOlxUpW+7nmDWDU+PJWRvfi3/Smzz1\r\n9yps1bgp2jTASq3xvo/GS9tCZQQsZUFHxDahRdUJ7txWw8OvOkWuMRjYuXMl\r\nZTdGLw0i+vrSbz4AdXiqx8B8UNhBRi9q0EhcTxJ2jRircNJ7c0II2It10moI\r\nI4WLcJQVlyHXcEt9epKCEFRS0FxCYt9xiDrUlKf1Ao41QXu8js38WbxYQr9L\r\nT+zqk8+B1Wn8u+kY4iXHeBy0W4EJ26gCrb23+jTW4UDzpleoynDbwtRJ0Z76\r\nhi+OKkwAUITVD//AGJ8vx3194QGhcW60Xt/CHum7Qdx9h1NeAHQrgTBr5YdU\r\n5juIlG1HsjH16WSbVV9tqOCsHLv/ROV7RRAMR94KMxD5RquYPjvXJOZHwxpO\r\njLAGAEuv47XY7h4sN+4DXyRXvO7Pbd/ZF+CUwf0OMvWZdnj++LuT6mZ8+zrC\r\nHVHTAElAVWY/hi5JMkUWJzqasY2WuewZVpSCUzWM3SLJy5Dsoh30u9F9yiPW\r\njHe8ccPQF4y7I5rT7xK2bXZzArOMGatqj9KZL1ga0uc+s96iCOAJGtrXr7fC\r\n8Gufs7pMVsYyDZKvXBFjYux8m0o2yQMV31g=\r\n=2QkC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/jquery.js", "husky": {"hooks": {"commit-msg": "commitplease .git/COMMIT_EDITMSG", "pre-commit": "grunt lint:newer qunit_fixture"}}, "title": "j<PERSON><PERSON><PERSON>", "gitHead": "da0f228131a578aea168b799fe4d7fe01764c98b", "scripts": {"test": "npm run test:slim && npm run test:no-deprecated && npm run test:no-sizzle && grunt && grunt test:slow && grunt karma:main && grunt karma:amd", "build": "npm install && grunt", "start": "grunt watch", "jenkins": "npm run test:browserless", "test:amd": "grunt && grunt karma:amd", "test:slim": "grunt test:prepare && grunt custom:slim && grunt karma:main", "test:browser": "grunt && grunt karma:main", "test:no-sizzle": "grunt test:prepare && grunt custom:-sizzle && grunt karma:main", "test:browserless": "grunt && grunt test:slow", "test:no-deprecated": "grunt test:prepare && grunt custom:-deprecated && grunt karma:main"}, "_npmUser": {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jquery/jquery.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "JavaScript library for DOM operations", "directories": {}, "_nodeVersion": "18.12.1", "commitplease": {"nohook": true, "components": ["Docs", "Tests", "Build", "Support", "Release", "Core", "Ajax", "Attributes", "Callbacks", "CSS", "Data", "Deferred", "Deprecated", "Dimensions", "Effects", "Event", "Manipulation", "Offset", "Queue", "Selector", "Serialize", "Traversing", "Wrap"], "markerPattern": "^((clos|fix|resolv)(e[sd]|ing))|^(refs?)", "ticketPattern": "^((Closes|Fixes) ([a-zA-Z]{2,}-)[0-9]+)|^(Refs? [^#])"}, "_hasShrinkwrap": false, "devDependencies": {"q": "1.5.1", "grunt": "1.5.3", "husky": "4.2.5", "jsdom": "19.0.0", "karma": "^6.3.17", "qunit": "2.9.2", "sinon": "2.3.7", "colors": "1.4.0", "sizzle": "2.3.9", "core-js": "2.6.5", "gzip-js": "0.3.2", "raw-body": "2.3.3", "grunt-cli": "1.4.3", "requirejs": "2.3.6", "testswarm": "1.1.2", "uglify-js": "3.4.7", "@babel/core": "7.3.3", "grunt-babel": "8.0.0", "grunt-karma": "4.0.2", "grunt-newer": "1.3.0", "karma-qunit": "4.1.2", "commitplease": "3.2.0", "grunt-eslint": "22.0.0", "grunt-npmcopy": "0.2.0", "grunt-jsonlint": "2.1.2", "load-grunt-tasks": "5.1.0", "grunt-git-authors": "3.2.0", "karma-ie-launcher": "1.0.0", "grunt-compare-size": "0.4.2", "grunt-contrib-watch": "1.1.0", "native-promise-only": "0.8.1", "strip-json-comments": "2.0.1", "eslint-config-jquery": "3.0.0", "grunt-contrib-uglify": "3.4.0", "karma-jsdom-launcher": "12.0.0", "promises-aplus-tests": "2.1.2", "karma-chrome-launcher": "3.1.1", "karma-firefox-launcher": "2.1.2", "karma-browserstack-launcher": "1.6.0", "@babel/plugin-transform-for-of": "7.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/jquery_3.6.3_1671572096795_0.12692892354079333", "host": "s3://npm-registry-packages"}}, "3.6.4": {"name": "j<PERSON>y", "version": "3.6.4", "keywords": ["j<PERSON>y", "javascript", "browser", "library"], "author": {"url": "https://github.com/jquery/jquery/blob/3.6.4/AUTHORS.txt", "name": "OpenJS Foundation and other contributors"}, "license": "MIT", "_id": "j<PERSON>y@3.6.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}, {"name": "mgol", "email": "<EMAIL>"}, {"name": "openjsfoundation", "email": "<EMAIL>"}], "homepage": "https://jquery.com", "bugs": {"url": "https://github.com/jquery/jquery/issues"}, "dist": {"shasum": "ba065c188142100be4833699852bf7c24dc0252f", "tarball": "https://registry.npmjs.org/jquery/-/jquery-3.6.4.tgz", "fileCount": 126, "integrity": "sha512-v28EW9DWDFpzcD9O5iyJXg3R3+q+mET5JhnjJzQUZMHOv67bpSIHq81GEYpPNZHG+XXHsfSme3nxp/hndKEcsQ==", "signatures": [{"sig": "MEYCIQDYxWZ7lMlBE4JQQseCkJvYVFtOwqjN+yzafNOGLSTTegIhAMVNvWIrQi4kjlQMY+DaD5XWEdCOoG1MDIBP4v83jHZ7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1332602, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkCKqeACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqpqQ/9EzjYmVNyg2NnCL76e8DAeDI+2C4ubjTc2nGgljO9NJ3LePEj\r\nqlyvXq4jgq7pEzEs5UNzy8PqW7xHXwdxWr6ijm+2FuQnX4fghlH5xcwCiVo9\r\n+7Qp67agATtgmdY7VJK3jLUdxDqL39zZ4UHmOEsHlewpK2wu1QNvEWGZ70PU\r\nMS9/XDWhf/NVXVyqzhM5U5Lt/iiE8pZTmLJSbldmPm/XYHqIbyIGMVJLOWC4\r\norJbdJ+cCV7sWWc9UxEVEfGJ6ypcUa0ZudqWjKGSqFgzstj8K1ctEfg9wQ4F\r\nEE0Y1S7JHN4qNCsc2WpoUf9MH7s/ibJIiqCgDnD++5o/IZWDQ8I1e5joBiEK\r\n6SQ7UYZEsmjCDvOAYi6NumOKsYALqwNXuSps9lerRPROydmKq1oatAOyXkZX\r\nhLffBEp81I4k6SRjZ6eseEIN791RMDSgztF6Xo5MjwgeZdy11UkR3WLRSVKx\r\nZP1mR7NZSfLw1DDCBSpgsHBvHdl1X19s9q17hNhlQD9HcVmYmewdsmAWjLPB\r\nogicgg/uTxwpQXLCJAWir1+vVJutHvIFWZwXZfwcs+xFJrlYsA78SGl2STkf\r\nA7xR0/Txn5NkMMwSx5qJo0mtmwEU6M268I42MFE7iYFeThQHgAxSWYRmt61j\r\n5LJpwKA4e5Fh42wuWmKJvBYqUVsRjVGpkhg=\r\n=5JHF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/jquery.js", "husky": {"hooks": {"commit-msg": "commitplease .git/COMMIT_EDITMSG", "pre-commit": "grunt lint:newer qunit_fixture"}}, "title": "j<PERSON><PERSON><PERSON>", "gitHead": "91ef2d8836342875f2519b5815197ea0f23613cf", "scripts": {"test": "npm run test:slim && npm run test:no-deprecated && npm run test:no-sizzle && grunt && grunt test:slow && grunt karma:main && grunt karma:amd", "build": "npm install && grunt", "start": "grunt watch", "jenkins": "npm run test:browserless", "test:amd": "grunt && grunt karma:amd", "test:slim": "grunt test:prepare && grunt custom:slim && grunt karma:main", "test:browser": "grunt && grunt karma:main", "test:no-sizzle": "grunt test:prepare && grunt custom:-sizzle && grunt karma:main", "test:browserless": "grunt && grunt test:slow", "test:no-deprecated": "grunt test:prepare && grunt custom:-deprecated && grunt karma:main"}, "_npmUser": {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jquery/jquery.git", "type": "git"}, "_npmVersion": "9.5.0", "description": "JavaScript library for DOM operations", "directories": {}, "_nodeVersion": "18.14.2", "commitplease": {"nohook": true, "components": ["Docs", "Tests", "Build", "Support", "Release", "Core", "Ajax", "Attributes", "Callbacks", "CSS", "Data", "Deferred", "Deprecated", "Dimensions", "Effects", "Event", "Manipulation", "Offset", "Queue", "Selector", "Serialize", "Traversing", "Wrap"], "markerPattern": "^((clos|fix|resolv)(e[sd]|ing))|^(refs?)", "ticketPattern": "^((Closes|Fixes) ([a-zA-Z]{2,}-)[0-9]+)|^(Refs? [^#])"}, "_hasShrinkwrap": false, "devDependencies": {"q": "1.5.1", "grunt": "1.5.3", "husky": "4.2.5", "jsdom": "19.0.0", "karma": "^6.3.17", "qunit": "2.9.2", "sinon": "2.3.7", "colors": "1.4.0", "sizzle": "2.3.10", "core-js": "2.6.5", "gzip-js": "0.3.2", "raw-body": "2.3.3", "grunt-cli": "1.4.3", "requirejs": "2.3.6", "testswarm": "1.1.2", "uglify-js": "3.4.7", "@babel/core": "7.3.3", "grunt-babel": "8.0.0", "grunt-karma": "4.0.2", "grunt-newer": "1.3.0", "karma-qunit": "4.1.2", "commitplease": "3.2.0", "grunt-eslint": "22.0.0", "grunt-npmcopy": "0.2.0", "grunt-jsonlint": "2.1.2", "load-grunt-tasks": "5.1.0", "grunt-git-authors": "3.2.0", "karma-ie-launcher": "1.0.0", "grunt-compare-size": "0.4.2", "grunt-contrib-watch": "1.1.0", "native-promise-only": "0.8.1", "strip-json-comments": "2.0.1", "eslint-config-jquery": "3.0.0", "grunt-contrib-uglify": "3.4.0", "karma-jsdom-launcher": "12.0.0", "promises-aplus-tests": "2.1.2", "karma-chrome-launcher": "3.1.1", "karma-firefox-launcher": "2.1.2", "karma-browserstack-launcher": "1.6.0", "@babel/plugin-transform-for-of": "7.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/jquery_3.6.4_1678289566333_0.9588044326783693", "host": "s3://npm-registry-packages"}}, "3.7.0": {"name": "j<PERSON>y", "version": "3.7.0", "keywords": ["j<PERSON>y", "javascript", "browser", "library"], "author": {"url": "https://github.com/jquery/jquery/blob/3.7.0/AUTHORS.txt", "name": "OpenJS Foundation and other contributors"}, "license": "MIT", "_id": "jquery@3.7.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}, {"name": "mgol", "email": "<EMAIL>"}, {"name": "openjsfoundation", "email": "<EMAIL>"}], "homepage": "https://jquery.com", "bugs": {"url": "https://github.com/jquery/jquery/issues"}, "dist": {"shasum": "fe2c01a05da500709006d8790fe21c8a39d75612", "tarball": "https://registry.npmjs.org/jquery/-/jquery-3.7.0.tgz", "fileCount": 125, "integrity": "sha512-umpJ0/k8X0MvD1ds0P9SfowREz2LenHsQaxSohMZ5OMNEU2r0tf8pdeEFTHMFxWVxKNyU9rTtK3CWzUCTKJUeQ==", "signatures": [{"sig": "MEUCIQDdk8JtX/1qsJJQBaHvWsfLB5OeMaxTZE3Zrog7nriSSQIgOu3+KTkLXgPtLCW1knsv4qp8fJps3gbdhR4rEyBCfrs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1245008}, "main": "dist/jquery.js", "husky": {"hooks": {"commit-msg": "commitplease .git/COMMIT_EDITMSG", "pre-commit": "grunt lint:newer qunit_fixture"}}, "title": "j<PERSON><PERSON><PERSON>", "gitHead": "b755b3e9ab895aed7f409ec48a1349f5b185f55f", "scripts": {"test": "npm run test:slim && npm run test:no-deprecated && npm run test:selector-native && grunt && grunt test:slow && grunt karma:main && grunt karma:amd", "build": "npm install && grunt", "start": "grunt watch", "jenkins": "npm run test:browserless", "test:amd": "grunt && grunt karma:amd", "test:slim": "grunt test:prepare && grunt custom:slim && grunt karma:main", "test:browser": "grunt && grunt karma:main", "test:browserless": "grunt && grunt test:slow", "test:no-deprecated": "grunt test:prepare && grunt custom:-deprecated && grunt karma:main", "test:selector-native": "grunt test:prepare && grunt custom:-selector && grunt karma:main"}, "_npmUser": {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jquery/jquery.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "JavaScript library for DOM operations", "directories": {}, "_nodeVersion": "18.16.0", "commitplease": {"nohook": true, "components": ["Docs", "Tests", "Build", "Support", "Release", "Core", "Ajax", "Attributes", "Callbacks", "CSS", "Data", "Deferred", "Deprecated", "Dimensions", "Effects", "Event", "Manipulation", "Offset", "Queue", "Selector", "Serialize", "Traversing", "Wrap"], "markerPattern": "^((clos|fix|resolv)(e[sd]|ing))|^(refs?)", "ticketPattern": "^((Closes|Fixes) ([a-zA-Z]{2,}-)[0-9]+)|^(Refs? [^#])"}, "_hasShrinkwrap": false, "devDependencies": {"q": "1.5.1", "grunt": "1.5.3", "husky": "4.2.5", "jsdom": "19.0.0", "karma": "6.4.1", "qunit": "2.9.2", "sinon": "2.3.7", "colors": "1.4.0", "core-js": "2.6.5", "gzip-js": "0.3.2", "raw-body": "2.3.3", "grunt-cli": "1.4.3", "requirejs": "2.3.6", "testswarm": "1.1.2", "uglify-js": "3.4.7", "@babel/core": "7.3.3", "grunt-babel": "8.0.0", "grunt-karma": "4.0.2", "grunt-newer": "1.3.0", "karma-qunit": "4.1.2", "commitplease": "3.2.0", "grunt-eslint": "22.0.0", "grunt-npmcopy": "0.2.0", "grunt-jsonlint": "2.1.2", "load-grunt-tasks": "5.1.0", "grunt-git-authors": "3.2.0", "karma-ie-launcher": "1.0.0", "playwright-webkit": "1.30.0", "grunt-compare-size": "0.4.2", "grunt-contrib-watch": "1.1.0", "native-promise-only": "0.8.1", "strip-json-comments": "2.0.1", "eslint-config-jquery": "3.0.0", "grunt-contrib-uglify": "3.4.0", "karma-jsdom-launcher": "12.0.0", "promises-aplus-tests": "2.1.2", "karma-chrome-launcher": "3.1.1", "karma-webkit-launcher": "2.1.0", "karma-firefox-launcher": "2.1.2", "karma-browserstack-launcher": "1.6.0", "@babel/plugin-transform-for-of": "7.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/jquery_3.7.0_1683829920338_0.8233095022409871", "host": "s3://npm-registry-packages"}}, "3.7.1": {"name": "j<PERSON>y", "version": "3.7.1", "keywords": ["j<PERSON>y", "javascript", "browser", "library"], "author": {"url": "https://github.com/jquery/jquery/blob/3.7.1/AUTHORS.txt", "name": "OpenJS Foundation and other contributors"}, "license": "MIT", "_id": "jquery@3.7.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}, {"name": "mgol", "email": "<EMAIL>"}, {"name": "openjsfoundation", "email": "<EMAIL>"}], "homepage": "https://jquery.com", "bugs": {"url": "https://github.com/jquery/jquery/issues"}, "dist": {"shasum": "083ef98927c9a6a74d05a6af02806566d16274de", "tarball": "https://registry.npmjs.org/jquery/-/jquery-3.7.1.tgz", "fileCount": 125, "integrity": "sha512-m4avr8yL8kmFN8psrbFFFmB/If14iN5o9nw/NgnnM+kybDJpRsAynV2BsfpTYrTRysYUdADVD7CkUUizgkpLfg==", "signatures": [{"sig": "MEUCIFGjEjlmHHrLubZ8IAx/rrhJLpnmUtQgNiuvA8kyDabbAiEApoI0yIbxC4ZzPP7VG8KdyA7+yak8r5dx+uHtZBWPekk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1246374}, "main": "dist/jquery.js", "husky": {"hooks": {"commit-msg": "commitplease .git/COMMIT_EDITMSG", "pre-commit": "grunt lint:newer qunit_fixture"}}, "title": "j<PERSON><PERSON><PERSON>", "gitHead": "fde1f76e2799dd877c176abde0ec836553246991", "scripts": {"test": "npm run test:browserless && npm run test:slim && npm run test:no-deprecated && npm run test:selector-native && grunt && grunt test:slow && grunt karma:main && grunt karma:amd", "build": "npm install && npm run build-all-variants", "start": "grunt watch", "jenkins": "npm run test:browserless", "test:amd": "grunt && grunt karma:amd", "test:slim": "grunt test:prepare && grunt custom:slim && grunt karma:main", "test:browser": "grunt && grunt karma:main", "test:browserless": "grunt && npm run test:node_smoke_tests && grunt test:slow", "build-all-variants": "grunt custom:slim --filename=jquery.slim.js && grunt custom", "test:no-deprecated": "grunt test:prepare && grunt custom:-deprecated && grunt karma:main", "test:selector-native": "grunt test:prepare && grunt custom:-selector && grunt karma:main", "test:node_smoke_tests": "npm run test:node_smoke_tests:full && npm run test:node_smoke_tests:slim", "test:node_smoke_tests:full": "grunt node_smoke_tests:./dist/jquery.js", "test:node_smoke_tests:slim": "grunt node_smoke_tests:./dist/jquery.slim.js"}, "_npmUser": {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jquery/jquery.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "JavaScript library for DOM operations", "directories": {}, "_nodeVersion": "18.17.1", "commitplease": {"nohook": true, "components": ["Docs", "Tests", "Build", "Support", "Release", "Core", "Ajax", "Attributes", "Callbacks", "CSS", "Data", "Deferred", "Deprecated", "Dimensions", "Effects", "Event", "Manipulation", "Offset", "Queue", "Selector", "Serialize", "Traversing", "Wrap"], "markerPattern": "^((clos|fix|resolv)(e[sd]|ing))|^(refs?)", "ticketPattern": "^((Closes|Fixes) ([a-zA-Z]{2,}-)[0-9]+)|^(Refs? [^#])"}, "_hasShrinkwrap": false, "devDependencies": {"q": "1.5.1", "grunt": "1.5.3", "husky": "4.2.5", "jsdom": "19.0.0", "karma": "6.4.1", "qunit": "2.9.2", "sinon": "2.3.7", "colors": "1.4.0", "core-js": "2.6.5", "gzip-js": "0.3.2", "raw-body": "2.3.3", "bootstrap": "5.3.0", "grunt-cli": "1.4.3", "requirejs": "2.3.6", "testswarm": "1.1.2", "uglify-js": "3.4.7", "@babel/core": "7.3.3", "grunt-babel": "8.0.0", "grunt-karma": "4.0.2", "grunt-newer": "1.3.0", "karma-qunit": "4.1.2", "commitplease": "3.2.0", "grunt-eslint": "22.0.0", "grunt-npmcopy": "0.2.0", "grunt-jsonlint": "2.1.2", "load-grunt-tasks": "5.1.0", "grunt-git-authors": "3.2.0", "karma-ie-launcher": "1.0.0", "playwright-webkit": "1.30.0", "grunt-compare-size": "0.4.2", "grunt-contrib-watch": "1.1.0", "native-promise-only": "0.8.1", "strip-json-comments": "2.0.1", "eslint-config-jquery": "3.0.0", "grunt-contrib-uglify": "3.4.0", "karma-jsdom-launcher": "12.0.0", "promises-aplus-tests": "2.1.2", "karma-chrome-launcher": "3.1.1", "karma-webkit-launcher": "2.1.0", "karma-firefox-launcher": "2.1.2", "karma-browserstack-launcher": "1.6.0", "@babel/plugin-transform-for-of": "7.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/jquery_3.7.1_1693229962753_0.08647899688700456", "host": "s3://npm-registry-packages"}}, "4.0.0-beta": {"name": "j<PERSON>y", "version": "4.0.0-beta", "keywords": ["j<PERSON>y", "javascript", "browser", "library"], "author": {"url": "https://github.com/jquery/jquery/blob/main/AUTHORS.txt", "name": "OpenJS Foundation and other contributors"}, "license": "MIT", "_id": "jquery@4.0.0-beta", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}, {"name": "mgol", "email": "<EMAIL>"}, {"name": "openjsfoundation", "email": "<EMAIL>"}], "homepage": "https://jquery.com", "bugs": {"url": "https://github.com/jquery/jquery/issues"}, "dist": {"shasum": "695479abe4e55bf4c2d261cf0d6535f3bd9d1e0c", "tarball": "https://registry.npmjs.org/jquery/-/jquery-4.0.0-beta.tgz", "fileCount": 157, "integrity": "sha512-F/LxAJ1KuoIfS5j6T5R57YceLJfXjwBl5hJ9za2itWyCy24YqU5R/DzRf76RrpeHfpJgQyqwnPbUQbjSfoEBcQ==", "signatures": [{"sig": "MEUCIG5C4h/EO2XNt23HUPCZ2kienAZ+4bitRxfpF6hkw/+vAiEAiRH3aohMqSzOLsagmnd9qvYZe5nOLglyzy8Be35sIok=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2015962}, "main": "dist/jquery.js", "type": "module", "title": "j<PERSON><PERSON><PERSON>", "exports": {".": {"node": {"import": "./dist-module/jquery.node-module-wrapper.js", "module": "./dist-module/jquery.module.js", "require": "./dist/jquery.js"}, "script": "./dist/jquery.min.js", "default": "./dist-module/jquery.module.min.js", "production": "./dist-module/jquery.module.min.js", "development": "./dist-module/jquery.module.js"}, "./slim": {"node": {"import": "./dist-module/jquery.node-module-wrapper.slim.js", "module": "./dist-module/jquery.slim.module.js", "require": "./dist/jquery.slim.js"}, "script": "./dist/jquery.slim.min.js", "default": "./dist-module/jquery.slim.module.min.js", "production": "./dist-module/jquery.slim.module.min.js", "development": "./dist-module/jquery.slim.module.js"}, "./factory": {"node": "./dist/jquery.factory.js", "default": "./dist-module/jquery.factory.module.js"}, "./src/*.js": "./src/*.js", "./factory-slim": {"node": "./dist/jquery.factory.slim.js", "default": "./dist-module/jquery.factory.slim.module.js"}}, "gitHead": "a31e9f375791afbda82e4813acad27e5a46568f9", "_npmUser": {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jquery/jquery.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "JavaScript library for DOM operations", "directories": {}, "_nodeVersion": "20.10.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jquery_4.0.0-beta_1707237780190_0.8075486470739359", "host": "s3://npm-registry-packages"}}, "4.0.0-beta.2": {"name": "j<PERSON>y", "version": "4.0.0-beta.2", "keywords": ["j<PERSON>y", "javascript", "browser", "library"], "author": {"url": "https://github.com/jquery/jquery/blob/4.0.0-beta.2/AUTHORS.txt", "name": "OpenJS Foundation and other contributors"}, "license": "MIT", "_id": "jquery@4.0.0-beta.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}, {"name": "mgol", "email": "<EMAIL>"}, {"name": "openjsfoundation", "email": "<EMAIL>"}], "homepage": "https://jquery.com", "bugs": {"url": "https://github.com/jquery/jquery/issues"}, "dist": {"shasum": "b1e92b6b72ec82bc154e5421186f543a72be5cba", "tarball": "https://registry.npmjs.org/jquery/-/jquery-4.0.0-beta.2.tgz", "fileCount": 183, "integrity": "sha512-2/cB7q2vtwltOGph8cNaNBRfVRdewrgdmYipXJ9ZYLQOPYjBKHH8+l5ZLVTFV7emKVIPXwDP3excXS0zUcx9kw==", "signatures": [{"sig": "MEUCIQDAO9PROY/fj1b+hpZ15qxcv0iNKkNgdfaz+hFfOwhs4QIgTxHMPxaHyB1i6r+pnEgW7CI4VSJRmqwjbr0pd43ZOgM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2948383}, "main": "dist/jquery.js", "type": "module", "title": "j<PERSON><PERSON><PERSON>", "exports": {".": {"node": {"import": "./dist-module/wrappers/jquery.node-module-wrapper.js", "default": "./dist/jquery.js"}, "import": "./dist-module/jquery.module.js", "module": {"import": "./dist-module/jquery.module.js", "default": "./dist/wrappers/jquery.bundler-require-wrapper.js"}, "default": "./dist/jquery.js"}, "./slim": {"node": {"import": "./dist-module/wrappers/jquery.node-module-wrapper.slim.js", "default": "./dist/jquery.slim.js"}, "import": "./dist-module/jquery.slim.module.js", "module": {"import": "./dist-module/jquery.slim.module.js", "default": "./dist/wrappers/jquery.bundler-require-wrapper.slim.js"}, "default": "./dist/jquery.slim.js"}, "./factory": {"node": "./dist/jquery.factory.js", "import": "./dist-module/jquery.factory.module.js", "module": "./dist-module/jquery.factory.module.js", "default": "./dist/jquery.factory.js"}, "./src/*.js": "./src/*.js", "./factory-slim": {"node": "./dist/jquery.factory.slim.js", "import": "./dist-module/jquery.factory.slim.module.js", "module": "./dist-module/jquery.factory.slim.module.js", "default": "./dist/jquery.factory.slim.js"}}, "gitHead": "3e612aeeb3821c657989e67b43c9b715f5cd32e2", "scripts": {"lint": "concurrently -r \"npm:lint:dev\" \"npm:lint:json\"", "test": "npm run build:all && npm run lint && npm run test:browserless && npm run test:browser && npm run test:esm && npm run test:slim && npm run test:no-deprecated && npm run test:selector-native", "build": "node ./build/command.js", "start": "node -e \"(async () => { const { buildDefaultFiles } = await import('./build/tasks/build.js'); buildDefaultFiles({ watch: true }) })()\"", "npmcopy": "node build/tasks/npmcopy.js", "prepare": "husky", "pretest": "npm run qunit-fixture && npm run babel:tests && npm run npmcopy", "release": "release-it", "lint:dev": "eslint --cache .", "test:esm": "npm run pretest && npm run build:main && npm run test:unit -- --esm -h", "build:all": "node -e \"(async () => { const { buildDefaultFiles } = await import('./build/tasks/build.js'); buildDefaultFiles() })()\"", "lint:json": "jsonlint --quiet package.json", "test:slim": "npm run pretest && npm run build -- --slim && npm run test:unit -- -h", "test:unit": "node test/runner/command.js", "build:main": "node -e \"(async () => { const { build } = await import('./build/tasks/build.js'); build() })()\"", "test:jsdom": "npm run pretest && npm run build:main && npm run test:unit -- -b jsdom -m basic", "babel:tests": "babel test/data/core/jquery-iterability-transpiled-es6.js --out-file test/data/core/jquery-iterability-transpiled.js", "build:clean": "rimraf --glob dist/*.{js,map} --glob dist-module/*.{js,map}", "release:cdn": "node build/release/cdn.js", "test:safari": "npm run pretest && npm run build:main && npm run test:unit -- -b safari", "test:server": "node test/runner/server.js", "release:dist": "node build/release/dist.js", "test:browser": "npm run pretest && npm run build:main && npm run test:unit -- -b chrome -b firefox -h", "test:firefox": "npm run pretest && npm run build:main && npm run test:unit -- -v -b firefox -h", "authors:check": "node -e \"(async () => { const { checkAuthors } = await import('./build/release/authors.js'); checkAuthors() })()\"", "qunit-fixture": "node build/tasks/qunit-fixture.js", "release:clean": "rimraf tmp --glob changelog.{md,html} contributors.html", "test:bundlers": "npm run pretest && npm run build:all && node test/bundler_smoke_tests/run-jsdom-tests.js", "authors:update": "node -e \"(async () => { const { updateAuthors } = await import('./build/release/authors.js'); updateAuthors() })()\"", "release:verify": "node build/release/verify.js", "test:browserless": "npm run pretest && npm run build:all && node test/bundler_smoke_tests/run-jsdom-tests.js && node build/tasks/node_smoke_tests.js && node build/tasks/promises_aplus_tests.js && npm run test:unit -- -b jsdom -m basic", "release:changelog": "node build/release/changelog.js", "test:no-deprecated": "npm run pretest && npm run build -- -e deprecated && npm run test:unit -- -h", "test:promises_aplus": "npm run build:main && node build/tasks/promises_aplus_tests.js", "test:selector-native": "npm run pretest && npm run build -- -e selector && npm run test:unit -- -h", "test:node_smoke_tests": "npm run pretest && npm run build:all && node build/tasks/node_smoke_tests.js"}, "_npmUser": {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jquery/jquery.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "JavaScript library for DOM operations", "directories": {}, "_nodeVersion": "20.15.1", "commitplease": {"nohook": true, "components": ["Docs", "Tests", "Build", "Support", "Release", "Core", "Ajax", "Attributes", "Callbacks", "CSS", "Data", "Deferred", "Deprecated", "Dimensions", "Effects", "Event", "Manipulation", "Offset", "Queue", "Selector", "Serialize", "Traversing", "Wrap"], "markerPattern": "^((clos|fix|resolv)(e[sd]|ing))|^(refs?)", "ticketPattern": "^((Closes|Fixes) ([a-zA-Z]{2,}-)[0-9]+)|^(Refs? [^#])"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"q": "1.5.1", "diff": "5.2.0", "chalk": "5.3.0", "husky": "9.0.11", "jsdom": "24.1.0", "qunit": "2.21.0", "sinon": "9.2.4", "yargs": "17.7.2", "colors": "1.4.0", "eslint": "8.57.0", "marked": "13.0.2", "rimraf": "6.0.0", "rollup": "4.18.1", "express": "4.19.2", "globals": "15.8.0", "webpack": "5.92.1", "archiver": "7.0.1", "raw-body": "2.5.2", "@swc/core": "1.6.13", "bootstrap": "5.3.3", "cross-env": "7.0.3", "exit-hook": "4.0.0", "requirejs": "2.3.6", "@babel/cli": "7.24.7", "multiparty": "4.2.3", "release-it": "17.5.0", "@babel/core": "7.24.7", "body-parser": "1.20.2", "commitplease": "3.2.0", "concurrently": "8.2.2", "core-js-bundle": "3.37.1", "@prantlf/jsonlint": "14.0.3", "browserstack-local": "1.5.5", "selenium-webdriver": "4.22.0", "native-promise-only": "0.8.1", "eslint-config-jquery": "3.0.2", "eslint-plugin-import": "2.29.1", "promises-aplus-tests": "2.1.2", "@rollup/plugin-commonjs": "26.0.1", "@types/selenium-webdriver": "4.1.24", "@rollup/plugin-node-resolve": "15.2.3", "@babel/plugin-transform-for-of": "7.24.7", "express-body-parser-error-handler": "1.0.7"}, "_npmOperationalInternal": {"tmp": "tmp/jquery_4.0.0-beta.2_1721223806864_0.38256379656702033", "host": "s3://npm-registry-packages"}}}, "time": {"created": "2011-03-19T07:19:56.392Z", "modified": "2025-02-14T23:06:31.564Z", "1.5.1": "2011-03-19T07:19:56.956Z", "1.6.2": "2011-07-06T16:13:21.519Z", "1.6.3": "2011-09-12T19:05:34.373Z", "1.7.2": "2012-05-01T11:14:09.090Z", "1.7.3": "2012-07-01T16:11:53.194Z", "1.8.2": "2012-11-11T22:43:58.910Z", "1.8.3": "2012-12-01T00:03:02.297Z", "2.1.0-beta2": "2013-11-15T16:34:48.859Z", "2.1.0-beta3": "2013-12-20T22:53:28.426Z", "1.11.0-beta3": "2014-01-06T19:57:22.387Z", "1.11.0-pre": "2014-01-16T20:38:42.912Z", "1.11.0-rc1": "2014-01-16T21:08:36.924Z", "2.1.0-rc1": "2014-01-17T22:47:16.391Z", "1.11.0": "2014-01-23T21:07:07.184Z", "2.1.0": "2014-01-23T21:12:47.772Z", "2.1.1-beta1": "2014-03-24T17:05:07.581Z", "1.11.1-beta1": "2014-03-24T17:25:14.236Z", "2.1.1-rc1": "2014-04-18T15:29:41.423Z", "1.11.1-rc1": "2014-04-18T15:40:23.931Z", "2.1.1-rc2": "2014-04-21T20:52:06.866Z", "1.11.1-rc2": "2014-04-21T21:12:52.817Z", "2.1.1": "2014-05-01T17:15:20.164Z", "1.11.1": "2014-05-01T18:05:32.863Z", "1.9.1": "2014-07-17T22:01:17.886Z", "2.1.2": "2014-12-17T14:14:28.184Z", "1.11.2": "2014-12-17T16:07:07.647Z", "2.1.3": "2014-12-18T15:18:38.205Z", "2.1.4": "2015-04-28T16:17:13.648Z", "1.11.3": "2015-04-28T16:22:06.378Z", "3.0.0-alpha1": "2015-07-13T19:26:37.913Z", "1.12.0": "2016-01-08T19:58:05.265Z", "2.2.0": "2016-01-08T20:03:43.280Z", "3.0.0-beta1": "2016-01-14T23:09:43.368Z", "1.12.1": "2016-02-22T19:08:05.212Z", "2.2.1": "2016-02-22T19:12:09.116Z", "1.12.2": "2016-03-17T17:45:59.810Z", "2.2.2": "2016-03-17T17:52:26.967Z", "1.12.3": "2016-04-05T19:21:37.716Z", "2.2.3": "2016-04-05T19:27:17.929Z", "1.12.4": "2016-05-20T17:19:07.375Z", "2.2.4": "2016-05-20T17:26:07.921Z", "3.0.0-rc1": "2016-05-20T19:13:48.387Z", "3.0.0": "2016-06-09T18:33:13.420Z", "3.1.0": "2016-07-07T21:46:05.554Z", "3.1.1": "2016-09-22T22:32:49.360Z", "3.2.0": "2017-03-16T21:30:56.342Z", "3.2.1": "2017-03-20T19:02:13.508Z", "3.3.0": "2018-01-19T19:03:52.616Z", "3.3.1": "2018-01-20T17:27:11.928Z", "3.4.0": "2019-04-10T19:53:06.430Z", "3.4.1": "2019-05-01T21:19:38.408Z", "3.5.0": "2020-04-10T15:41:47.872Z", "3.5.1": "2020-05-04T22:50:08.819Z", "3.6.0": "2021-03-02T17:12:11.948Z", "3.6.1": "2022-08-26T17:54:24.768Z", "3.6.2": "2022-12-13T14:58:41.701Z", "3.6.3": "2022-12-20T21:34:57.021Z", "3.6.4": "2023-03-08T15:32:46.615Z", "3.7.0": "2023-05-11T18:32:00.590Z", "3.7.1": "2023-08-28T13:39:23.083Z", "4.0.0-beta": "2024-02-06T16:43:00.505Z", "4.0.0-beta.2": "2024-07-17T13:43:27.110Z"}, "bugs": {"url": "https://github.com/jquery/jquery/issues"}, "author": {"url": "https://github.com/jquery/jquery/blob/3.7.1/AUTHORS.txt", "name": "OpenJS Foundation and other contributors"}, "license": "MIT", "homepage": "https://jquery.com", "keywords": ["j<PERSON>y", "javascript", "browser", "library"], "repository": {"url": "git+https://github.com/jquery/jquery.git", "type": "git"}, "description": "JavaScript library for DOM operations", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gibson042", "email": "<EMAIL>"}, {"name": "mgol", "email": "<EMAIL>"}, {"name": "openjsfoundation", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON>l", "email": "<EMAIL>"}], "readme": "", "readmeFilename": "", "users": {"cvc": true, "cedx": true, "d3ck": true, "dodo": true, "fadi": true, "j3kz": true, "juje": true, "kmck": true, "mgol": true, "mhat": true, "nazy": true, "sgpr": true, "xufz": true, "adamk": true, "anker": true, "bengi": true, "csbun": true, "cubit": true, "demod": true, "haeck": true, "hanwf": true, "igsys": true, "jkcpr": true, "jmm23": true, "jostw": true, "jream": true, "junos": true, "junya": true, "luiko": true, "modao": true, "oroce": true, "pfuri": true, "sky3r": true, "st3ko": true, "timdp": true, "tripu": true, "weerd": true, "wlmts": true, "yatsu": true, "yisen": true, "71emj1": true, "agoral": true, "alanho": true, "arttse": true, "bak258": true, "bebaps": true, "birkey": true, "bob123": true, "brettv": true, "bumsuk": true, "buzuli": true, "chiroc": true, "cypark": true, "daavve": true, "dainov": true, "damkat": true, "dmonti": true, "fbnlsr": true, "gluten": true, "heisem": true, "helcat": true, "ikeyan": true, "isis97": true, "itskdk": true, "jcdsr3": true, "kasumi": true, "kimhoe": true, "knoja4": true, "lendix": true, "leodel": true, "maddas": true, "memija": true, "merbst": true, "modest": true, "neelok": true, "ovgu12": true, "ovrmrw": true, "pandao": true, "pherum": true, "piotrd": true, "rmzxcm": true, "rwhogg": true, "stiker": true, "tcrowe": true, "temelm": true, "tpdnxu": true, "trotyl": true, "vsn4ik": true, "wkx101": true, "wtower": true, "xhxxhx": true, "xmwx38": true, "yangzw": true, "yuch4n": true, "ackuser": true, "adriasb": true, "asj1992": true, "asm2hex": true, "bcawrse": true, "broxmgs": true, "chengsu": true, "cueedee": true, "ddmitov": true, "dhampik": true, "eloscod": true, "ezakaze": true, "ezeikel": true, "fatelei": true, "fdeneux": true, "filipve": true, "frk1705": true, "ftornik": true, "funroll": true, "fxkraus": true, "ggviana": true, "hubojun": true, "hughker": true, "jesus81": true, "jlertle": true, "justsso": true, "kuravih": true, "laoshaw": true, "liushld": true, "mort3za": true, "mr_eaze": true, "nhkchan": true, "nysingh": true, "odahcam": true, "olian04": true, "oliverj": true, "orlenka": true, "ostgals": true, "ousmane": true, "paroczi": true, "parroit": true, "rpgreen": true, "rudchyk": true, "sahilsk": true, "seaseng": true, "shahyar": true, "takonyc": true, "teiming": true, "tooyond": true, "wjervis": true, "xfloops": true, "xgqfrms": true, "xulayen": true, "zenrumi": true, "adnanexy": true, "ageofsys": true, "ahvonenj": true, "alvin2ye": true, "ameer157": true, "ayad0net": true, "bendiken": true, "brentely": true, "bsurnida": true, "chenshao": true, "coalesce": true, "damianof": true, "dnarvaez": true, "dzhou777": true, "faraoman": true, "frankxin": true, "freebird": true, "hasanabb": true, "huiyifyj": true, "hypo9eal": true, "jackvial": true, "jgreeley": true, "johnshao": true, "krabello": true, "linkchef": true, "losssoul": true, "manxisuo": true, "maxwhite": true, "meligatt": true, "mhaidarh": true, "oka-hide": true, "onheiron": true, "pablopap": true, "pddivine": true, "pnevares": true, "potentii": true, "rhythm19": true, "sedalski": true, "sedmonds": true, "shelling": true, "sibawite": true, "tdmalone": true, "tektimmy": true, "tfentonz": true, "tmurngon": true, "tommyjs7": true, "veritasx": true, "vishwasc": true, "wkaifang": true, "abpeinado": true, "abuelwafa": true, "aldofunes": true, "aquafadas": true, "bcipriano": true, "cripstian": true, "dexterneo": true, "dondyabla": true, "dreamseer": true, "duchenerc": true, "edmondnow": true, "ehabovich": true, "emircanok": true, "fgribreau": true, "fps20only": true, "honingwon": true, "i-erokhin": true, "jakedalus": true, "janspicka": true, "jetbug123": true, "joaocunha": true, "joshbruce": true, "kevinagin": true, "kingfeast": true, "konfuzius": true, "kongol.ml": true, "landy2014": true, "leobalter": true, "luiscauro": true, "magemagic": true, "matiasm15": true, "merlyn333": true, "mikemimik": true, "mojaray2k": true, "natforyou": true, "noxracing": true, "onursumer": true, "redstrike": true, "rgraves90": true, "rogeriera": true, "rylan_yan": true, "shakakira": true, "sirkinder": true, "thisiskun": true, "vince-ann": true, "xanderiel": true, "zhuheyang": true, "adswebwork": true, "alexpearly": true, "axelrindle": true, "bjlxj_2008": true, "budiantoip": true, "cfleschhut": true, "chiaychang": true, "daniellink": true, "dericdavis": true, "hentai_mew": true, "ianpaschal": true, "ibourgeois": true, "inancgumus": true, "j717273419": true, "johnny_kim": true, "juangotama": true, "kazuyahama": true, "langri-sha": true, "liamebirge": true, "mareksagan": true, "midascreed": true, "morogasper": true, "pantyuhind": true, "pkhotpanya": true, "princetoad": true, "psycograph": true, "rocket0191": true, "seangenabe": true, "shuoshubao": true, "simplyianm": true, "softskills": true, "tiggem1993": true, "wambulance": true, "wearevilla": true, "wenhsiaoyi": true, "willbeeler": true, "wwag110465": true, "yesseecity": true, "adamdreszer": true, "adeelquadri": true, "adrianorosa": true, "akshymalvia": true, "alaeddine17": true, "arturmuller": true, "bogdanvlviv": true, "chenyanfeng": true, "cloudychris": true, "cycomachead": true, "davidnyhuis": true, "ericteng177": true, "flumpus-dev": true, "ghoulfriend": true, "hal9zillion": true, "joannerpena": true, "lukecarrier": true, "marcovossen": true, "mseminatore": true, "nitayneeman": true, "themadjoker": true, "vivekwisdom": true, "whiskeyjack": true, "wildmind-ch": true, "wm123450405": true, "alexandermac": true, "claytonnnnnn": true, "gilvannfilho": true, "goatandsheep": true, "ivan.marquez": true, "jamal-safwat": true, "josejaguirre": true, "matiasmarani": true, "matthewbauer": true, "mswanson1524": true, "nickeltobias": true, "oussoulessou": true, "paulkolesnyk": true, "rahsaanbasek": true, "raven3murphy": true, "ristostevcev": true, "santhoshbabu": true, "skinnybrit51": true, "thejohnsmith": true, "thomas.miele": true, "tobiasnickel": true, "vinnyfonseca": true, "wesleylhandy": true, "chinawolf_wyp": true, "davidbaiguini": true, "fabiomendonca": true, "fchienvuhoang": true, "gurpreetbirdi": true, "highlanderkev": true, "komarovsergey": true, "manojkhannakm": true, "miadzadfallah": true, "sharrondenice": true, "shen-weizhong": true, "uselesscarrot": true, "baraunaluciano": true, "beatwinthewave": true, "beth_rogers465": true, "dragonfire1119": true, "karlitowhoelse": true, "mahmoodramzani": true, "maycon_ribeiro": true, "mmunchandersen": true, "pocketfulofash": true, "shogun88888888": true, "szilard-nemeth": true, "victorcastelan": true, "andreasneumeier": true, "jeffb_incontact": true, "marcobiedermann": true, "andrew.oxenburgh": true, "giuseppe.morelli": true, "orellanajonathan": true, "yonathan-ashebir": true, "thesoftwarepeople": true, "zhangqingfeng1984": true, "davidjsalazarmoreno": true}}