<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PolyFlix - <PERSON><PERSON> thống rạp số 1 thế giới</title>
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <link rel="icon" type="image/png" href="<?php echo e(asset('logo/polyflix_title.png')); ?>">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600&display=swap" rel="stylesheet">
    <!-- Font Awesome 6 (miễn phí) -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <?php echo app('Illuminate\Foundation\Vite')('resources/js/client.js'); ?>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <?php echo $__env->yieldContent('styles'); ?>
</head>

<body>
    <div class="container">
        <?php if(session('success')): ?>
            <script>
                Swal.fire({
                    toast: true,
                    position: 'top-end',
                    icon: 'success',
                    title: '<?php echo e(session('success')); ?>',
                    background: '#10b981',
                    color: '#fff',
                    showCloseButton: true,
                    timer: 7000,
                    timerProgressBar: true,
                    showConfirmButton: false,
                    customClass: {
                        popup: 'custom-toast'
                    }
                });
            </script>
        <?php endif; ?>
        
        <div class="header">
            <div class="logo">
                <a href="<?php echo e(route('home')); ?>">
                    <img src="<?php echo e(asset('logo/CinematicPolyFlixLogo-removebg-preview-removebg-preview.png')); ?>"
                        alt="PolyFlix Logo">
                </a>
            </div>
            <div class="list-header">
                <div class="search">
                    <input type="text" name="search" placeholder="Tìm phim...">
                    <i class="fa-solid fa-magnifying-glass"></i>
                </div>

                <div class="login dropdown">
                    <i class="fa-solid fa-user"></i>

                    <?php if(auth()->guard()->check()): ?>
                        <div class="user-toggle" onclick="toggleUserDropdown()">
                            <span class="user-name"><?php echo e(Auth::user()->name); ?></span>
                            <i class="fa-solid fa-chevron-down"></i>
                        </div>
                        <div class="dropdown-menu" id="userDropdown">
                            <a href="<?php echo e(route('profile')); ?>"><i class="fa-solid fa-user"></i> Thông tin cá nhân</a>
                            <a href="<?php echo e(route('logout')); ?>"
                                onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                                <i class="fa-solid fa-arrow-right-from-bracket"></i> Đăng xuất
                            </a>
                            <form id="logout-form" action="<?php echo e(route('logout')); ?>" method="POST" style="display: none;">
                                <?php echo csrf_field(); ?>
                            </form>
                        </div>
                    <?php else: ?>
                        <a href="<?php echo e(route('login.form')); ?>"><span>Đăng nhập</span></a>
                    <?php endif; ?>
                </div>

            </div>

        </div>
    </div>

    <div class="nav-bar">
        <div class="container">
            <div class="nav1">
                <div class="list-nav">
                    <div class="rap">
                        <i class="fa-solid fa-location-dot"></i>
                        <p>Chọn rạp</p>
                    </div>
                    <div class="lich-chieu">
                        <i class="fa-solid fa-calendar-days"></i>
                        <p>Lịch chiếu</p>
                    </div>
                </div>
                <div class="nav2">
                    <ul>
                        <li>
                            <a href="<?php echo e(route('khuyen-mai.index')); ?>" style="text-decoration: none; color: inherit;">
                                Khuyến mãi
                            </a>
                        </li>
                        <li><a href="<?php echo e(route('client.bai-viet')); ?>">Góc điện ảnh</a></li>
                        <li>Liên hệ</li>
                        <li>Giới thiệu</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    
    <?php echo $__env->yieldContent('content'); ?>

    
    <div class="footer">
        <div class="footer1">
            <img src="<?php echo e(asset('logo/CinematicPolyFlixLogo-removebg-preview-removebg-preview.png')); ?>" width="150px"
                alt="">
            <p class="none">NO SEAT, NO CHILL</p>
            <div class="social">
                <i class="fa-brands fa-facebook"></i>
                <i class="fa-brands fa-tiktok"></i>
                <i class="fa-brands fa-instagram"></i>
            </div>
        </div>
        <div>
            <h3>TÀI KHOẢN</h3>
            <p>Đăng nhập</p>
            <p>Đăng ký</p>
        </div>
        <div>
            <h3>XEM PHIM</h3>
            <p>Phim đang chiếu</p>
            <p>Phim sắp chiếu</p>
            <p>Suất chiếu đặc biệt</p>
        </div>
        <div>
            <h3>POLYFLIX</h3>
            <p>Giới thiệu</p>
            <p>Liên hệ</p>
            <p>Tuyển dụng</p>
        </div>
        <div>
            <h3>HỆ THỐNG RẠP</h3>
            <p>Tất cả hệ thống rạp</p>
            <p>PolyFlix Long Biên - Hà Nội</p>
        </div>
    </div>
</body>

</html>

<?php echo $__env->yieldContent('scripts'); ?>
<?php /**PATH C:\laragon\www\PolyFlix\resources\views/layouts/client.blade.php ENDPATH**/ ?>